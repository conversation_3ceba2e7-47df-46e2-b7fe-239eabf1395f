<?php

namespace App\Providers;

use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;
use App\Listeners\LogSendingEmail;
use App\Listeners\UpdateEmailLogStatus;
use Illuminate\Mail\Events\MessageSending;
use Illuminate\Mail\Events\MessageSent;


class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],

        MessageSending::class => [
            LogSendingEmail::class,
            \App\Listeners\RedirectStagingEmail::class,
        ],

        \Illuminate\Mail\Events\MessageSent::class => [
            UpdateEmailLogStatus::class,
        ],
        // MessageSent::class => [
        //     LogSentEmail::class,
        // ],
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
        //
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     *
     * @return bool
     */
    public function shouldDiscoverEvents()
    {
        return false;
    }
}