
<?php $__env->startSection('css_custom'); ?>
    <link href="<?php echo e(asset2('/backend/assets/plugins/fileupload/css/fileupload.css')); ?>" rel="stylesheet" type="text/css" />
    <!-- INTERNAL Fancy File Upload css -->
    <link href="<?php echo e(asset2('/backend/assets/plugins/fancyuploder/fancy_fileupload.css')); ?>" rel="stylesheet" />
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>

    <!--Page header-->
    <div class="page-header d-xl-flex d-block">
        <div class="page-leftheader">
            <h4 class="page-title">Chỉnh sửa CTV</h4>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-body">
                    <div class=" tab-menu-heading p-0 bg-light">
                        <div class="tabs-menu1">
                            <!-- Tabs -->
                            <ul class="nav panel-tabs">
                                <li class="ml-4"><a href="#tab-create" class="<?php if(!session('action') || session('action') == 'edit' ): ?> active <?php endif; ?>"  data-toggle="tab">Thông tin</a></li>
                                <?php if(\App\Services\Admin\PermissionService::checkPermission('submit.cv-datatable')): ?>
                                <li class="ml-4"><a href="#referral-list"  class="" data-toggle="tab">Giới thiệu ứng viên</a></li>
                                <?php endif; ?>
                                <li class="ml-4"><a href="#tab-change-password"  class="<?php if(session('action') == 'changePassword' ): ?> active <?php endif; ?>" data-toggle="tab">Đổi mật khẩu</a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="panel-body tabs-menu-body">
                        <div class="tab-content">
                            <div class="tab-pane <?php if(!session('action') || session('action') == 'edit' ): ?> active <?php endif; ?>" id="tab-create">
                                <form action="<?php echo e(route('collaborator.update',['collaborator'=>$data->id])); ?>" method="POST"  enctype="multipart/form-data" class="sbm_form_s">
                                    <?php echo csrf_field(); ?>
                                    <?php echo e(method_field('put')); ?>

                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="text-center">
                                                    <div class="upload-center">
                                                        <div class="max-with-upload">
                                                            <input type="file" class="dropify " name="avatar" data-default-file="<?php echo e($data->avatar_url); ?>" data-height="180"  />
                                                        </div>
                                                    </div>
                                                    <?php if($errors->has('avatar')): ?>
                                                        <div class="text-danger"> <?php echo e($errors->first('avatar')); ?> </div>
                                                    <?php endif; ?>
                                                    <label class="form-label">Ảnh đại diện</label>
                                                </div>
                                                <div class="form-group">
                                                    <label class="form-label">Họ tên <span class="text-red">*</span></label>
                                                    <input class="form-control <?php if($errors->has('name')): ?> is-invalid <?php endif; ?>" name="name" value="<?php echo e(old('name',$data->name)); ?>">
                                                    <?php if($errors->has('name')): ?>
                                                        <span class="text-danger"> <?php echo e($errors->first('name')); ?> </span>
                                                    <?php endif; ?>
                                                </div>

                                                <div class="form-group">
                                                    <label class="form-label">Email <span class="text-red">*</span></label>
                                                    <input class="form-control <?php if($errors->has('email')): ?> is-invalid <?php endif; ?>" name="email" value="<?php echo e(old('email',$data->email)); ?>">
                                                    <?php if($errors->has('email')): ?>
                                                        <span class="text-danger"> <?php echo e($errors->first('email')); ?> </span>
                                                    <?php endif; ?>
                                                </div>

                                                <div class="form-group">
                                                    <label class="form-label">Ngày sinh <span class="text-red">*</span></label>
                                                    <input class="form-control fc-datepicker <?php if($errors->has('birthday')): ?> is-invalid <?php endif; ?>" value="<?php echo e(old('birthday',$data->birthday_value)); ?>" name="birthday" placeholder="MM/DD/YYYY" type="text" autocomplete="off">
                                                    <?php if($errors->has('birthday')): ?>
                                                        <span class="text-danger"> <?php echo e($errors->first('birthday')); ?> </span>
                                                    <?php endif; ?>
                                                </div>

                                                <div class="form-group">
                                                    <label class="form-label">Trạng thái hoạt động  <span class="text-red">*</span></label>
                                                    <label class="custom-switch">
                                                        <?php if(old('flg_status') == 1): ?>
                                                            <input type="checkbox" <?php if(old('is_active') == 1): ?> checked <?php endif; ?> value="1" name="is_active" class="custom-switch-input">
                                                        <?php else: ?>
                                                            <input type="checkbox" <?php if($data->is_active == 1): ?> checked <?php endif; ?> value="1" name="is_active" class="custom-switch-input">
                                                        <?php endif; ?>
                                                        <span class="custom-switch-indicator custom-switch-indicator-xl"></span>
                                                        <span class="custom-switch-description mr-2" id="status_active">Active</span>
                                                    </label>
                                                </div>

                                                <div class="form-group">
                                                    <label class="form-label">Số CCCD</label>
                                                    <input class="form-control <?php if($errors->has('cccd_number')): ?> is-invalid <?php endif; ?>" name="cccd_number" value="<?php echo e(old('cccd_number', isset($data->userInfo) ? $data->userInfo->cccd_number : '')); ?>" placeholder="Nhập số căn cước công dân">
                                                    <?php if($errors->has('cccd_number')): ?>
                                                        <span class="text-danger"> <?php echo e($errors->first('cccd_number')); ?> </span>
                                                    <?php endif; ?>
                                                </div>

                                                <div class="form-group">
                                                    <label class="form-label">Ảnh mặt trước CCCD</label>
                                                    <div class="upload-center">
                                                        <div class="max-with-upload">
                                                            <input type="file" class="dropify" name="cccd_front_image"
                                                                   data-default-file="<?php echo e(isset($data->userInfo) && $data->userInfo->cccd_front_image ? gen_url_file_s3($data->userInfo->cccd_front_image, '', false) : ''); ?>"
                                                                   data-height="180" />
                                                        </div>
                                                    </div>
                                                    <?php if(isset($data->userInfo) && $data->userInfo->cccd_front_image): ?>
                                                        <div class="mt-2 text-center">
                                                            <img src="<?php echo e(gen_url_file_s3($data->userInfo->cccd_front_image, '', false)); ?>"
                                                                 alt="CCCD Front"
                                                                 style="max-width: 200px; max-height: 150px; border: 1px solid #ddd; border-radius: 4px;"
                                                                 class="img-thumbnail">
                                                            <div class="mt-2">
                                                                <a href="<?php echo e(gen_url_file_s3($data->userInfo->cccd_front_image, '', false)); ?>"
                                                                   target="_blank"
                                                                   class="btn btn-sm btn-info">
                                                                    <i class="fa fa-download"></i> Tải ảnh
                                                                </a>
                                                            </div>
                                                        </div>
                                                    <?php endif; ?>
                                                    <?php if($errors->has('cccd_front_image')): ?>
                                                        <div class="text-danger"> <?php echo e($errors->first('cccd_front_image')); ?> </div>
                                                    <?php endif; ?>
                                                </div>

                                                <div class="form-group">
                                                    <label class="form-label">Ảnh mặt sau CCCD</label>
                                                    <div class="upload-center">
                                                        <div class="max-with-upload">
                                                            <input type="file" class="dropify" name="cccd_back_image"
                                                                   data-default-file="<?php echo e(isset($data->userInfo) && $data->userInfo->cccd_back_image ? gen_url_file_s3($data->userInfo->cccd_back_image, '', false) : ''); ?>"
                                                                   data-height="180" />
                                                        </div>
                                                    </div>
                                                    <?php if(isset($data->userInfo) && $data->userInfo->cccd_back_image): ?>
                                                        <div class="mt-2 text-center">
                                                            <img src="<?php echo e(gen_url_file_s3($data->userInfo->cccd_back_image, '', false)); ?>"
                                                                 alt="CCCD Back"
                                                                 style="max-width: 200px; max-height: 150px; border: 1px solid #ddd; border-radius: 4px;"
                                                                 class="img-thumbnail">
                                                            <div class="mt-2">
                                                                <a href="<?php echo e(gen_url_file_s3($data->userInfo->cccd_back_image, '', false)); ?>"
                                                                   target="_blank"
                                                                   class="btn btn-sm btn-info">
                                                                    <i class="fa fa-download"></i> Tải ảnh
                                                                </a>
                                                            </div>
                                                        </div>
                                                    <?php endif; ?>
                                                    <?php if($errors->has('cccd_back_image')): ?>
                                                        <div class="text-danger"> <?php echo e($errors->first('cccd_back_image')); ?> </div>
                                                    <?php endif; ?>
                                                </div>

                                            </div>
                                            <div class="col-md-6">
                                                <h3 class="card-title text-center">Thông tin tài khoản ngân hàng</h3>
                                                <div class="table-responsive mb-5">
                                                    <table class="table card-table table-vcenter text-nowrap mb-0">
                                                        <tbody>
                                                        <tr>
                                                            <th scope="row" class="w-25 text-right">Tên chủ tài khoản:</th>
                                                            <td><?php echo e(isset($data->userInfo)?$data->userInfo->bank_account:''); ?></td>
                                                        </tr>
                                                        <tr>
                                                            <th scope="row" class="w-25 text-right">Số tài khoản:</th>
                                                            <td><?php echo e(isset($data->userInfo)?$data->userInfo->bank_account_number:''); ?></td>
                                                        </tr>
                                                        <tr>
                                                            <th scope="row" class="w-25 text-right">Tên ngân hàng:</th>
                                                            <td><?php echo e(isset($data->userInfo)?$data->userInfo->bank_name:''); ?></td>
                                                        </tr>
                                                        <tr>
                                                            <th scope="row" class="w-25 text-right">Tên chi nhánh:</th>
                                                            <td><?php echo e(isset($data->userInfo)?$data->userInfo->bank_branch:''); ?></td>
                                                        </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                                <h3 class="card-title text-center">Thông tin khác</h3>
                                                <div class="table-responsive">
                                                    <table class="table card-table table-vcenter text-nowrap mb-0">
                                                        <tbody>
                                                        <tr>
                                                            <th scope="row" class="w-25 text-right">Provider:</th>
                                                            <td><?php echo e($data->provider); ?></td>
                                                        </tr>
                                                        <tr>
                                                            <th scope="row" class="w-25 text-right">Referral code:</th>
                                                            <td><?php echo e($data->referral_code); ?></td>
                                                        </tr>
                                                        <tr>
                                                            <th scope="row" class="w-25 text-right">Verify:</th>
                                                            <td><?php echo e(isset($data->email_verified_at)?'Đã xác thực':'Chưa xác thực'); ?></td>
                                                        </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-footer text-right">
                                        <input type="hidden" class="flg_status" name="flg_status" value="<?php echo e(old('flg_status')); ?>"/>
                                        <a href="<?php echo e(route('collaborator.index')); ?>" class="btn btn-danger btn-lg">Close</a>
                                        <button class="btn btn-success btn-lg sbm_form" type="button">Submit</button>
                                    </div>
                                </form>
                            </div>
                            <?php if(\App\Services\Admin\PermissionService::checkPermission('submit.cv-datatable')): ?>
                            <div class="tab-pane " id="referral-list">
                                <div class="row">
                                    <div class="col-xl-12 col-md-12 col-lg-12">
                                        <?php echo e($datatable->render()); ?>

                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                            <?php if(\App\Services\Admin\PermissionService::checkPermission('collaborator.change-password')): ?>
                            <div class="tab-pane <?php if(session('action') == 'changePassword' ): ?> active <?php endif; ?>" id="tab-change-password">
                                <form action="<?php echo e(route('collaborator.change-password',['collaborator'=>$data->id])); ?>" method="POST">
                                    <?php echo csrf_field(); ?>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-3"></div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label class="form-label">Mật khẩu mới <span class="text-red">*</span></label>
                                                    <input type="password" class="form-control <?php if($errors->has('password')): ?> is-invalid <?php endif; ?>" name="password" value="">
                                                    <?php if($errors->has('password')): ?>
                                                        <span class="text-danger"> <?php echo e($errors->first('password')); ?> </span>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="form-group">
                                                    <label class="form-label">Xác nhận mật khẩu mới <span class="text-red">*</span></label>
                                                    <input type="password" class="form-control <?php if($errors->has('password_confirm')): ?> is-invalid <?php endif; ?>" name="password_confirm" value="">
                                                    <?php if($errors->has('password_confirm')): ?>
                                                        <span class="text-danger"> <?php echo e($errors->first('password_confirm')); ?> </span>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="form-group text-center">
                                                    <a href="<?php echo e(route('collaborator.index')); ?>" class="btn btn-danger btn-lg">Close</a>
                                                    <button class="btn btn-success btn-lg">Submit</button>
                                                </div>
                                            </div>
                                            <div class="col-md-3"></div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
    <script src="<?php echo e(asset2('backend/assets/plugins/fileupload/js/dropify.js')); ?>"></script>
    <script src="<?php echo e(asset2('backend/assets/js/filupload.js')); ?>"></script>
    <script>
        $(document).ready(function () {
            $('[name="is_active"]').prop("checked") ? $('#status_active').html('Active') : $('#status_active').html('Inactive');
            $('[name="is_active"]').change(function (){
                if($(this).prop("checked")){
                    $('#status_active').html('Active');
                }else{
                    $('#status_active').html('Inactive');
                }
            });

            $(".fc-datepicker").datepicker({
                showOtherMonths: !0,
                selectOtherMonths: !0,
                dateFormat: 'dd/mm/yy'
            })

            $(document).on('click', '.ti-import', function(){
                let url = $(this).data('value');
                let route = '<?php echo e(route('download-file')); ?>' + '?url=' + url;

                window.open(route, '_blank');
            });

            $(document).on('click', '.fa-eye', function(){
                let url = $(this).data('value');
                window.open(url, '_blank');
            });


            $(document).on('click', '.sbm_form', function(){
                $('.flg_status').val('1');
                $('.sbm_form_s').get(0).submit();
            });
        })
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Projects\HRI\RecLand\resources\views/admin/pages/collaborator/edit.blade.php ENDPATH**/ ?>