{"__meta": {"id": "01K1SBJBH5BVA9CHDTDPHTWBD0", "datetime": "2025-08-04 08:36:35", "utime": **********.365733, "method": "POST", "uri": "/admin/employers/search?", "ip": "127.0.0.1"}, "php": {"version": "8.1.6", "interface": "apache2handler"}, "messages": {"count": 70, "messages": [{"message": "[08:36:34] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `users` where `type` = 'employer'\\n-- \",\n    \"Time:\": 29.06\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.551744, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:34] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `users` where `type` = 'employer' and month(`created_at`) = '08' and year(`created_at`) = '2025'\\n-- \",\n    \"Time:\": 3.15\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.556503, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:34] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `type` = 'employer' order by `id` desc limit 25\\n-- \",\n    \"Time:\": 1.99\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.589306, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:34] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as total_rows from (select `users`.`id` from `users`) as `users_aggregator`\\n-- \",\n    \"Time:\": 2\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.627306, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:34] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as total_rows from (select `users`.`id` from `users` where `type` = 'employer') as `users_aggregator`\\n-- \",\n    \"Time:\": 2.43\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.631358, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:34] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `wallets` where `wallets`.`user_id` = '7480' and `wallets`.`user_id` is not null limit 1\\n-- \",\n    \"Time:\": 3.77\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.892633, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:34] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `job` where `job`.`employer_id` = '7480' and `job`.`employer_id` is not null\\n-- \",\n    \"Time:\": 4.15\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.915422, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:34] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `wallets` where `wallets`.`user_id` = '7474' and `wallets`.`user_id` is not null limit 1\\n-- \",\n    \"Time:\": 5.59\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.982359, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:34] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `job` where `job`.`employer_id` = '7474' and `job`.`employer_id` is not null\\n-- \",\n    \"Time:\": 7.92\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.991755, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `wallets` where `wallets`.`user_id` = '7473' and `wallets`.`user_id` is not null limit 1\\n-- \",\n    \"Time:\": 4.34\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.002505, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `job` where `job`.`employer_id` = '7473' and `job`.`employer_id` is not null\\n-- \",\n    \"Time:\": 3.89\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.007875, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `wallets` where `wallets`.`user_id` = '7457' and `wallets`.`user_id` is not null limit 1\\n-- \",\n    \"Time:\": 2.38\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.016558, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `job` where `job`.`employer_id` = '7457' and `job`.`employer_id` is not null\\n-- \",\n    \"Time:\": 3.95\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.021938, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `companies` where `companies`.`id` = '696' and `companies`.`id` is not null limit 1\\n-- \",\n    \"Time:\": 0.38\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.027074, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `wallets` where `wallets`.`user_id` = '7456' and `wallets`.`user_id` is not null limit 1\\n-- \",\n    \"Time:\": 2.33\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.032479, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `job` where `job`.`employer_id` = '7456' and `job`.`employer_id` is not null\\n-- \",\n    \"Time:\": 3.86\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.037755, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `wallets` where `wallets`.`user_id` = '7447' and `wallets`.`user_id` is not null limit 1\\n-- \",\n    \"Time:\": 2.55\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.046643, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `job` where `job`.`employer_id` = '7447' and `job`.`employer_id` is not null\\n-- \",\n    \"Time:\": 3.82\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.051876, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `wallets` where `wallets`.`user_id` = '7424' and `wallets`.`user_id` is not null limit 1\\n-- \",\n    \"Time:\": 5.44\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.063622, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `job` where `job`.`employer_id` = '7424' and `job`.`employer_id` is not null\\n-- \",\n    \"Time:\": 3.82\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.068922, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `wallets` where `wallets`.`user_id` = '7419' and `wallets`.`user_id` is not null limit 1\\n-- \",\n    \"Time:\": 2.56\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.077824, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `job` where `job`.`employer_id` = '7419' and `job`.`employer_id` is not null\\n-- \",\n    \"Time:\": 3.76\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.083016, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `companies` where `companies`.`id` = '695' and `companies`.`id` is not null limit 1\\n-- \",\n    \"Time:\": 0.38\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.087778, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `wallets` where `wallets`.`user_id` = '7415' and `wallets`.`user_id` is not null limit 1\\n-- \",\n    \"Time:\": 5.34\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.096222, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `job` where `job`.`employer_id` = '7415' and `job`.`employer_id` is not null\\n-- \",\n    \"Time:\": 8.63\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.106446, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `companies` where `companies`.`id` = '694' and `companies`.`id` is not null limit 1\\n-- \",\n    \"Time:\": 0.73\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.116998, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `wallets` where `wallets`.`user_id` = '7412' and `wallets`.`user_id` is not null limit 1\\n-- \",\n    \"Time:\": 2.36\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.124936, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `job` where `job`.`employer_id` = '7412' and `job`.`employer_id` is not null\\n-- \",\n    \"Time:\": 3.73\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.13005, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `wallets` where `wallets`.`user_id` = '7410' and `wallets`.`user_id` is not null limit 1\\n-- \",\n    \"Time:\": 2.47\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.138853, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `job` where `job`.`employer_id` = '7410' and `job`.`employer_id` is not null\\n-- \",\n    \"Time:\": 3.45\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.143733, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `wallets` where `wallets`.`user_id` = '7402' and `wallets`.`user_id` is not null limit 1\\n-- \",\n    \"Time:\": 2.45\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.152584, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `job` where `job`.`employer_id` = '7402' and `job`.`employer_id` is not null\\n-- \",\n    \"Time:\": 3.43\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.157462, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `companies` where `companies`.`id` = '693' and `companies`.`id` is not null limit 1\\n-- \",\n    \"Time:\": 0.39\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.162157, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `wallets` where `wallets`.`user_id` = '7372' and `wallets`.`user_id` is not null limit 1\\n-- \",\n    \"Time:\": 2.38\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.167605, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `job` where `job`.`employer_id` = '7372' and `job`.`employer_id` is not null\\n-- \",\n    \"Time:\": 3.53\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.172551, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `wallets` where `wallets`.`user_id` = '7350' and `wallets`.`user_id` is not null limit 1\\n-- \",\n    \"Time:\": 2.66\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.181448, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `job` where `job`.`employer_id` = '7350' and `job`.`employer_id` is not null\\n-- \",\n    \"Time:\": 3.49\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.186347, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `companies` where `companies`.`id` = '692' and `companies`.`id` is not null limit 1\\n-- \",\n    \"Time:\": 0.37\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.190894, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `wallets` where `wallets`.`user_id` = '6644' and `wallets`.`user_id` is not null limit 1\\n-- \",\n    \"Time:\": 2.45\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.196397, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `job` where `job`.`employer_id` = '6644' and `job`.`employer_id` is not null\\n-- \",\n    \"Time:\": 3.31\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.201045, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `companies` where `companies`.`id` = '691' and `companies`.`id` is not null limit 1\\n-- \",\n    \"Time:\": 0.36\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.205607, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `wallets` where `wallets`.`user_id` = '6643' and `wallets`.`user_id` is not null limit 1\\n-- \",\n    \"Time:\": 2.35\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.21104, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `job` where `job`.`employer_id` = '6643' and `job`.`employer_id` is not null\\n-- \",\n    \"Time:\": 3.25\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.215691, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `companies` where `companies`.`id` = '690' and `companies`.`id` is not null limit 1\\n-- \",\n    \"Time:\": 0.33\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.220212, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `wallets` where `wallets`.`user_id` = '6642' and `wallets`.`user_id` is not null limit 1\\n-- \",\n    \"Time:\": 2.32\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.225597, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `job` where `job`.`employer_id` = '6642' and `job`.`employer_id` is not null\\n-- \",\n    \"Time:\": 3.26\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.230233, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `companies` where `companies`.`id` = '689' and `companies`.`id` is not null limit 1\\n-- \",\n    \"Time:\": 0.33\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.234779, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `wallets` where `wallets`.`user_id` = '6641' and `wallets`.`user_id` is not null limit 1\\n-- \",\n    \"Time:\": 2.31\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.240146, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `job` where `job`.`employer_id` = '6641' and `job`.`employer_id` is not null\\n-- \",\n    \"Time:\": 3.3\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.244795, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `companies` where `companies`.`id` = '688' and `companies`.`id` is not null limit 1\\n-- \",\n    \"Time:\": 0.34\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.249361, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `wallets` where `wallets`.`user_id` = '6640' and `wallets`.`user_id` is not null limit 1\\n-- \",\n    \"Time:\": 2.35\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.254747, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `job` where `job`.`employer_id` = '6640' and `job`.`employer_id` is not null\\n-- \",\n    \"Time:\": 3.33\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.259406, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `companies` where `companies`.`id` = '687' and `companies`.`id` is not null limit 1\\n-- \",\n    \"Time:\": 0.36\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.264032, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `wallets` where `wallets`.`user_id` = '6639' and `wallets`.`user_id` is not null limit 1\\n-- \",\n    \"Time:\": 2.39\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.269459, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `job` where `job`.`employer_id` = '6639' and `job`.`employer_id` is not null\\n-- \",\n    \"Time:\": 7.21\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.278089, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `companies` where `companies`.`id` = '686' and `companies`.`id` is not null limit 1\\n-- \",\n    \"Time:\": 0.35\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.282701, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `wallets` where `wallets`.`user_id` = '6638' and `wallets`.`user_id` is not null limit 1\\n-- \",\n    \"Time:\": 5.37\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.291152, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `job` where `job`.`employer_id` = '6638' and `job`.`employer_id` is not null\\n-- \",\n    \"Time:\": 3.58\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.296074, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `companies` where `companies`.`id` = '685' and `companies`.`id` is not null limit 1\\n-- \",\n    \"Time:\": 0.34\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.300681, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `wallets` where `wallets`.`user_id` = '6637' and `wallets`.`user_id` is not null limit 1\\n-- \",\n    \"Time:\": 2.39\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.306079, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `job` where `job`.`employer_id` = '6637' and `job`.`employer_id` is not null\\n-- \",\n    \"Time:\": 3.55\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.310976, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `companies` where `companies`.`id` = '684' and `companies`.`id` is not null limit 1\\n-- \",\n    \"Time:\": 0.34\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.315597, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `wallets` where `wallets`.`user_id` = '6636' and `wallets`.`user_id` is not null limit 1\\n-- \",\n    \"Time:\": 2.35\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.321005, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `job` where `job`.`employer_id` = '6636' and `job`.`employer_id` is not null\\n-- \",\n    \"Time:\": 3.52\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.326019, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `companies` where `companies`.`id` = '683' and `companies`.`id` is not null limit 1\\n-- \",\n    \"Time:\": 0.35\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.333348, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `wallets` where `wallets`.`user_id` = '6635' and `wallets`.`user_id` is not null limit 1\\n-- \",\n    \"Time:\": 2.31\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.338713, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `job` where `job`.`employer_id` = '6635' and `job`.`employer_id` is not null\\n-- \",\n    \"Time:\": 3.43\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.343472, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `companies` where `companies`.`id` = '682' and `companies`.`id` is not null limit 1\\n-- \",\n    \"Time:\": 0.34\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.348059, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `wallets` where `wallets`.`user_id` = '6634' and `wallets`.`user_id` is not null limit 1\\n-- \",\n    \"Time:\": 2.31\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.353393, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `job` where `job`.`employer_id` = '6634' and `job`.`employer_id` is not null\\n-- \",\n    \"Time:\": 3.41\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.358139, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 3, "start": **********.107227, "end": **********.36584, "duration": 1.258612871170044, "duration_str": "1.26s", "measures": [{"label": "Booting", "start": **********.107227, "relative_start": 0, "end": **********.460338, "relative_end": **********.460338, "duration": 0.*****************, "duration_str": "353ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.460349, "relative_start": 0.****************, "end": **********.365842, "relative_end": 2.1457672119140625e-06, "duration": 0.****************, "duration_str": "905ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.471724, "relative_start": 0.*****************, "end": **********.474896, "relative_end": **********.474896, "duration": 0.0031719207763671875, "duration_str": "3.17ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 425, "nb_templates": 425, "templates": [{"name": "25x crud::columns.number", "param_count": null, "params": [], "start": **********.659029, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/number.blade.phpcrud::columns.number", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Fcrud%2Fcolumns%2Fnumber.blade.php&line=1", "ajax": false, "filename": "number.blade.php", "line": "?"}, "render_count": 25, "name_original": "crud::columns.number"}, {"name": "75x crud::columns.custom_html", "param_count": null, "params": [], "start": **********.687146, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.phpcrud::columns.custom_html", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Fcrud%2Fcolumns%2Fcustom_html.blade.php&line=1", "ajax": false, "filename": "custom_html.blade.php", "line": "?"}, "render_count": 75, "name_original": "crud::columns.custom_html"}, {"name": "25x crud::columns.email", "param_count": null, "params": [], "start": **********.715826, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/email.blade.phpcrud::columns.email", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Fcrud%2Fcolumns%2Femail.blade.php&line=1", "ajax": false, "filename": "email.blade.php", "line": "?"}, "render_count": 25, "name_original": "crud::columns.email"}, {"name": "75x crud::columns.inc.wrapper_start", "param_count": null, "params": [], "start": **********.745095, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/inc/wrapper_start.blade.phpcrud::columns.inc.wrapper_start", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Fcrud%2Fcolumns%2Finc%2Fwrapper_start.blade.php&line=1", "ajax": false, "filename": "wrapper_start.blade.php", "line": "?"}, "render_count": 75, "name_original": "crud::columns.inc.wrapper_start"}, {"name": "75x crud::columns.inc.wrapper_end", "param_count": null, "params": [], "start": **********.780789, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/inc/wrapper_end.blade.phpcrud::columns.inc.wrapper_end", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Fcrud%2Fcolumns%2Finc%2Fwrapper_end.blade.php&line=1", "ajax": false, "filename": "wrapper_end.blade.php", "line": "?"}, "render_count": 75, "name_original": "crud::columns.inc.wrapper_end"}, {"name": "25x crud::columns.text", "param_count": null, "params": [], "start": **********.804994, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/text.blade.phpcrud::columns.text", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Fcrud%2Fcolumns%2Ftext.blade.php&line=1", "ajax": false, "filename": "text.blade.php", "line": "?"}, "render_count": 25, "name_original": "crud::columns.text"}, {"name": "50x crud::columns.boolean", "param_count": null, "params": [], "start": **********.833943, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/boolean.blade.phpcrud::columns.boolean", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Fcrud%2Fcolumns%2Fboolean.blade.php&line=1", "ajax": false, "filename": "boolean.blade.php", "line": "?"}, "render_count": 50, "name_original": "crud::columns.boolean"}, {"name": "25x crud::columns.datetime", "param_count": null, "params": [], "start": **********.916506, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/datetime.blade.phpcrud::columns.datetime", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Fcrud%2Fcolumns%2Fdatetime.blade.php&line=1", "ajax": false, "filename": "datetime.blade.php", "line": "?"}, "render_count": 25, "name_original": "crud::columns.datetime"}, {"name": "25x crud::inc.button_stack", "param_count": null, "params": [], "start": **********.955138, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/inc/button_stack.blade.phpcrud::inc.button_stack", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Fcrud%2Finc%2Fbutton_stack.blade.php&line=1", "ajax": false, "filename": "button_stack.blade.php", "line": "?"}, "render_count": 25, "name_original": "crud::inc.button_stack"}, {"name": "25x crud::buttons.employer.custom_edit", "param_count": null, "params": [], "start": **********.956148, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/buttons/employer/custom_edit.blade.phpcrud::buttons.employer.custom_edit", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Fcrud%2Fbuttons%2Femployer%2Fcustom_edit.blade.php&line=1", "ajax": false, "filename": "custom_edit.blade.php", "line": "?"}, "render_count": 25, "name_original": "crud::buttons.employer.custom_edit"}]}, "route": {"uri": "POST admin/employers/search", "middleware": "web, admin, Closure", "as": "employers.search", "operation": "list", "controller": "App\\Http\\Controllers\\Admin\\EmployerCrudController@search<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FHttp%2FControllers%2FOperations%2FListOperation.php&line=70\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "admin", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FHttp%2FControllers%2FOperations%2FListOperation.php&line=70\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/backpack/crud/src/app/Http/Controllers/Operations/ListOperation.php:70-111</a>"}, "queries": {"count": 71, "nb_statements": 71, "nb_visible_statements": 71, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.24466000000000002, "accumulated_duration_str": "245ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select count(*) as aggregate from `users` where `type` = 'employer'", "type": "query", "params": [], "bindings": ["employer"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 612}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 82}, {"index": 17, "namespace": null, "name": "vendor/backpack/crud/src/app/Http/Controllers/CrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Http\\Controllers\\CrudController.php", "line": 121}, {"index": 18, "namespace": null, "name": "vendor/backpack/crud/src/app/Http/Controllers/CrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Http\\Controllers\\CrudController.php", "line": 42}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 162}], "start": **********.5229201, "duration": 0.02906, "duration_str": "29.06ms", "memory": 0, "memory_str": null, "filename": "EmployerCrudController.php:612", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 612}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FEmployerCrudController.php&line=612", "ajax": false, "filename": "EmployerCrudController.php", "line": "612"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `users` where `type` = ?", "hash": "e7098d5a40a0f6a86f190494613469e3f8705ca86dc150dec271de79e327bf78"}, "start_percent": 0, "width_percent": 11.878}, {"sql": "select count(*) as aggregate from `users` where `type` = 'employer' and month(`created_at`) = '08' and year(`created_at`) = 2025", "type": "query", "params": [], "bindings": ["employer", "08", 2025], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 619}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 91}, {"index": 17, "namespace": null, "name": "vendor/backpack/crud/src/app/Http/Controllers/CrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Http\\Controllers\\CrudController.php", "line": 121}, {"index": 18, "namespace": null, "name": "vendor/backpack/crud/src/app/Http/Controllers/CrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Http\\Controllers\\CrudController.php", "line": 42}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 162}], "start": **********.553415, "duration": 0.00315, "duration_str": "3.15ms", "memory": 0, "memory_str": null, "filename": "EmployerCrudController.php:619", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 619}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FEmployerCrudController.php&line=619", "ajax": false, "filename": "EmployerCrudController.php", "line": "619"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `users` where `type` = ? and month(`created_at`) = ? and year(`created_at`) = ?", "hash": "2294555b25919f5dd19d1816f6ae207b0af8ab057e4667afe0bf02d0b2d141a6"}, "start_percent": 11.878, "width_percent": 1.288}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'hri_recland_product' and table_name = 'users'", "type": "query", "params": [], "bindings": ["hri_recland_product", "users"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/ColumnsProtectedMethods.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\ColumnsProtectedMethods.php", "line": 312}, {"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Columns.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Columns.php", "line": 382}, {"index": 12, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/CrudColumn.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\CrudColumn.php", "line": 53}, {"index": 13, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Columns.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Columns.php", "line": 423}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 110}], "start": **********.5616791, "duration": 0.02224, "duration_str": "22.24ms", "memory": 0, "memory_str": null, "filename": "ColumnsProtectedMethods.php:312", "source": {"index": 10, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/ColumnsProtectedMethods.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\ColumnsProtectedMethods.php", "line": 312}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FLibrary%2FCrudPanel%2FTraits%2FColumnsProtectedMethods.php&line=312", "ajax": false, "filename": "ColumnsProtectedMethods.php", "line": "312"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select column_name as `column_name` from information_schema.columns where table_schema = ? and table_name = ?", "hash": "2f932a8ab5559dc2e718e2169d292b7c251235f387a3834f8b6fb4b996f87912"}, "start_percent": 13.165, "width_percent": 9.09}, {"sql": "select * from `users` where `type` = 'employer' order by `id` desc limit 25", "type": "query", "params": [], "bindings": ["employer"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Read.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Read.php", "line": 147}, {"index": 15, "namespace": null, "name": "vendor/backpack/crud/src/app/Http/Controllers/Operations/ListOperation.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Http\\Controllers\\Operations\\ListOperation.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.58738, "duration": 0.00199, "duration_str": "1.99ms", "memory": 0, "memory_str": null, "filename": "Read.php:147", "source": {"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Read.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Read.php", "line": 147}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FLibrary%2FCrudPanel%2FTraits%2FRead.php&line=147", "ajax": false, "filename": "Read.php", "line": "147"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `type` = ? order by `id` desc limit 25", "hash": "4e45654bb2a167350ea95ced074fe8b3e17b5ec1d55260239977608bb9706775"}, "start_percent": 22.255, "width_percent": 0.813}, {"sql": "select count(*) as total_rows from (select `users`.`id` from `users`) as `users_aggregator`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Query.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Query.php", "line": 277}, {"index": 12, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Query.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Query.php", "line": 197}, {"index": 13, "namespace": null, "name": "vendor/backpack/crud/src/app/Http/Controllers/Operations/ListOperation.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Http\\Controllers\\Operations\\ListOperation.php", "line": 100}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6253798, "duration": 0.002, "duration_str": "2ms", "memory": 0, "memory_str": null, "filename": "Query.php:277", "source": {"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Query.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Query.php", "line": 277}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FLibrary%2FCrudPanel%2FTraits%2FQuery.php&line=277", "ajax": false, "filename": "Query.php", "line": "277"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as total_rows from (select `users`.`id` from `users`) as `users_aggregator`", "hash": "242fd22e3064f44fab745474642a40f4bb25a9eab7104b15b0bea02cbb7d5b1e"}, "start_percent": 23.069, "width_percent": 0.817}, {"sql": "select count(*) as total_rows from (select `users`.`id` from `users` where `type` = 'employer') as `users_aggregator`", "type": "query", "params": [], "bindings": ["employer"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Query.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Query.php", "line": 277}, {"index": 12, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Query.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Query.php", "line": 207}, {"index": 13, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Query.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Query.php", "line": 220}, {"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/app/Http/Controllers/Operations/ListOperation.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Http\\Controllers\\Operations\\ListOperation.php", "line": 101}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.6290781, "duration": 0.0024300000000000003, "duration_str": "2.43ms", "memory": 0, "memory_str": null, "filename": "Query.php:277", "source": {"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Query.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Query.php", "line": 277}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FLibrary%2FCrudPanel%2FTraits%2FQuery.php&line=277", "ajax": false, "filename": "Query.php", "line": "277"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as total_rows from (select `users`.`id` from `users` where `type` = ?) as `users_aggregator`", "hash": "03fd0b30e66dbb318a4135e6c3931888d865b629eae451fc1d71ef3f50673e40"}, "start_percent": 23.886, "width_percent": 0.993}, {"sql": "select * from `wallets` where `wallets`.`user_id` = 7480 and `wallets`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [7480], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 184}, {"index": 22, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.8889399, "duration": 0.00377, "duration_str": "3.77ms", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `wallets` where `wallets`.`user_id` = ? and `wallets`.`user_id` is not null limit 1", "hash": "e492bf4aac2b7f5b3827c9e09c5ca4d6952ed05218b070d24ce22df01d4d8308"}, "start_percent": 24.879, "width_percent": 1.541}, {"sql": "select count(*) as aggregate from `job` where `job`.`employer_id` = 7480 and `job`.`employer_id` is not null", "type": "query", "params": [], "bindings": [7480], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, {"index": 19, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.911343, "duration": 0.00415, "duration_str": "4.15ms", "memory": 0, "memory_str": null, "filename": "EmployerCrudController.php:198", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FEmployerCrudController.php&line=198", "ajax": false, "filename": "EmployerCrudController.php", "line": "198"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `job` where `job`.`employer_id` = ? and `job`.`employer_id` is not null", "hash": "960034da14126e8b83cde909cdc7a41e5773664e6b9d926051afb2a75eb3b375"}, "start_percent": 26.42, "width_percent": 1.696}, {"sql": "select * from `wallets` where `wallets`.`user_id` = 7474 and `wallets`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [7474], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 184}, {"index": 22, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.976834, "duration": 0.0055899999999999995, "duration_str": "5.59ms", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `wallets` where `wallets`.`user_id` = ? and `wallets`.`user_id` is not null limit 1", "hash": "941db36531cafb9435101c811e7dffcff57c77798eb2c1b059baf9f2787b7936"}, "start_percent": 28.117, "width_percent": 2.285}, {"sql": "select count(*) as aggregate from `job` where `job`.`employer_id` = 7474 and `job`.`employer_id` is not null", "type": "query", "params": [], "bindings": [7474], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, {"index": 19, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.983909, "duration": 0.00792, "duration_str": "7.92ms", "memory": 0, "memory_str": null, "filename": "EmployerCrudController.php:198", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FEmployerCrudController.php&line=198", "ajax": false, "filename": "EmployerCrudController.php", "line": "198"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `job` where `job`.`employer_id` = ? and `job`.`employer_id` is not null", "hash": "4b46bfae160afd1438778256cdfe588c92f9f842576e0f2066c3a82ab611b209"}, "start_percent": 30.401, "width_percent": 3.237}, {"sql": "select * from `wallets` where `wallets`.`user_id` = 7473 and `wallets`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [7473], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 184}, {"index": 22, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.9982271, "duration": 0.00434, "duration_str": "4.34ms", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `wallets` where `wallets`.`user_id` = ? and `wallets`.`user_id` is not null limit 1", "hash": "aef5cade6334d910357fc951564bf9b80af25036ead23647f94711cc24ff17a0"}, "start_percent": 33.639, "width_percent": 1.774}, {"sql": "select count(*) as aggregate from `job` where `job`.`employer_id` = 7473 and `job`.`employer_id` is not null", "type": "query", "params": [], "bindings": [7473], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, {"index": 19, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.004047, "duration": 0.0038900000000000002, "duration_str": "3.89ms", "memory": 0, "memory_str": null, "filename": "EmployerCrudController.php:198", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FEmployerCrudController.php&line=198", "ajax": false, "filename": "EmployerCrudController.php", "line": "198"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `job` where `job`.`employer_id` = ? and `job`.`employer_id` is not null", "hash": "07d367478e7cbf0ec2b9df7dfb69d496cf95ba8e325fe541fb9882a8954bf84a"}, "start_percent": 35.412, "width_percent": 1.59}, {"sql": "select * from `wallets` where `wallets`.`user_id` = 7457 and `wallets`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [7457], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 184}, {"index": 22, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.01424, "duration": 0.0023799999999999997, "duration_str": "2.38ms", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `wallets` where `wallets`.`user_id` = ? and `wallets`.`user_id` is not null limit 1", "hash": "474e5b43e404a6db9b57baeb4017256694a89b76da0ab7f5ba882837d3df02b5"}, "start_percent": 37.002, "width_percent": 0.973}, {"sql": "select count(*) as aggregate from `job` where `job`.`employer_id` = 7457 and `job`.`employer_id` is not null", "type": "query", "params": [], "bindings": [7457], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, {"index": 19, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.018051, "duration": 0.00395, "duration_str": "3.95ms", "memory": 0, "memory_str": null, "filename": "EmployerCrudController.php:198", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FEmployerCrudController.php&line=198", "ajax": false, "filename": "EmployerCrudController.php", "line": "198"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `job` where `job`.`employer_id` = ? and `job`.`employer_id` is not null", "hash": "d490d46c8fbf40ded9db335de59a889fd6a75e1c3d030444f73bfa9bc44b9571"}, "start_percent": 37.975, "width_percent": 1.614}, {"sql": "select * from `companies` where `companies`.`id` = 696 and `companies`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [696], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Models\\User.php", "line": 143}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.026757, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `companies` where `companies`.`id` = ? and `companies`.`id` is not null limit 1", "hash": "af5b37af892d6f231f4dda4ae893f3fadfd564f900184fdd3ad6bf861ba4c804"}, "start_percent": 39.59, "width_percent": 0.155}, {"sql": "select * from `wallets` where `wallets`.`user_id` = 7456 and `wallets`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [7456], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 184}, {"index": 22, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.03021, "duration": 0.00233, "duration_str": "2.33ms", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `wallets` where `wallets`.`user_id` = ? and `wallets`.`user_id` is not null limit 1", "hash": "e35a8b7e01d31ab9e5b590fdf35489c28dcac391e7cad96dda37cfcd50569b9e"}, "start_percent": 39.745, "width_percent": 0.952}, {"sql": "select count(*) as aggregate from `job` where `job`.`employer_id` = 7456 and `job`.`employer_id` is not null", "type": "query", "params": [], "bindings": [7456], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, {"index": 19, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.033958, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "EmployerCrudController.php:198", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FEmployerCrudController.php&line=198", "ajax": false, "filename": "EmployerCrudController.php", "line": "198"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `job` where `job`.`employer_id` = ? and `job`.`employer_id` is not null", "hash": "c865aaecbe0478aa0ea43c9c6f193cf4085757cce2ce23735fd54ea3dd22253b"}, "start_percent": 40.697, "width_percent": 1.578}, {"sql": "select * from `wallets` where `wallets`.`user_id` = 7447 and `wallets`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [7447], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 184}, {"index": 22, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.044157, "duration": 0.0025499999999999997, "duration_str": "2.55ms", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `wallets` where `wallets`.`user_id` = ? and `wallets`.`user_id` is not null limit 1", "hash": "ce068c0db6dcd64288ed5b7a936f437e22590147dbc4632a1dd372036c458820"}, "start_percent": 42.275, "width_percent": 1.042}, {"sql": "select count(*) as aggregate from `job` where `job`.`employer_id` = 7447 and `job`.`employer_id` is not null", "type": "query", "params": [], "bindings": [7447], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, {"index": 19, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.048118, "duration": 0.00382, "duration_str": "3.82ms", "memory": 0, "memory_str": null, "filename": "EmployerCrudController.php:198", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FEmployerCrudController.php&line=198", "ajax": false, "filename": "EmployerCrudController.php", "line": "198"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `job` where `job`.`employer_id` = ? and `job`.`employer_id` is not null", "hash": "3798bc6902ebf3ab15814b4924163e5e8895f5d44d6c9fb3e535ddd63a79ea26"}, "start_percent": 43.317, "width_percent": 1.561}, {"sql": "select * from `wallets` where `wallets`.`user_id` = 7424 and `wallets`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [7424], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 184}, {"index": 22, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.058245, "duration": 0.00544, "duration_str": "5.44ms", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `wallets` where `wallets`.`user_id` = ? and `wallets`.`user_id` is not null limit 1", "hash": "a236224fc5abbb6c71bac9f0688f262fa14099f4d66df63afb893d341aee33e8"}, "start_percent": 44.879, "width_percent": 2.223}, {"sql": "select count(*) as aggregate from `job` where `job`.`employer_id` = 7424 and `job`.`employer_id` is not null", "type": "query", "params": [], "bindings": [7424], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, {"index": 19, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.0651681, "duration": 0.00382, "duration_str": "3.82ms", "memory": 0, "memory_str": null, "filename": "EmployerCrudController.php:198", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FEmployerCrudController.php&line=198", "ajax": false, "filename": "EmployerCrudController.php", "line": "198"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `job` where `job`.`employer_id` = ? and `job`.`employer_id` is not null", "hash": "7d601b3f3ec771a09f60ad0c055a6489708ed509f9819d68dde757368b176d2d"}, "start_percent": 47.102, "width_percent": 1.561}, {"sql": "select * from `wallets` where `wallets`.`user_id` = 7419 and `wallets`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [7419], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 184}, {"index": 22, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.075329, "duration": 0.00256, "duration_str": "2.56ms", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `wallets` where `wallets`.`user_id` = ? and `wallets`.`user_id` is not null limit 1", "hash": "b853e185452c2e8acd867e73ce0a66c4d921e57b10b35c326a628e0a4a55139c"}, "start_percent": 48.663, "width_percent": 1.046}, {"sql": "select count(*) as aggregate from `job` where `job`.`employer_id` = 7419 and `job`.`employer_id` is not null", "type": "query", "params": [], "bindings": [7419], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, {"index": 19, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.0793178, "duration": 0.00376, "duration_str": "3.76ms", "memory": 0, "memory_str": null, "filename": "EmployerCrudController.php:198", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FEmployerCrudController.php&line=198", "ajax": false, "filename": "EmployerCrudController.php", "line": "198"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `job` where `job`.`employer_id` = ? and `job`.`employer_id` is not null", "hash": "49f9cd01ea47fad8e931252070aabfb1cbfe80fd60d849adb7c13c823bfacaf2"}, "start_percent": 49.71, "width_percent": 1.537}, {"sql": "select * from `companies` where `companies`.`id` = 695 and `companies`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [695], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Models\\User.php", "line": 143}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.087462, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `companies` where `companies`.`id` = ? and `companies`.`id` is not null limit 1", "hash": "efccca5923fba3a7ca629ed22638446d444c5280539559bb3d86eaa3945c3180"}, "start_percent": 51.247, "width_percent": 0.155}, {"sql": "select * from `wallets` where `wallets`.`user_id` = 7415 and `wallets`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [7415], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 184}, {"index": 22, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.090946, "duration": 0.00534, "duration_str": "5.34ms", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `wallets` where `wallets`.`user_id` = ? and `wallets`.`user_id` is not null limit 1", "hash": "91d166efadf0d9feb5c1eccfdeb244fdaf4ed37d48044484062cd07be79c13de"}, "start_percent": 51.402, "width_percent": 2.183}, {"sql": "select count(*) as aggregate from `job` where `job`.`employer_id` = 7415 and `job`.`employer_id` is not null", "type": "query", "params": [], "bindings": [7415], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, {"index": 19, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.097967, "duration": 0.00863, "duration_str": "8.63ms", "memory": 0, "memory_str": null, "filename": "EmployerCrudController.php:198", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FEmployerCrudController.php&line=198", "ajax": false, "filename": "EmployerCrudController.php", "line": "198"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `job` where `job`.`employer_id` = ? and `job`.`employer_id` is not null", "hash": "c4090c36c2947966f778bea72a2f821bd1725429cbccdfdca6e2f0af0b03add0"}, "start_percent": 53.585, "width_percent": 3.527}, {"sql": "select * from `companies` where `companies`.`id` = 694 and `companies`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [694], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Models\\User.php", "line": 143}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.1164079, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `companies` where `companies`.`id` = ? and `companies`.`id` is not null limit 1", "hash": "571a80cca9dda154ca76d8b2ed8645be21541f0e9f06a7e9e79b96a44b11654d"}, "start_percent": 57.112, "width_percent": 0.298}, {"sql": "select * from `wallets` where `wallets`.`user_id` = 7412 and `wallets`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [7412], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 184}, {"index": 22, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.1226408, "duration": 0.0023599999999999997, "duration_str": "2.36ms", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `wallets` where `wallets`.`user_id` = ? and `wallets`.`user_id` is not null limit 1", "hash": "274f7305517cdf7ccd2943159da3bd76699c5fbb06417edac7c6de3ffb1ed8d1"}, "start_percent": 57.41, "width_percent": 0.965}, {"sql": "select count(*) as aggregate from `job` where `job`.`employer_id` = 7412 and `job`.`employer_id` is not null", "type": "query", "params": [], "bindings": [7412], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, {"index": 19, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.1263819, "duration": 0.00373, "duration_str": "3.73ms", "memory": 0, "memory_str": null, "filename": "EmployerCrudController.php:198", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FEmployerCrudController.php&line=198", "ajax": false, "filename": "EmployerCrudController.php", "line": "198"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `job` where `job`.`employer_id` = ? and `job`.`employer_id` is not null", "hash": "961f3e02434a919249bc2526256561680d474a60b13c08a0a05e1a850c923b0a"}, "start_percent": 58.375, "width_percent": 1.525}, {"sql": "select * from `wallets` where `wallets`.`user_id` = 7410 and `wallets`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [7410], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 184}, {"index": 22, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.136445, "duration": 0.0024700000000000004, "duration_str": "2.47ms", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `wallets` where `wallets`.`user_id` = ? and `wallets`.`user_id` is not null limit 1", "hash": "9402be0110dee0f6055f39fd2ed1b7935d3c98cf19184c87b0138633c5a52c49"}, "start_percent": 59.899, "width_percent": 1.01}, {"sql": "select count(*) as aggregate from `job` where `job`.`employer_id` = 7410 and `job`.`employer_id` is not null", "type": "query", "params": [], "bindings": [7410], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, {"index": 19, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.1403491, "duration": 0.0034500000000000004, "duration_str": "3.45ms", "memory": 0, "memory_str": null, "filename": "EmployerCrudController.php:198", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FEmployerCrudController.php&line=198", "ajax": false, "filename": "EmployerCrudController.php", "line": "198"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `job` where `job`.`employer_id` = ? and `job`.`employer_id` is not null", "hash": "706c42fb3a550c55fb30be823aa81cf9c1120b21b296b6330c07f7cc88cbebb5"}, "start_percent": 60.909, "width_percent": 1.41}, {"sql": "select * from `wallets` where `wallets`.`user_id` = 7402 and `wallets`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [7402], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 184}, {"index": 22, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.150196, "duration": 0.0024500000000000004, "duration_str": "2.45ms", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `wallets` where `wallets`.`user_id` = ? and `wallets`.`user_id` is not null limit 1", "hash": "a2eed0ba87bb12c81e76dd2214f3f8eea682bdb8df493985ae570f64ff4d4d78"}, "start_percent": 62.319, "width_percent": 1.001}, {"sql": "select count(*) as aggregate from `job` where `job`.`employer_id` = 7402 and `job`.`employer_id` is not null", "type": "query", "params": [], "bindings": [7402], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, {"index": 19, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.1540942, "duration": 0.0034300000000000003, "duration_str": "3.43ms", "memory": 0, "memory_str": null, "filename": "EmployerCrudController.php:198", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FEmployerCrudController.php&line=198", "ajax": false, "filename": "EmployerCrudController.php", "line": "198"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `job` where `job`.`employer_id` = ? and `job`.`employer_id` is not null", "hash": "6e2bec596f3c46794b45fd4c46a4a7ceb1447574432dba4f52d8ebfba8ba16c5"}, "start_percent": 63.321, "width_percent": 1.402}, {"sql": "select * from `companies` where `companies`.`id` = 693 and `companies`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [693], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Models\\User.php", "line": 143}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.161831, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `companies` where `companies`.`id` = ? and `companies`.`id` is not null limit 1", "hash": "bb5e4833d677e0597e88d9b4c200364351b0c5641907d67b27d71925cfb96e34"}, "start_percent": 64.722, "width_percent": 0.159}, {"sql": "select * from `wallets` where `wallets`.`user_id` = 7372 and `wallets`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [7372], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 184}, {"index": 22, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.1652932, "duration": 0.0023799999999999997, "duration_str": "2.38ms", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `wallets` where `wallets`.`user_id` = ? and `wallets`.`user_id` is not null limit 1", "hash": "563e361489cd2df5f7aa4a7e7f51aef20f0695bea274ff56701b928c23dd9dc4"}, "start_percent": 64.882, "width_percent": 0.973}, {"sql": "select count(*) as aggregate from `job` where `job`.`employer_id` = 7372 and `job`.`employer_id` is not null", "type": "query", "params": [], "bindings": [7372], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, {"index": 19, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.169084, "duration": 0.0035299999999999997, "duration_str": "3.53ms", "memory": 0, "memory_str": null, "filename": "EmployerCrudController.php:198", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FEmployerCrudController.php&line=198", "ajax": false, "filename": "EmployerCrudController.php", "line": "198"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `job` where `job`.`employer_id` = ? and `job`.`employer_id` is not null", "hash": "1268337f6d3e65353ab1218d8bb4ae114483700c422a7890a5094d4e255db09c"}, "start_percent": 65.855, "width_percent": 1.443}, {"sql": "select * from `wallets` where `wallets`.`user_id` = 7350 and `wallets`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [7350], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 184}, {"index": 22, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.17885, "duration": 0.00266, "duration_str": "2.66ms", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `wallets` where `wallets`.`user_id` = ? and `wallets`.`user_id` is not null limit 1", "hash": "1595827dba0f0e9ba123a07e7de3e984a2cbe97fe054ef2f7ee72f9bc497fb40"}, "start_percent": 67.297, "width_percent": 1.087}, {"sql": "select count(*) as aggregate from `job` where `job`.`employer_id` = 7350 and `job`.`employer_id` is not null", "type": "query", "params": [], "bindings": [7350], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, {"index": 19, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.18292, "duration": 0.00349, "duration_str": "3.49ms", "memory": 0, "memory_str": null, "filename": "EmployerCrudController.php:198", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FEmployerCrudController.php&line=198", "ajax": false, "filename": "EmployerCrudController.php", "line": "198"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `job` where `job`.`employer_id` = ? and `job`.`employer_id` is not null", "hash": "6840a5e8e324cce136bf358a2c3b875e929112efdbbb0247024dc866b48c668d"}, "start_percent": 68.385, "width_percent": 1.426}, {"sql": "select * from `companies` where `companies`.`id` = 692 and `companies`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [692], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Models\\User.php", "line": 143}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.190587, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `companies` where `companies`.`id` = ? and `companies`.`id` is not null limit 1", "hash": "8f6873daa834511165654398dbaf7fcb5a5f9be53738c545d041b2cde075c10d"}, "start_percent": 69.811, "width_percent": 0.151}, {"sql": "select * from `wallets` where `wallets`.`user_id` = 6644 and `wallets`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [6644], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 184}, {"index": 22, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.19401, "duration": 0.0024500000000000004, "duration_str": "2.45ms", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `wallets` where `wallets`.`user_id` = ? and `wallets`.`user_id` is not null limit 1", "hash": "e9b7240ddcfe8f60c9dd136ad25bc6f647ea3bb7ccf0d86b2f86a4492dec77e5"}, "start_percent": 69.962, "width_percent": 1.001}, {"sql": "select count(*) as aggregate from `job` where `job`.`employer_id` = 6644 and `job`.`employer_id` is not null", "type": "query", "params": [], "bindings": [6644], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, {"index": 19, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.197796, "duration": 0.00331, "duration_str": "3.31ms", "memory": 0, "memory_str": null, "filename": "EmployerCrudController.php:198", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FEmployerCrudController.php&line=198", "ajax": false, "filename": "EmployerCrudController.php", "line": "198"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `job` where `job`.`employer_id` = ? and `job`.`employer_id` is not null", "hash": "c54213b497e4785169b9a109ea3a65439761ce4be9c1c299da03f81058097ddf"}, "start_percent": 70.964, "width_percent": 1.353}, {"sql": "select * from `companies` where `companies`.`id` = 691 and `companies`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [691], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Models\\User.php", "line": 143}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.205344, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `companies` where `companies`.`id` = ? and `companies`.`id` is not null limit 1", "hash": "8eb6b3085dca6f08fa9ab45f33680bcc780db020b742a6deddac665dc5a72909"}, "start_percent": 72.317, "width_percent": 0.147}, {"sql": "select * from `wallets` where `wallets`.`user_id` = 6643 and `wallets`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [6643], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 184}, {"index": 22, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.208752, "duration": 0.00235, "duration_str": "2.35ms", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `wallets` where `wallets`.`user_id` = ? and `wallets`.`user_id` is not null limit 1", "hash": "cf7b49584e760305eeef6b5690a04b108f5ae731071a13f55cf7785d9c30cf23"}, "start_percent": 72.464, "width_percent": 0.961}, {"sql": "select count(*) as aggregate from `job` where `job`.`employer_id` = 6643 and `job`.`employer_id` is not null", "type": "query", "params": [], "bindings": [6643], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, {"index": 19, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.2125041, "duration": 0.00325, "duration_str": "3.25ms", "memory": 0, "memory_str": null, "filename": "EmployerCrudController.php:198", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FEmployerCrudController.php&line=198", "ajax": false, "filename": "EmployerCrudController.php", "line": "198"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `job` where `job`.`employer_id` = ? and `job`.`employer_id` is not null", "hash": "33d55663b711bea05e0cde5266917b9ac1d5e1f1b05736ffaddad32683e17d7a"}, "start_percent": 73.424, "width_percent": 1.328}, {"sql": "select * from `companies` where `companies`.`id` = 690 and `companies`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [690], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Models\\User.php", "line": 143}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.219947, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `companies` where `companies`.`id` = ? and `companies`.`id` is not null limit 1", "hash": "d0fcfbd69270f36d5506a56cbedbf071d50b26b304a14de0013e2392d6172c56"}, "start_percent": 74.753, "width_percent": 0.135}, {"sql": "select * from `wallets` where `wallets`.`user_id` = 6642 and `wallets`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [6642], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 184}, {"index": 22, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.2233539, "duration": 0.00232, "duration_str": "2.32ms", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `wallets` where `wallets`.`user_id` = ? and `wallets`.`user_id` is not null limit 1", "hash": "aa1c5d0909765b25e61b2fb70c9de0a10298c64452f1a157d0588a3e13ec975e"}, "start_percent": 74.888, "width_percent": 0.948}, {"sql": "select count(*) as aggregate from `job` where `job`.`employer_id` = 6642 and `job`.`employer_id` is not null", "type": "query", "params": [], "bindings": [6642], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, {"index": 19, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.2270372, "duration": 0.00326, "duration_str": "3.26ms", "memory": 0, "memory_str": null, "filename": "EmployerCrudController.php:198", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FEmployerCrudController.php&line=198", "ajax": false, "filename": "EmployerCrudController.php", "line": "198"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `job` where `job`.`employer_id` = ? and `job`.`employer_id` is not null", "hash": "ee74f5fb11f9c9e54b561e09e26a795fbb365583495ac94a1655e3289fb3c933"}, "start_percent": 75.836, "width_percent": 1.332}, {"sql": "select * from `companies` where `companies`.`id` = 689 and `companies`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [689], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Models\\User.php", "line": 143}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.234513, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `companies` where `companies`.`id` = ? and `companies`.`id` is not null limit 1", "hash": "c5f10fd4ea1ce223454f7a742f8d3263333a15350fb111c4c888d41881d3832d"}, "start_percent": 77.168, "width_percent": 0.135}, {"sql": "select * from `wallets` where `wallets`.`user_id` = 6641 and `wallets`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [6641], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 184}, {"index": 22, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.2378979, "duration": 0.00231, "duration_str": "2.31ms", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `wallets` where `wallets`.`user_id` = ? and `wallets`.`user_id` is not null limit 1", "hash": "ea3c4a7990cd90d5c389c98fca3c72a39155aca11fc0d38438e2b99179f78ffd"}, "start_percent": 77.303, "width_percent": 0.944}, {"sql": "select count(*) as aggregate from `job` where `job`.`employer_id` = 6641 and `job`.`employer_id` is not null", "type": "query", "params": [], "bindings": [6641], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, {"index": 19, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.2415571, "duration": 0.0033, "duration_str": "3.3ms", "memory": 0, "memory_str": null, "filename": "EmployerCrudController.php:198", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FEmployerCrudController.php&line=198", "ajax": false, "filename": "EmployerCrudController.php", "line": "198"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `job` where `job`.`employer_id` = ? and `job`.`employer_id` is not null", "hash": "38cafcd867410b84bdd952e135dad88078a3c96bc352a08e58302e1ebc759e80"}, "start_percent": 78.247, "width_percent": 1.349}, {"sql": "select * from `companies` where `companies`.`id` = 688 and `companies`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [688], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Models\\User.php", "line": 143}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.249085, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `companies` where `companies`.`id` = ? and `companies`.`id` is not null limit 1", "hash": "4da59eea9cae498301209dc606308987b949084bb0d7c9b4eafb70cedde02338"}, "start_percent": 79.596, "width_percent": 0.139}, {"sql": "select * from `wallets` where `wallets`.`user_id` = 6640 and `wallets`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [6640], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 184}, {"index": 22, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.25246, "duration": 0.00235, "duration_str": "2.35ms", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `wallets` where `wallets`.`user_id` = ? and `wallets`.`user_id` is not null limit 1", "hash": "79f6c8c0c501c1224f2385499a82e0a724cc90b932ced92611f6c63fe25952d1"}, "start_percent": 79.735, "width_percent": 0.961}, {"sql": "select count(*) as aggregate from `job` where `job`.`employer_id` = 6640 and `job`.`employer_id` is not null", "type": "query", "params": [], "bindings": [6640], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, {"index": 19, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.256136, "duration": 0.00333, "duration_str": "3.33ms", "memory": 0, "memory_str": null, "filename": "EmployerCrudController.php:198", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FEmployerCrudController.php&line=198", "ajax": false, "filename": "EmployerCrudController.php", "line": "198"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `job` where `job`.`employer_id` = ? and `job`.`employer_id` is not null", "hash": "b974968759341c497271b31ee16f586274f1302368a5d2d2dfdd5cc1a89354a4"}, "start_percent": 80.696, "width_percent": 1.361}, {"sql": "select * from `companies` where `companies`.`id` = 687 and `companies`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [687], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Models\\User.php", "line": 143}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.263735, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `companies` where `companies`.`id` = ? and `companies`.`id` is not null limit 1", "hash": "7675cdaffd2ec4c6d50d8bc4ea1a1a695fe3a522b5607f071148e759b45d5f07"}, "start_percent": 82.057, "width_percent": 0.147}, {"sql": "select * from `wallets` where `wallets`.`user_id` = 6639 and `wallets`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [6639], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 184}, {"index": 22, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.267133, "duration": 0.00239, "duration_str": "2.39ms", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `wallets` where `wallets`.`user_id` = ? and `wallets`.`user_id` is not null limit 1", "hash": "bed78cbd8b341620dff47cb0702b4afde4a44ba7ddb1ca53a2a238a279814763"}, "start_percent": 82.204, "width_percent": 0.977}, {"sql": "select count(*) as aggregate from `job` where `job`.`employer_id` = 6639 and `job`.`employer_id` is not null", "type": "query", "params": [], "bindings": [6639], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, {"index": 19, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.270942, "duration": 0.00721, "duration_str": "7.21ms", "memory": 0, "memory_str": null, "filename": "EmployerCrudController.php:198", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FEmployerCrudController.php&line=198", "ajax": false, "filename": "EmployerCrudController.php", "line": "198"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `job` where `job`.`employer_id` = ? and `job`.`employer_id` is not null", "hash": "60a268b7c8832eecde3c7efc7e7211806362816eff006c94173652f2518b8aa7"}, "start_percent": 83.181, "width_percent": 2.947}, {"sql": "select * from `companies` where `companies`.`id` = 686 and `companies`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [686], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Models\\User.php", "line": 143}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.282416, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `companies` where `companies`.`id` = ? and `companies`.`id` is not null limit 1", "hash": "07b2cb4ea24496d52b42594bb41a827eea3f64e65c2aa0b9e1dd581cfd10ff0d"}, "start_percent": 86.128, "width_percent": 0.143}, {"sql": "select * from `wallets` where `wallets`.`user_id` = 6638 and `wallets`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [6638], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 184}, {"index": 22, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.285844, "duration": 0.00537, "duration_str": "5.37ms", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `wallets` where `wallets`.`user_id` = ? and `wallets`.`user_id` is not null limit 1", "hash": "6180f74998796c757c2ca2f4f5c729713edbe85ee04cd21f489da323fa495f25"}, "start_percent": 86.271, "width_percent": 2.195}, {"sql": "select count(*) as aggregate from `job` where `job`.`employer_id` = 6638 and `job`.`employer_id` is not null", "type": "query", "params": [], "bindings": [6638], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, {"index": 19, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.2925558, "duration": 0.0035800000000000003, "duration_str": "3.58ms", "memory": 0, "memory_str": null, "filename": "EmployerCrudController.php:198", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FEmployerCrudController.php&line=198", "ajax": false, "filename": "EmployerCrudController.php", "line": "198"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `job` where `job`.`employer_id` = ? and `job`.`employer_id` is not null", "hash": "b4fa6fd99e538f0d1a6573bb10e1009a8b2e8fd5945077d36dc1cd1ad17618b9"}, "start_percent": 88.466, "width_percent": 1.463}, {"sql": "select * from `companies` where `companies`.`id` = 685 and `companies`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [685], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Models\\User.php", "line": 143}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.300405, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `companies` where `companies`.`id` = ? and `companies`.`id` is not null limit 1", "hash": "884c513d589e061b56cc45c27a65d286416f71f31003b9712fef367f2be89484"}, "start_percent": 89.929, "width_percent": 0.139}, {"sql": "select * from `wallets` where `wallets`.`user_id` = 6637 and `wallets`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [6637], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 184}, {"index": 22, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.303751, "duration": 0.00239, "duration_str": "2.39ms", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `wallets` where `wallets`.`user_id` = ? and `wallets`.`user_id` is not null limit 1", "hash": "36d001db0204638a9a561773db073e9ead3964ef2007cec49e548b532a3c173c"}, "start_percent": 90.068, "width_percent": 0.977}, {"sql": "select count(*) as aggregate from `job` where `job`.`employer_id` = 6637 and `job`.`employer_id` is not null", "type": "query", "params": [], "bindings": [6637], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, {"index": 19, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.307494, "duration": 0.0035499999999999998, "duration_str": "3.55ms", "memory": 0, "memory_str": null, "filename": "EmployerCrudController.php:198", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FEmployerCrudController.php&line=198", "ajax": false, "filename": "EmployerCrudController.php", "line": "198"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `job` where `job`.`employer_id` = ? and `job`.`employer_id` is not null", "hash": "f5ef5105eb0710c3298a91dbe1050274f04447954da9c69babf5fe1d674a20f4"}, "start_percent": 91.045, "width_percent": 1.451}, {"sql": "select * from `companies` where `companies`.`id` = 684 and `companies`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [684], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Models\\User.php", "line": 143}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.315321, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `companies` where `companies`.`id` = ? and `companies`.`id` is not null limit 1", "hash": "53d927247e9928fab2884ce3c95874aed20de28783b632a1766a9d960a81ebaf"}, "start_percent": 92.496, "width_percent": 0.139}, {"sql": "select * from `wallets` where `wallets`.`user_id` = 6636 and `wallets`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [6636], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 184}, {"index": 22, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.318718, "duration": 0.00235, "duration_str": "2.35ms", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `wallets` where `wallets`.`user_id` = ? and `wallets`.`user_id` is not null limit 1", "hash": "5e97351bebbf484c5ab10fa0ec0fc26528dc5ae3b3425e54a8acf39c7f7790cd"}, "start_percent": 92.635, "width_percent": 0.961}, {"sql": "select count(*) as aggregate from `job` where `job`.`employer_id` = 6636 and `job`.`employer_id` is not null", "type": "query", "params": [], "bindings": [6636], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, {"index": 19, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.322652, "duration": 0.00352, "duration_str": "3.52ms", "memory": 0, "memory_str": null, "filename": "EmployerCrudController.php:198", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FEmployerCrudController.php&line=198", "ajax": false, "filename": "EmployerCrudController.php", "line": "198"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `job` where `job`.`employer_id` = ? and `job`.`employer_id` is not null", "hash": "cd6dd017efbdf0f1f042ff95532e91e0e9b726676539d378d5191674a29bbf8f"}, "start_percent": 93.595, "width_percent": 1.439}, {"sql": "select * from `companies` where `companies`.`id` = 683 and `companies`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [683], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Models\\User.php", "line": 143}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.333063, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `companies` where `companies`.`id` = ? and `companies`.`id` is not null limit 1", "hash": "4cae243a02f83ec503c56fd87e9b7515aeeaa782890237acc7ceb895ebd46843"}, "start_percent": 95.034, "width_percent": 0.143}, {"sql": "select * from `wallets` where `wallets`.`user_id` = 6635 and `wallets`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [6635], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 184}, {"index": 22, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.336466, "duration": 0.00231, "duration_str": "2.31ms", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `wallets` where `wallets`.`user_id` = ? and `wallets`.`user_id` is not null limit 1", "hash": "56602fb2611d003f9d73ac2ab93276263af966da5f9f50f27629e63934b895bf"}, "start_percent": 95.177, "width_percent": 0.944}, {"sql": "select count(*) as aggregate from `job` where `job`.`employer_id` = 6635 and `job`.`employer_id` is not null", "type": "query", "params": [], "bindings": [6635], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, {"index": 19, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.340106, "duration": 0.0034300000000000003, "duration_str": "3.43ms", "memory": 0, "memory_str": null, "filename": "EmployerCrudController.php:198", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FEmployerCrudController.php&line=198", "ajax": false, "filename": "EmployerCrudController.php", "line": "198"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `job` where `job`.`employer_id` = ? and `job`.`employer_id` is not null", "hash": "df0c9b8392fbbb5f7d70e488ee9d4d6ba964c1074e5c7a847b750057b0d235ac"}, "start_percent": 96.121, "width_percent": 1.402}, {"sql": "select * from `companies` where `companies`.`id` = 682 and `companies`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [682], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Models\\User.php", "line": 143}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.347783, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `companies` where `companies`.`id` = ? and `companies`.`id` is not null limit 1", "hash": "3ff22c8b25c491974a92340a69ba7034961e4889fc766435085503bf1b3084e3"}, "start_percent": 97.523, "width_percent": 0.139}, {"sql": "select * from `wallets` where `wallets`.`user_id` = 6634 and `wallets`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [6634], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 184}, {"index": 22, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.351145, "duration": 0.00231, "duration_str": "2.31ms", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `wallets` where `wallets`.`user_id` = ? and `wallets`.`user_id` is not null limit 1", "hash": "e5ea6772acecd2c34113a35238bf8cfa8e7bd219df5499b5d35524154774445c"}, "start_percent": 97.662, "width_percent": 0.944}, {"sql": "select count(*) as aggregate from `job` where `job`.`employer_id` = 6634 and `job`.`employer_id` is not null", "type": "query", "params": [], "bindings": [6634], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, {"index": 19, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.php", "line": 9}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.35479, "duration": 0.0034100000000000003, "duration_str": "3.41ms", "memory": 0, "memory_str": null, "filename": "EmployerCrudController.php:198", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 198}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FEmployerCrudController.php&line=198", "ajax": false, "filename": "EmployerCrudController.php", "line": "198"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `job` where `job`.`employer_id` = ? and `job`.`employer_id` is not null", "hash": "affffeb103dd431a41b7e0b663eb5538ebc52e8d1e472bf2f9b62ecf2739b82e"}, "start_percent": 98.606, "width_percent": 1.394}]}, "models": {"data": {"App\\Models\\User": {"retrieved": 25, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Company": {"retrieved": 15, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FCompany.php&line=1", "ajax": false, "filename": "Company.php", "line": "?"}}, "App\\Models\\Wallet": {"retrieved": 13, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FWallet.php&line=1", "ajax": false, "filename": "Wallet.php", "line": "?"}}}, "count": 53, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 53}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "67FrIiAKRzqdDDYE9lpf1v63S50ahQba2svcMgmp", "PHPDEBUGBAR_STACK_DATA": "[]", "_previous": "array:1 [\n  \"url\" => \"http://recland.local/admin/employers\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1"}, "request": {"data": {"status": "200 OK", "full_url": "http://recland.local/admin/employers/search", "action_name": "employers.search", "controller_action": "App\\Http\\Controllers\\Admin\\EmployerCrudController@search", "uri": "POST admin/employers/search", "operation": "list", "controller": "App\\Http\\Controllers\\Admin\\EmployerCrudController@search<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FHttp%2FControllers%2FOperations%2FListOperation.php&line=70\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "admin", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FHttp%2FControllers%2FOperations%2FListOperation.php&line=70\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/backpack/crud/src/app/Http/Controllers/Operations/ListOperation.php:70-111</a>", "middleware": "web, admin", "duration": "1.27s", "peak_memory": "46MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-69487775 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-69487775\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-675340740 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>draw</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>columns</span>\" => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str>1</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str>2</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str>4</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str>6</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str>7</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str>8</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str>9</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>start</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>length</span>\" => \"<span class=sf-dump-str title=\"2 characters\">25</span>\"\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>totalEntryCount</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-675340740\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2158</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">67FrIiAKRzqdDDYE9lpf1v63S50ahQba2svcMgmp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://recland.local/admin/employers</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,vi;q=0.8,nl;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1257 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkR4eDJ2S2txdnV0SENrWk1VVVVxRmc9PSIsInZhbHVlIjoiUU5TNDgyREJKcEV4K0pyendYMk1YM1NNU2kzSFZtZFRJVUIrN0FGV3hWWDJ1ZzNLY2pLamJ2bENEY0tFeDNsT0YwZEwwSTdVUXcza3ZHNk1UUEpjT3BOZzdwMk42Q2RYRDJ1cWpCZzFZQXphZUFRYXpRd1JpcWRzc2xuMzNGYk8iLCJtYWMiOiI3NzA2Y2ExNmJhYmE4ODE5NjhkMjFlNTJiNmQ1NjNlMjc4NmY2MmM1NTY4ZTIwMmE5MDI2MGIwYmZhOWU3NzczIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6ImxQZWF3dGJOOEJyaGdUQkM5eSthQkE9PSIsInZhbHVlIjoick50QmZyWFVlenE0Sm1MeWRPekNwRkpGNE91c0xVdGFyOEYyVW9OOTByY2RwbWVjYU1Pa2hnOHVFbThiczZidjhyVW5uZ0ZaVlJXYmdvbUc0NlVSZmRVWVY1UmJpY1pHQXkwU1hjeFZTdmg2NDZJN2pSZm9VK2JweDZLbkcyRWYiLCJtYWMiOiIxN2U5MDJiOTZkMWQ3ZGYzYWQ0OTlkMGQ1N2IyNjcyNjc2NzQ2OTE3MGY3MGQ5ZGRjYjdlMGQ2ZWM1OTNkYTAxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|cm8f4SgFu8wrAd7wPR9b0gsQwQ8YYGlGSWS8Y9hgKQXIsj7qT8VG3KwC3dMx|$2y$10$iTSniY9ReMSnjm6GaX0bRuGWPNsyk9pQat6r8dKHdyoQSgwMkVojS</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">67FrIiAKRzqdDDYE9lpf1v63S50ahQba2svcMgmp</span>\"\n  \"<span class=sf-dump-key>recland_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aK6CrmwILvnr50B6VrqyQAI9QjCWvYUdKGChzrVE</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1980646149 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 04 Aug 2025 01:36:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1980646149\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1721530438 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">67FrIiAKRzqdDDYE9lpf1v63S50ahQba2svcMgmp</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://recland.local/admin/employers</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1721530438\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://recland.local/admin/employers/search", "action_name": "employers.search", "controller_action": "App\\Http\\Controllers\\Admin\\EmployerCrudController@search"}, "badge": null}}