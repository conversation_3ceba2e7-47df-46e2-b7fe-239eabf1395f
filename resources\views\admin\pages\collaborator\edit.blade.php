@extends('admin.layouts.app')
@section('css_custom')
    <link href="{{asset2('/backend/assets/plugins/fileupload/css/fileupload.css')}}" rel="stylesheet" type="text/css" />
    <!-- INTERNAL Fancy File Upload css -->
    <link href="{{asset2('/backend/assets/plugins/fancyuploder/fancy_fileupload.css')}}" rel="stylesheet" />
@endsection
@section('content')

    <!--Page header-->
    <div class="page-header d-xl-flex d-block">
        <div class="page-leftheader">
            <h4 class="page-title">Chỉnh sửa CTV</h4>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-body">
                    <div class=" tab-menu-heading p-0 bg-light">
                        <div class="tabs-menu1">
                            <!-- Tabs -->
                            <ul class="nav panel-tabs">
                                <li class="ml-4"><a href="#tab-create" class="@if(!session('action') || session('action') == 'edit' ) active @endif"  data-toggle="tab">Thông tin</a></li>
                                @if(\App\Services\Admin\PermissionService::checkPermission('submit.cv-datatable'))
                                <li class="ml-4"><a href="#referral-list"  class="" data-toggle="tab">Giới thiệu ứng viên</a></li>
                                @endif
                                <li class="ml-4"><a href="#tab-change-password"  class="@if(session('action') == 'changePassword' ) active @endif" data-toggle="tab">Đổi mật khẩu</a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="panel-body tabs-menu-body">
                        <div class="tab-content">
                            <div class="tab-pane @if(!session('action') || session('action') == 'edit' ) active @endif" id="tab-create">
                                <form action="{{route('collaborator.update',['collaborator'=>$data->id])}}" method="POST"  enctype="multipart/form-data" class="sbm_form_s">
                                    @csrf
                                    {{method_field('put')}}
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="text-center">
                                                    <div class="upload-center">
                                                        <div class="max-with-upload">
                                                            <input type="file" class="dropify " name="avatar" data-default-file="{{$data->avatar_url}}" data-height="180"  />
                                                        </div>
                                                    </div>
                                                    @if($errors->has('avatar'))
                                                        <div class="text-danger"> {{$errors->first('avatar')}} </div>
                                                    @endif
                                                    <label class="form-label">Ảnh đại diện</label>
                                                </div>
                                                <div class="form-group">
                                                    <label class="form-label">Họ tên <span class="text-red">*</span></label>
                                                    <input class="form-control @if($errors->has('name')) is-invalid @endif" name="name" value="{{old('name',$data->name)}}">
                                                    @if($errors->has('name'))
                                                        <span class="text-danger"> {{$errors->first('name')}} </span>
                                                    @endif
                                                </div>

                                                <div class="form-group">
                                                    <label class="form-label">Email <span class="text-red">*</span></label>
                                                    <input class="form-control @if($errors->has('email')) is-invalid @endif" name="email" value="{{old('email',$data->email)}}">
                                                    @if($errors->has('email'))
                                                        <span class="text-danger"> {{$errors->first('email')}} </span>
                                                    @endif
                                                </div>

                                                <div class="form-group">
                                                    <label class="form-label">Ngày sinh <span class="text-red">*</span></label>
                                                    <input class="form-control fc-datepicker @if($errors->has('birthday')) is-invalid @endif" value="{{old('birthday',$data->birthday_value)}}" name="birthday" placeholder="MM/DD/YYYY" type="text" autocomplete="off">
                                                    @if($errors->has('birthday'))
                                                        <span class="text-danger"> {{$errors->first('birthday')}} </span>
                                                    @endif
                                                </div>

                                                <div class="form-group">
                                                    <label class="form-label">Trạng thái hoạt động  <span class="text-red">*</span></label>
                                                    <label class="custom-switch">
                                                        @if(old('flg_status') == 1)
                                                            <input type="checkbox" @if(old('is_active') == 1) checked @endif value="1" name="is_active" class="custom-switch-input">
                                                        @else
                                                            <input type="checkbox" @if($data->is_active == 1) checked @endif value="1" name="is_active" class="custom-switch-input">
                                                        @endif
                                                        <span class="custom-switch-indicator custom-switch-indicator-xl"></span>
                                                        <span class="custom-switch-description mr-2" id="status_active">Active</span>
                                                    </label>
                                                </div>

                                                <div class="form-group">
                                                    <label class="form-label">Số CCCD</label>
                                                    <input class="form-control @if($errors->has('cccd_number')) is-invalid @endif" name="cccd_number" value="{{old('cccd_number', isset($data->userInfo) ? $data->userInfo->cccd_number : '')}}" placeholder="Nhập số căn cước công dân">
                                                    @if($errors->has('cccd_number'))
                                                        <span class="text-danger"> {{$errors->first('cccd_number')}} </span>
                                                    @endif
                                                </div>

                                                <div class="form-group">
                                                    <label class="form-label">Ảnh mặt trước CCCD</label>
                                                    <div class="upload-center">
                                                        <div class="max-with-upload">
                                                            <input type="file" class="dropify" name="cccd_front_image"
                                                                   data-default-file="{{isset($data->userInfo) && $data->userInfo->cccd_front_image ? gen_url_file_s3($data->userInfo->cccd_front_image, '', false) : ''}}"
                                                                   data-height="180" />
                                                        </div>
                                                    </div>
                                                    @if(isset($data->userInfo) && $data->userInfo->cccd_front_image)
                                                        <div class="mt-2 text-center">
                                                            <img src="{{gen_url_file_s3($data->userInfo->cccd_front_image, '', false)}}"
                                                                 alt="CCCD Front"
                                                                 style="max-width: 200px; max-height: 150px; border: 1px solid #ddd; border-radius: 4px;"
                                                                 class="img-thumbnail">
                                                            <div class="mt-2">
                                                                <a href="{{gen_url_file_s3($data->userInfo->cccd_front_image, '', false)}}"
                                                                   target="_blank"
                                                                   class="btn btn-sm btn-info">
                                                                    <i class="fa fa-download"></i> Tải ảnh
                                                                </a>
                                                            </div>
                                                        </div>
                                                    @endif
                                                    @if($errors->has('cccd_front_image'))
                                                        <div class="text-danger"> {{$errors->first('cccd_front_image')}} </div>
                                                    @endif
                                                </div>

                                                <div class="form-group">
                                                    <label class="form-label">Ảnh mặt sau CCCD</label>
                                                    <div class="upload-center">
                                                        <div class="max-with-upload">
                                                            <input type="file" class="dropify" name="cccd_back_image"
                                                                   data-default-file="{{isset($data->userInfo) && $data->userInfo->cccd_back_image ? gen_url_file_s3($data->userInfo->cccd_back_image, '', false) : ''}}"
                                                                   data-height="180" />
                                                        </div>
                                                    </div>
                                                    @if(isset($data->userInfo) && $data->userInfo->cccd_back_image)
                                                        <div class="mt-2 text-center">
                                                            <img src="{{gen_url_file_s3($data->userInfo->cccd_back_image, '', false)}}"
                                                                 alt="CCCD Back"
                                                                 style="max-width: 200px; max-height: 150px; border: 1px solid #ddd; border-radius: 4px;"
                                                                 class="img-thumbnail">
                                                            <div class="mt-2">
                                                                <a href="{{gen_url_file_s3($data->userInfo->cccd_back_image, '', false)}}"
                                                                   target="_blank"
                                                                   class="btn btn-sm btn-info">
                                                                    <i class="fa fa-download"></i> Tải ảnh
                                                                </a>
                                                            </div>
                                                        </div>
                                                    @endif
                                                    @if($errors->has('cccd_back_image'))
                                                        <div class="text-danger"> {{$errors->first('cccd_back_image')}} </div>
                                                    @endif
                                                </div>

                                            </div>
                                            <div class="col-md-6">
                                                <h3 class="card-title text-center">Thông tin tài khoản ngân hàng</h3>
                                                <div class="table-responsive mb-5">
                                                    <table class="table card-table table-vcenter text-nowrap mb-0">
                                                        <tbody>
                                                        <tr>
                                                            <th scope="row" class="w-25 text-right">Tên chủ tài khoản:</th>
                                                            <td>{{isset($data->userInfo)?$data->userInfo->bank_account:''}}</td>
                                                        </tr>
                                                        <tr>
                                                            <th scope="row" class="w-25 text-right">Số tài khoản:</th>
                                                            <td>{{isset($data->userInfo)?$data->userInfo->bank_account_number:''}}</td>
                                                        </tr>
                                                        <tr>
                                                            <th scope="row" class="w-25 text-right">Tên ngân hàng:</th>
                                                            <td>{{isset($data->userInfo)?$data->userInfo->bank_name:''}}</td>
                                                        </tr>
                                                        <tr>
                                                            <th scope="row" class="w-25 text-right">Tên chi nhánh:</th>
                                                            <td>{{isset($data->userInfo)?$data->userInfo->bank_branch:''}}</td>
                                                        </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                                <h3 class="card-title text-center">Thông tin khác</h3>
                                                <div class="table-responsive">
                                                    <table class="table card-table table-vcenter text-nowrap mb-0">
                                                        <tbody>
                                                        <tr>
                                                            <th scope="row" class="w-25 text-right">Provider:</th>
                                                            <td>{{$data->provider}}</td>
                                                        </tr>
                                                        <tr>
                                                            <th scope="row" class="w-25 text-right">Referral code:</th>
                                                            <td>{{$data->referral_code}}</td>
                                                        </tr>
                                                        <tr>
                                                            <th scope="row" class="w-25 text-right">Verify:</th>
                                                            <td>{{isset($data->email_verified_at)?'Đã xác thực':'Chưa xác thực'}}</td>
                                                        </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-footer text-right">
                                        <input type="hidden" class="flg_status" name="flg_status" value="{{old('flg_status')}}"/>
                                        <a href="{{route('collaborator.index')}}" class="btn btn-danger btn-lg">Close</a>
                                        <button class="btn btn-success btn-lg sbm_form" type="button">Submit</button>
                                    </div>
                                </form>
                            </div>
                            @if(\App\Services\Admin\PermissionService::checkPermission('submit.cv-datatable'))
                            <div class="tab-pane " id="referral-list">
                                <div class="row">
                                    <div class="col-xl-12 col-md-12 col-lg-12">
                                        {{$datatable->render()}}
                                    </div>
                                </div>
                            </div>
                            @endif
                            @if(\App\Services\Admin\PermissionService::checkPermission('collaborator.change-password'))
                            <div class="tab-pane @if(session('action') == 'changePassword' ) active @endif" id="tab-change-password">
                                <form action="{{route('collaborator.change-password',['collaborator'=>$data->id])}}" method="POST">
                                    @csrf
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-3"></div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label class="form-label">Mật khẩu mới <span class="text-red">*</span></label>
                                                    <input type="password" class="form-control @if($errors->has('password')) is-invalid @endif" name="password" value="">
                                                    @if($errors->has('password'))
                                                        <span class="text-danger"> {{$errors->first('password')}} </span>
                                                    @endif
                                                </div>
                                                <div class="form-group">
                                                    <label class="form-label">Xác nhận mật khẩu mới <span class="text-red">*</span></label>
                                                    <input type="password" class="form-control @if($errors->has('password_confirm')) is-invalid @endif" name="password_confirm" value="">
                                                    @if($errors->has('password_confirm'))
                                                        <span class="text-danger"> {{$errors->first('password_confirm')}} </span>
                                                    @endif
                                                </div>
                                                <div class="form-group text-center">
                                                    <a href="{{route('collaborator.index')}}" class="btn btn-danger btn-lg">Close</a>
                                                    <button class="btn btn-success btn-lg">Submit</button>
                                                </div>
                                            </div>
                                            <div class="col-md-3"></div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


@endsection

@section('scripts')
    <script src="{{asset2('backend/assets/plugins/fileupload/js/dropify.js')}}"></script>
    <script src="{{asset2('backend/assets/js/filupload.js')}}"></script>
    <script>
        $(document).ready(function () {
            $('[name="is_active"]').prop("checked") ? $('#status_active').html('Active') : $('#status_active').html('Inactive');
            $('[name="is_active"]').change(function (){
                if($(this).prop("checked")){
                    $('#status_active').html('Active');
                }else{
                    $('#status_active').html('Inactive');
                }
            });

            $(".fc-datepicker").datepicker({
                showOtherMonths: !0,
                selectOtherMonths: !0,
                dateFormat: 'dd/mm/yy'
            })

            $(document).on('click', '.ti-import', function(){
                let url = $(this).data('value');
                let route = '{{route('download-file')}}' + '?url=' + url;

                window.open(route, '_blank');
            });

            $(document).on('click', '.fa-eye', function(){
                let url = $(this).data('value');
                window.open(url, '_blank');
            });


            $(document).on('click', '.sbm_form', function(){
                $('.flg_status').val('1');
                $('.sbm_form_s').get(0).submit();
            });
        })
    </script>
@endsection
