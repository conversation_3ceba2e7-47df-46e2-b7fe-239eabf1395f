<footer class="footer">
    <div class="container">
        <div class="row">
            <div class="col-md-4">
                <a class="logo" title="new-ca" href="#">
                    <img src="<?php echo e(asset2('frontend/assets_v2/images/graphics/logo.png')); ?>" alt="">
                    <img class="logo-white" src="<?php echo e(asset2('frontend/assets_v2/images/graphics/logo-white.png')); ?>"
                        alt="">
                </a>
                <div class="mb40"><?php echo e(config('settings.' . app()->getLocale() . '.footer.mangvieclamvauudai')); ?></div>
                <div class="social flex">
                    <a target="_blank" href="<?php echo e(config('settings.global.link_facebook')); ?>"><img src="<?php echo e(asset2('frontend/assets_v2/images/icons/icon-face.png')); ?>" alt=""></a>
                    <a target="_blank" href="<?php echo e(config('settings.global.group_zalo_link')); ?>"><img src="<?php echo e(asset2('frontend/assets_v2/images/icons/icon-zalo.png')); ?>" alt=""></a>
                    <a target="_blank" href="<?php echo e(config('settings.global.link_linkedin')); ?>"><img src="<?php echo e(asset2('frontend/assets_v2/images/icons/icon-in.png')); ?>" alt=""></a>
                </div>
            </div>
            <div class="col-md-8">
                <div class="row">
                    <div class="col-sm-6">
                        <h3 class="title"> <?php echo e(config('settings.' . app()->getLocale() . '.footer.vechungtoi')); ?></h3>
                        <ul>
                            <li><a target="_blank"
                                   href="<?php echo e(route('about-us')); ?>"><?php echo config('settings.' . app()->getLocale() . '.footer.gioithieu'); ?></a>
                            </li>
                            <li><a target="_blank"
                                   href="<?php echo e(route('contact-us')); ?>"><?php echo config('settings.' . app()->getLocale() . '.footer.lienhe'); ?></a></li>
                            <li><a target="_blank"
                                   href="#"><?php echo config('settings.' . app()->getLocale() . '.footer.dangtuyendung'); ?></a>
                            </li>
                            <li><a target="_blank"
                                   href="#"><?php echo config('settings.' . app()->getLocale() . '.footer.timkiemhoso'); ?></a>
                            </li>
                        </ul>
                    </div>
                    <div class="col-sm-6">
                        <h3 class="title">
                            <?php echo config('settings.' . app()->getLocale() . '.footer.doitac'); ?>

                        </h3>
                        <ul>
                            <li>
                                <a target="_blank" href="<?php echo e(route('mechanismOfActionCollaborator')); ?>">
                                    <?php echo config('settings.' . app()->getLocale() . '.footer.cochehoatdongctv'); ?></a>
                            </li>
                            <li>
                                <a target="_blank" href="<?php echo e(route('mechanismOfActionRec')); ?>">
                                    <?php echo config('settings.' . app()->getLocale() . '.footer.cochehoatdongntd'); ?></a>
                            </li>
                            <li>
                                <a target="_blank" href="<?php echo e(route('termsOfService')); ?>">
                                    <?php echo config('settings.' . app()->getLocale() . '.footer.dieukhoandichvu'); ?></a>
                            </li>
                            <li>
                                <a target="_blank" href="<?php echo e(route('privacyPolicy')); ?>">
                                    <?php echo config('settings.' . app()->getLocale() . '.footer.quydinhbaomat'); ?></a>
                            </li>
                            <?php if(auth()->guard('client')->check()): ?>
                            <li>
                                <a href="#" id="bugReportLink">
                                    <i class="fas fa-bug"></i> Báo cáo lỗi
                                </a>
                            </li>
                            <?php endif; ?>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</footer>
<?php /**PATH D:\Projects\HRI\RecLand\resources\views/frontend/inc_layouts/v2/hh_footer.blade.php ENDPATH**/ ?>