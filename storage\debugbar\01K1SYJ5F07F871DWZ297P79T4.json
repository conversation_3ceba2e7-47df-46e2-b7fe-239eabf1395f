{"__meta": {"id": "01K1SYJ5F07F871DWZ297P79T4", "datetime": "2025-08-04 14:08:32", "utime": **********.097521, "method": "GET", "uri": "/admin/new-collaborator", "ip": "127.0.0.1"}, "php": {"version": "8.1.6", "interface": "apache2handler"}, "messages": {"count": 2, "messages": [{"message": "[14:08:32] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `id` = '1' limit 1\\n-- \",\n    \"Time:\": 20.27\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.021954, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:32] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)\\n-- \",\n    \"Time:\": 0.58\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.031738, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 3, "start": **********.525212, "end": **********.097543, "duration": 0.5723309516906738, "duration_str": "572ms", "measures": [{"label": "Booting", "start": **********.525212, "relative_start": 0, "end": **********.926897, "relative_end": **********.926897, "duration": 0.****************, "duration_str": "402ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.926906, "relative_start": 0.*****************, "end": **********.097545, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "171ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.939922, "relative_start": 0.*****************, "end": **********.943457, "relative_end": **********.943457, "duration": 0.0035347938537597656, "duration_str": "3.53ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "40MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 23, "nb_templates": 23, "templates": [{"name": "1x admin.pages.collaborator.backpack_list", "param_count": null, "params": [], "start": **********.074861, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/pages/collaborator/backpack_list.blade.phpadmin.pages.collaborator.backpack_list", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Fpages%2Fcollaborator%2Fbackpack_list.blade.php&line=1", "ajax": false, "filename": "backpack_list.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.pages.collaborator.backpack_list"}, {"name": "1x crud::inc.button_stack", "param_count": null, "params": [], "start": **********.076391, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/inc/button_stack.blade.phpcrud::inc.button_stack", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Fcrud%2Finc%2Fbutton_stack.blade.php&line=1", "ajax": false, "filename": "button_stack.blade.php", "line": "?"}, "render_count": 1, "name_original": "crud::inc.button_stack"}, {"name": "1x crud::buttons.create", "param_count": null, "params": [], "start": **********.076994, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/buttons/create.blade.phpcrud::buttons.create", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Fcrud%2Fbuttons%2Fcreate.blade.php&line=1", "ajax": false, "filename": "create.blade.php", "line": "?"}, "render_count": 1, "name_original": "crud::buttons.create"}, {"name": "1x crud::inc.filters_navbar", "param_count": null, "params": [], "start": **********.077441, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/inc/filters_navbar.blade.phpcrud::inc.filters_navbar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Fcrud%2Finc%2Ffilters_navbar.blade.php&line=1", "ajax": false, "filename": "filters_navbar.blade.php", "line": "?"}, "render_count": 1, "name_original": "crud::inc.filters_navbar"}, {"name": "1x backpack.pro::filters.select2", "param_count": null, "params": [], "start": **********.078291, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\packages\\backpack-pro\\src/../resources/views/filters/select2.blade.phpbackpack.pro::filters.select2", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fpackages%2Fbackpack-pro%2Fresources%2Fviews%2Ffilters%2Fselect2.blade.php&line=1", "ajax": false, "filename": "select2.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.pro::filters.select2"}, {"name": "2x backpack.pro::filters.dropdown", "param_count": null, "params": [], "start": **********.079272, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\packages\\backpack-pro\\src/../resources/views/filters/dropdown.blade.phpbackpack.pro::filters.dropdown", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fpackages%2Fbackpack-pro%2Fresources%2Fviews%2Ffilters%2Fdropdown.blade.php&line=1", "ajax": false, "filename": "dropdown.blade.php", "line": "?"}, "render_count": 2, "name_original": "backpack.pro::filters.dropdown"}, {"name": "1x backpack.pro::filters.date_range", "param_count": null, "params": [], "start": **********.080725, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\packages\\backpack-pro\\src/../resources/views/filters/date_range.blade.phpbackpack.pro::filters.date_range", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fpackages%2Fbackpack-pro%2Fresources%2Fviews%2Ffilters%2Fdate_range.blade.php&line=1", "ajax": false, "filename": "date_range.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.pro::filters.date_range"}, {"name": "1x crud::inc.datatables_logic", "param_count": null, "params": [], "start": **********.082071, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/inc/datatables_logic.blade.phpcrud::inc.datatables_logic", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Fcrud%2Finc%2Fdatatables_logic.blade.php&line=1", "ajax": false, "filename": "datatables_logic.blade.php", "line": "?"}, "render_count": 1, "name_original": "crud::inc.datatables_logic"}, {"name": "1x crud::inc.export_buttons", "param_count": null, "params": [], "start": **********.084886, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/inc/export_buttons.blade.phpcrud::inc.export_buttons", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Fcrud%2Finc%2Fexport_buttons.blade.php&line=1", "ajax": false, "filename": "export_buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "crud::inc.export_buttons"}, {"name": "1x crud::inc.details_row_logic", "param_count": null, "params": [], "start": **********.085482, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/inc/details_row_logic.blade.phpcrud::inc.details_row_logic", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Fcrud%2Finc%2Fdetails_row_logic.blade.php&line=1", "ajax": false, "filename": "details_row_logic.blade.php", "line": "?"}, "render_count": 1, "name_original": "crud::inc.details_row_logic"}, {"name": "1x admin.layouts.app_backpack", "param_count": null, "params": [], "start": **********.086618, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/layouts/app_backpack.blade.phpadmin.layouts.app_backpack", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Flayouts%2Fapp_backpack.blade.php&line=1", "ajax": false, "filename": "app_backpack.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.layouts.app_backpack"}, {"name": "1x backpack::inc.head", "param_count": null, "params": [], "start": **********.087624, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\resources\\views\\base/inc/head.blade.phpbackpack::inc.head", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fbase%2Finc%2Fhead.blade.php&line=1", "ajax": false, "filename": "head.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack::inc.head"}, {"name": "4x backpack::inc.widgets", "param_count": null, "params": [], "start": **********.088862, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\resources\\views\\base/inc/widgets.blade.phpbackpack::inc.widgets", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fbase%2Finc%2Fwidgets.blade.php&line=1", "ajax": false, "filename": "widgets.blade.php", "line": "?"}, "render_count": 4, "name_original": "backpack::inc.widgets"}, {"name": "1x admin.inc_layouts.side_bar", "param_count": null, "params": [], "start": **********.090127, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/inc_layouts/side_bar.blade.phpadmin.inc_layouts.side_bar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Finc_layouts%2Fside_bar.blade.php&line=1", "ajax": false, "filename": "side_bar.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.inc_layouts.side_bar"}, {"name": "1x admin.inc_layouts.header", "param_count": null, "params": [], "start": **********.091764, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/inc_layouts/header.blade.phpadmin.inc_layouts.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Finc_layouts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.inc_layouts.header"}, {"name": "1x admin.inc_layouts.footer", "param_count": null, "params": [], "start": **********.092323, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/inc_layouts/footer.blade.phpadmin.inc_layouts.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Finc_layouts%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.inc_layouts.footer"}, {"name": "1x backpack::inc.scripts", "param_count": null, "params": [], "start": **********.09307, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\resources\\views\\base/inc/scripts.blade.phpbackpack::inc.scripts", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fbase%2Finc%2Fscripts.blade.php&line=1", "ajax": false, "filename": "scripts.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack::inc.scripts"}, {"name": "1x backpack::inc.alerts", "param_count": null, "params": [], "start": **********.093902, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\resources\\views\\base/inc/alerts.blade.phpbackpack::inc.alerts", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fbase%2Finc%2Falerts.blade.php&line=1", "ajax": false, "filename": "alerts.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack::inc.alerts"}, {"name": "1x crud::inc.ajax_error_frame", "param_count": null, "params": [], "start": **********.094636, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/inc/ajax_error_frame.blade.phpcrud::inc.ajax_error_frame", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Fcrud%2Finc%2Fajax_error_frame.blade.php&line=1", "ajax": false, "filename": "ajax_error_frame.blade.php", "line": "?"}, "render_count": 1, "name_original": "crud::inc.ajax_error_frame"}]}, "route": {"uri": "GET admin/new-collaborator", "middleware": "web, admin, Closure", "as": "new-collaborator.index", "operation": "list", "controller": "App\\Http\\Controllers\\Admin\\CollaboratorCrudController@index<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FHttp%2FControllers%2FOperations%2FListOperation.php&line=54\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "admin", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FHttp%2FControllers%2FOperations%2FListOperation.php&line=54\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/backpack/crud/src/app/Http/Controllers/Operations/ListOperation.php:54-63</a>"}, "queries": {"count": 3, "nb_statements": 3, "nb_visible_statements": 3, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.04559, "accumulated_duration_str": "45.59ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "app/Services/Admin/PermissionService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\PermissionService.php", "line": 18}, {"index": 18, "namespace": null, "name": "app/Services/Admin/PermissionService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\PermissionService.php", "line": 31}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/CollaboratorCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\CollaboratorCrudController.php", "line": 34}], "start": **********.002004, "duration": 0.02027, "duration_str": "20.27ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `id` = ? limit 1", "hash": "ac0ef66d2bf40f3993c8e40de021b0d99da887a13adec01b9fbb0601a2bf69c5"}, "start_percent": 0, "width_percent": 44.462}, {"sql": "select `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Services/Admin/PermissionService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\PermissionService.php", "line": 19}, {"index": 19, "namespace": null, "name": "app/Services/Admin/PermissionService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\PermissionService.php", "line": 31}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/CollaboratorCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\CollaboratorCrudController.php", "line": 34}, {"index": 21, "namespace": null, "name": "vendor/backpack/crud/src/app/Http/Controllers/CrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Http\\Controllers\\CrudController.php", "line": 41}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 162}], "start": **********.0312228, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "PermissionService.php:19", "source": {"index": 18, "namespace": null, "name": "app/Services/Admin/PermissionService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\PermissionService.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FServices%2FAdmin%2FPermissionService.php&line=19", "ajax": false, "filename": "PermissionService.php", "line": "19"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)", "hash": "09b63fe1be07508dfb18b73a990c5ee0249dd740fb6501945ccdb8043c3b971c"}, "start_percent": 44.462, "width_percent": 1.272}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'hri_recland_product' and table_name = 'users'", "type": "query", "params": [], "bindings": ["hri_recland_product", "users"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/ColumnsProtectedMethods.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\ColumnsProtectedMethods.php", "line": 312}, {"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Columns.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Columns.php", "line": 382}, {"index": 12, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Columns.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Columns.php", "line": 69}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/Admin/CollaboratorCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\CollaboratorCrudController.php", "line": 59}, {"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/app/Http/Controllers/CrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Http\\Controllers\\CrudController.php", "line": 121}], "start": **********.039197, "duration": 0.024739999999999998, "duration_str": "24.74ms", "memory": 0, "memory_str": null, "filename": "ColumnsProtectedMethods.php:312", "source": {"index": 10, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/ColumnsProtectedMethods.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\ColumnsProtectedMethods.php", "line": 312}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FLibrary%2FCrudPanel%2FTraits%2FColumnsProtectedMethods.php&line=312", "ajax": false, "filename": "ColumnsProtectedMethods.php", "line": "312"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select column_name as `column_name` from information_schema.columns where table_schema = ? and table_name = ?", "hash": "2f932a8ab5559dc2e718e2169d292b7c251235f387a3834f8b6fb4b996f87912"}, "start_percent": 45.734, "width_percent": 54.266}]}, "models": {"data": {"App\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Role": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 2}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "67FrIiAKRzqdDDYE9lpf1v63S50ahQba2svcMgmp", "_previous": "array:1 [\n  \"url\" => \"http://recland.local/admin/jobs-management\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://recland.local/admin/new-collaborator", "action_name": "new-collaborator.index", "controller_action": "App\\Http\\Controllers\\Admin\\CollaboratorCrudController@index", "uri": "GET admin/new-collaborator", "operation": "list", "controller": "App\\Http\\Controllers\\Admin\\CollaboratorCrudController@index<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FHttp%2FControllers%2FOperations%2FListOperation.php&line=54\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "admin", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FHttp%2FControllers%2FOperations%2FListOperation.php&line=54\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/backpack/crud/src/app/Http/Controllers/Operations/ListOperation.php:54-63</a>", "middleware": "web, admin", "duration": "573ms", "peak_memory": "50MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-2062256121 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2062256121\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1048736341 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1048736341\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1608923629 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"49 characters\">http://recland.local/admin/collaborator/7486/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,vi;q=0.8,nl;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1257 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkVQWDFUUXZDWklYd0g2aDlFMWZVR1E9PSIsInZhbHVlIjoidld6L1Q0dTNXQUtocWttcVV2OU1HdDBXT2o5cEsxY0NMd0I5OWRVQUtBUEl5Mnp1Qk1lY010aUNndkRQeUMvcDByUVJaNTErdGdCWTQ4SGJTamxXWWtkSzJxRURERWZ5WGtVTDdsNERwTjQ1UWtRajhGNDB2UFhMUWlaWndkL2giLCJtYWMiOiI5NThjN2FmZjM0ZGU3YjY3OTBlZjlhZGU1OWZiNzdhMGRjNGRiNDg1NTVkZjVlYzI0YTNjMTc0ZTRjYTY3YjhhIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6ImxrTlJuYi94S01LMWM0QUlBRDh1b2c9PSIsInZhbHVlIjoiRlB2a0JQY0NuYU9FTVJXVHdIekRhcmU2VWRXS0pwbHRKcXV3MnRYWnI0N1dyRnE0R2poa0VEM1UvNkF0OUlWN1JpTDdpV1JJNEt5NGxMT3RiL1paamZZNGZYbEVrT0RuNzU1KzB3WDJHWCsxV1RFak5Gd0l0blFMcjdQVDJNM2siLCJtYWMiOiJiZjIxMGYzYTZlMmI1Yjc3NWJmYmExZGI2N2I4YjIyOTUzZTRjZmVhYjZjNDAzNjBkMDBjZDQ0NTQ0MjRhNGJhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1608923629\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-220017910 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|cm8f4SgFu8wrAd7wPR9b0gsQwQ8YYGlGSWS8Y9hgKQXIsj7qT8VG3KwC3dMx|$2y$10$iTSniY9ReMSnjm6GaX0bRuGWPNsyk9pQat6r8dKHdyoQSgwMkVojS</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">67FrIiAKRzqdDDYE9lpf1v63S50ahQba2svcMgmp</span>\"\n  \"<span class=sf-dump-key>recland_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aK6CrmwILvnr50B6VrqyQAI9QjCWvYUdKGChzrVE</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-220017910\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2047682461 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 04 Aug 2025 07:08:32 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2047682461\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1362762342 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">67FrIiAKRzqdDDYE9lpf1v63S50ahQba2svcMgmp</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://recland.local/admin/jobs-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1362762342\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://recland.local/admin/new-collaborator", "action_name": "new-collaborator.index", "controller_action": "App\\Http\\Controllers\\Admin\\CollaboratorCrudController@index"}, "badge": null}}