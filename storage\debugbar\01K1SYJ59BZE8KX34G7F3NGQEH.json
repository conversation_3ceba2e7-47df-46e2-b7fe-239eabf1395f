{"__meta": {"id": "01K1SYJ59BZE8KX34G7F3NGQEH", "datetime": "2025-08-04 14:08:31", "utime": **********.915906, "method": "GET", "uri": "/admin/jobs-management", "ip": "127.0.0.1"}, "php": {"version": "8.1.6", "interface": "apache2handler"}, "messages": {"count": 4, "messages": [{"message": "[14:08:31] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `job`\\n-- \",\n    \"Time:\": 12.67\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.09709, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:31] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `job` where `is_active` = '1'\\n-- \",\n    \"Time:\": 7.94\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.106569, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:31] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `job` where month(`created_at`) = '08' and year(`created_at`) = '2025'\\n-- \",\n    \"Time:\": 8.14\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.115859, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:31] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `id` = '1' limit 1\\n-- \",\n    \"Time:\": 0.99\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.900181, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 3, "start": **********.627715, "end": **********.915931, "duration": 1.2882158756256104, "duration_str": "1.29s", "measures": [{"label": "Booting", "start": **********.627715, "relative_start": 0, "end": **********.982165, "relative_end": **********.982165, "duration": 0.***************, "duration_str": "354ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.982175, "relative_start": 0.****************, "end": **********.915934, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "934ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.99314, "relative_start": 0.*****************, "end": **********.996387, "relative_end": **********.996387, "duration": 0.0032470226287841797, "duration_str": "3.25ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "40MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 38, "nb_templates": 38, "templates": [{"name": "1x crud::list", "param_count": null, "params": [], "start": **********.173871, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/list.blade.phpcrud::list", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Fcrud%2Flist.blade.php&line=1", "ajax": false, "filename": "list.blade.php", "line": "?"}, "render_count": 1, "name_original": "crud::list"}, {"name": "1x crud::inc.button_stack", "param_count": null, "params": [], "start": **********.433658, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/inc/button_stack.blade.phpcrud::inc.button_stack", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Fcrud%2Finc%2Fbutton_stack.blade.php&line=1", "ajax": false, "filename": "button_stack.blade.php", "line": "?"}, "render_count": 1, "name_original": "crud::inc.button_stack"}, {"name": "1x crud::buttons.job.add_job_button", "param_count": null, "params": [], "start": **********.43556, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/buttons/job/add_job_button.blade.phpcrud::buttons.job.add_job_button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Fcrud%2Fbuttons%2Fjob%2Fadd_job_button.blade.php&line=1", "ajax": false, "filename": "add_job_button.blade.php", "line": "?"}, "render_count": 1, "name_original": "crud::buttons.job.add_job_button"}, {"name": "1x crud::inc.filters_navbar", "param_count": null, "params": [], "start": **********.449368, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/inc/filters_navbar.blade.phpcrud::inc.filters_navbar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Fcrud%2Finc%2Ffilters_navbar.blade.php&line=1", "ajax": false, "filename": "filters_navbar.blade.php", "line": "?"}, "render_count": 1, "name_original": "crud::inc.filters_navbar"}, {"name": "4x backpack.pro::filters.dropdown", "param_count": null, "params": [], "start": **********.451167, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\packages\\backpack-pro\\src/../resources/views/filters/dropdown.blade.phpbackpack.pro::filters.dropdown", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fpackages%2Fbackpack-pro%2Fresources%2Fviews%2Ffilters%2Fdropdown.blade.php&line=1", "ajax": false, "filename": "dropdown.blade.php", "line": "?"}, "render_count": 4, "name_original": "backpack.pro::filters.dropdown"}, {"name": "2x backpack.pro::filters.select2_ajax", "param_count": null, "params": [], "start": **********.454452, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\packages\\backpack-pro\\src/../resources/views/filters/select2_ajax.blade.phpbackpack.pro::filters.select2_ajax", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fpackages%2Fbackpack-pro%2Fresources%2Fviews%2Ffilters%2Fselect2_ajax.blade.php&line=1", "ajax": false, "filename": "select2_ajax.blade.php", "line": "?"}, "render_count": 2, "name_original": "backpack.pro::filters.select2_ajax"}, {"name": "2x backpack.pro::filters.date_range", "param_count": null, "params": [], "start": **********.680822, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\packages\\backpack-pro\\src/../resources/views/filters/date_range.blade.phpbackpack.pro::filters.date_range", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fpackages%2Fbackpack-pro%2Fresources%2Fviews%2Ffilters%2Fdate_range.blade.php&line=1", "ajax": false, "filename": "date_range.blade.php", "line": "?"}, "render_count": 2, "name_original": "backpack.pro::filters.date_range"}, {"name": "1x crud::inc.datatables_logic", "param_count": null, "params": [], "start": **********.686542, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/inc/datatables_logic.blade.phpcrud::inc.datatables_logic", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Fcrud%2Finc%2Fdatatables_logic.blade.php&line=1", "ajax": false, "filename": "datatables_logic.blade.php", "line": "?"}, "render_count": 1, "name_original": "crud::inc.datatables_logic"}, {"name": "1x crud::inc.export_buttons", "param_count": null, "params": [], "start": **********.693107, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/inc/export_buttons.blade.phpcrud::inc.export_buttons", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Fcrud%2Finc%2Fexport_buttons.blade.php&line=1", "ajax": false, "filename": "export_buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "crud::inc.export_buttons"}, {"name": "1x crud::inc.details_row_logic", "param_count": null, "params": [], "start": **********.694436, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/inc/details_row_logic.blade.phpcrud::inc.details_row_logic", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Fcrud%2Finc%2Fdetails_row_logic.blade.php&line=1", "ajax": false, "filename": "details_row_logic.blade.php", "line": "?"}, "render_count": 1, "name_original": "crud::inc.details_row_logic"}, {"name": "1x admin.layouts.app_backpack", "param_count": null, "params": [], "start": **********.695738, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/layouts/app_backpack.blade.phpadmin.layouts.app_backpack", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Flayouts%2Fapp_backpack.blade.php&line=1", "ajax": false, "filename": "app_backpack.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.layouts.app_backpack"}, {"name": "1x backpack::inc.head", "param_count": null, "params": [], "start": **********.697953, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\resources\\views\\base/inc/head.blade.phpbackpack::inc.head", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fbase%2Finc%2Fhead.blade.php&line=1", "ajax": false, "filename": "head.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack::inc.head"}, {"name": "5x backpack::inc.widgets", "param_count": null, "params": [], "start": **********.700719, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\resources\\views\\base/inc/widgets.blade.phpbackpack::inc.widgets", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fbase%2Finc%2Fwidgets.blade.php&line=1", "ajax": false, "filename": "widgets.blade.php", "line": "?"}, "render_count": 5, "name_original": "backpack::inc.widgets"}, {"name": "1x backpack::widgets.div", "param_count": null, "params": [], "start": **********.702107, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\resources\\views\\base/widgets/div.blade.phpbackpack::widgets.div", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fbase%2Fwidgets%2Fdiv.blade.php&line=1", "ajax": false, "filename": "div.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack::widgets.div"}, {"name": "3x backpack::widgets.progress", "param_count": null, "params": [], "start": **********.756245, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\resources\\views\\base/widgets/progress.blade.phpbackpack::widgets.progress", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fbase%2Fwidgets%2Fprogress.blade.php&line=1", "ajax": false, "filename": "progress.blade.php", "line": "?"}, "render_count": 3, "name_original": "backpack::widgets.progress"}, {"name": "3x backpack::widgets.inc.wrapper_start", "param_count": null, "params": [], "start": **********.823727, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\resources\\views\\base/widgets/inc/wrapper_start.blade.phpbackpack::widgets.inc.wrapper_start", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fbase%2Fwidgets%2Finc%2Fwrapper_start.blade.php&line=1", "ajax": false, "filename": "wrapper_start.blade.php", "line": "?"}, "render_count": 3, "name_original": "backpack::widgets.inc.wrapper_start"}, {"name": "3x backpack::widgets.inc.wrapper_end", "param_count": null, "params": [], "start": **********.862565, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\resources\\views\\base/widgets/inc/wrapper_end.blade.phpbackpack::widgets.inc.wrapper_end", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fbase%2Fwidgets%2Finc%2Fwrapper_end.blade.php&line=1", "ajax": false, "filename": "wrapper_end.blade.php", "line": "?"}, "render_count": 3, "name_original": "backpack::widgets.inc.wrapper_end"}, {"name": "1x admin.inc_layouts.side_bar", "param_count": null, "params": [], "start": **********.885665, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/inc_layouts/side_bar.blade.phpadmin.inc_layouts.side_bar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Finc_layouts%2Fside_bar.blade.php&line=1", "ajax": false, "filename": "side_bar.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.inc_layouts.side_bar"}, {"name": "1x admin.inc_layouts.header", "param_count": null, "params": [], "start": **********.910018, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/inc_layouts/header.blade.phpadmin.inc_layouts.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Finc_layouts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.inc_layouts.header"}, {"name": "1x admin.inc_layouts.footer", "param_count": null, "params": [], "start": **********.910988, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/inc_layouts/footer.blade.phpadmin.inc_layouts.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Finc_layouts%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.inc_layouts.footer"}, {"name": "1x backpack::inc.scripts", "param_count": null, "params": [], "start": **********.911726, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\resources\\views\\base/inc/scripts.blade.phpbackpack::inc.scripts", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fbase%2Finc%2Fscripts.blade.php&line=1", "ajax": false, "filename": "scripts.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack::inc.scripts"}, {"name": "1x backpack::inc.alerts", "param_count": null, "params": [], "start": **********.91257, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\resources\\views\\base/inc/alerts.blade.phpbackpack::inc.alerts", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fbase%2Finc%2Falerts.blade.php&line=1", "ajax": false, "filename": "alerts.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack::inc.alerts"}, {"name": "1x crud::inc.ajax_error_frame", "param_count": null, "params": [], "start": **********.913327, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/inc/ajax_error_frame.blade.phpcrud::inc.ajax_error_frame", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Fcrud%2Finc%2Fajax_error_frame.blade.php&line=1", "ajax": false, "filename": "ajax_error_frame.blade.php", "line": "?"}, "render_count": 1, "name_original": "crud::inc.ajax_error_frame"}]}, "route": {"uri": "GET admin/jobs-management", "middleware": "web, admin, Closure", "as": "jobs-management.index", "operation": "list", "controller": "App\\Http\\Controllers\\Admin\\JobCrudController@index<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FHttp%2FControllers%2FOperations%2FListOperation.php&line=54\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "admin", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FHttp%2FControllers%2FOperations%2FListOperation.php&line=54\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/backpack/crud/src/app/Http/Controllers/Operations/ListOperation.php:54-63</a>"}, "queries": {"count": 5, "nb_statements": 5, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0496, "accumulated_duration_str": "49.6ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select count(*) as aggregate from `job`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/JobCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobCrudController.php", "line": 400}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/JobCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobCrudController.php", "line": 87}, {"index": 20, "namespace": null, "name": "vendor/backpack/crud/src/app/Http/Controllers/CrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Http\\Controllers\\CrudController.php", "line": 121}, {"index": 21, "namespace": null, "name": "vendor/backpack/crud/src/app/Http/Controllers/CrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Http\\Controllers\\CrudController.php", "line": 42}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 162}], "start": **********.084656, "duration": 0.01267, "duration_str": "12.67ms", "memory": 0, "memory_str": null, "filename": "JobCrudController.php:400", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/JobCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobCrudController.php", "line": 400}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FJobCrudController.php&line=400", "ajax": false, "filename": "JobCrudController.php", "line": "400"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `job`", "hash": "69ce9914dd8df1c6e446718b922b4f3947f2c065f466a1e566a877e12e6c2618"}, "start_percent": 0, "width_percent": 25.544}, {"sql": "select count(*) as aggregate from `job` where `is_active` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/JobCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobCrudController.php", "line": 405}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/JobCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobCrudController.php", "line": 96}, {"index": 17, "namespace": null, "name": "vendor/backpack/crud/src/app/Http/Controllers/CrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Http\\Controllers\\CrudController.php", "line": 121}, {"index": 18, "namespace": null, "name": "vendor/backpack/crud/src/app/Http/Controllers/CrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Http\\Controllers\\CrudController.php", "line": 42}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 162}], "start": **********.09869, "duration": 0.007940000000000001, "duration_str": "7.94ms", "memory": 0, "memory_str": null, "filename": "JobCrudController.php:405", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/JobCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobCrudController.php", "line": 405}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FJobCrudController.php&line=405", "ajax": false, "filename": "JobCrudController.php", "line": "405"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `job` where `is_active` = ?", "hash": "5a65b1fd7af6e5bb70cc9d18537216394af83833e0cee12b8e9d1dcd5a7285e6"}, "start_percent": 25.544, "width_percent": 16.008}, {"sql": "select count(*) as aggregate from `job` where month(`created_at`) = '08' and year(`created_at`) = 2025", "type": "query", "params": [], "bindings": ["08", 2025], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/JobCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobCrudController.php", "line": 412}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/JobCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobCrudController.php", "line": 105}, {"index": 17, "namespace": null, "name": "vendor/backpack/crud/src/app/Http/Controllers/CrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Http\\Controllers\\CrudController.php", "line": 121}, {"index": 18, "namespace": null, "name": "vendor/backpack/crud/src/app/Http/Controllers/CrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Http\\Controllers\\CrudController.php", "line": 42}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 162}], "start": **********.107781, "duration": 0.008140000000000001, "duration_str": "8.14ms", "memory": 0, "memory_str": null, "filename": "JobCrudController.php:412", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/JobCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobCrudController.php", "line": 412}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FJobCrudController.php&line=412", "ajax": false, "filename": "JobCrudController.php", "line": "412"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `job` where month(`created_at`) = ? and year(`created_at`) = ?", "hash": "4df7cf857fe8dc883e4b10353b6aeb8eea03ae042dd6975a45f85d2e9969e3ff"}, "start_percent": 41.552, "width_percent": 16.411}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'hri_recland_product' and table_name = 'job'", "type": "query", "params": [], "bindings": ["hri_recland_product", "job"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/ColumnsProtectedMethods.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\ColumnsProtectedMethods.php", "line": 312}, {"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Columns.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Columns.php", "line": 382}, {"index": 12, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/CrudColumn.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\CrudColumn.php", "line": 53}, {"index": 13, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Columns.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Columns.php", "line": 423}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/JobCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobCrudController.php", "line": 115}], "start": **********.141506, "duration": 0.01986, "duration_str": "19.86ms", "memory": 0, "memory_str": null, "filename": "ColumnsProtectedMethods.php:312", "source": {"index": 10, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/ColumnsProtectedMethods.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\ColumnsProtectedMethods.php", "line": 312}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FLibrary%2FCrudPanel%2FTraits%2FColumnsProtectedMethods.php&line=312", "ajax": false, "filename": "ColumnsProtectedMethods.php", "line": "312"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select column_name as `column_name` from information_schema.columns where table_schema = ? and table_name = ?", "hash": "990a09a5fd80a4157b96a1522ddde828b43b0f6c71c90db92e7d7f75e92a356e"}, "start_percent": 57.964, "width_percent": 40.04}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": "view", "name": "admin.inc_layouts.side_bar", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/admin/inc_layouts/side_bar.blade.php", "line": 19}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.8993552, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `id` = ? limit 1", "hash": "ac0ef66d2bf40f3993c8e40de021b0d99da887a13adec01b9fbb0601a2bf69c5"}, "start_percent": 98.004, "width_percent": 1.996}]}, "models": {"data": {"App\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 1}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "67FrIiAKRzqdDDYE9lpf1v63S50ahQba2svcMgmp", "_previous": "array:1 [\n  \"url\" => \"http://recland.local/admin/collaborator/7486/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://recland.local/admin/jobs-management", "action_name": "jobs-management.index", "controller_action": "App\\Http\\Controllers\\Admin\\JobCrudController@index", "uri": "GET admin/jobs-management", "operation": "list", "controller": "App\\Http\\Controllers\\Admin\\JobCrudController@index<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FHttp%2FControllers%2FOperations%2FListOperation.php&line=54\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "admin", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FHttp%2FControllers%2FOperations%2FListOperation.php&line=54\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/backpack/crud/src/app/Http/Controllers/Operations/ListOperation.php:54-63</a>", "middleware": "web, admin", "duration": "1.29s", "peak_memory": "48MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-448734167 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-448734167\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1278907150 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1278907150\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1554476808 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"49 characters\">http://recland.local/admin/collaborator/7486/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,vi;q=0.8,nl;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1257 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkVQWDFUUXZDWklYd0g2aDlFMWZVR1E9PSIsInZhbHVlIjoidld6L1Q0dTNXQUtocWttcVV2OU1HdDBXT2o5cEsxY0NMd0I5OWRVQUtBUEl5Mnp1Qk1lY010aUNndkRQeUMvcDByUVJaNTErdGdCWTQ4SGJTamxXWWtkSzJxRURERWZ5WGtVTDdsNERwTjQ1UWtRajhGNDB2UFhMUWlaWndkL2giLCJtYWMiOiI5NThjN2FmZjM0ZGU3YjY3OTBlZjlhZGU1OWZiNzdhMGRjNGRiNDg1NTVkZjVlYzI0YTNjMTc0ZTRjYTY3YjhhIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6ImxrTlJuYi94S01LMWM0QUlBRDh1b2c9PSIsInZhbHVlIjoiRlB2a0JQY0NuYU9FTVJXVHdIekRhcmU2VWRXS0pwbHRKcXV3MnRYWnI0N1dyRnE0R2poa0VEM1UvNkF0OUlWN1JpTDdpV1JJNEt5NGxMT3RiL1paamZZNGZYbEVrT0RuNzU1KzB3WDJHWCsxV1RFak5Gd0l0blFMcjdQVDJNM2siLCJtYWMiOiJiZjIxMGYzYTZlMmI1Yjc3NWJmYmExZGI2N2I4YjIyOTUzZTRjZmVhYjZjNDAzNjBkMDBjZDQ0NTQ0MjRhNGJhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1554476808\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1694893020 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|cm8f4SgFu8wrAd7wPR9b0gsQwQ8YYGlGSWS8Y9hgKQXIsj7qT8VG3KwC3dMx|$2y$10$iTSniY9ReMSnjm6GaX0bRuGWPNsyk9pQat6r8dKHdyoQSgwMkVojS</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">67FrIiAKRzqdDDYE9lpf1v63S50ahQba2svcMgmp</span>\"\n  \"<span class=sf-dump-key>recland_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aK6CrmwILvnr50B6VrqyQAI9QjCWvYUdKGChzrVE</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1694893020\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1132948837 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 04 Aug 2025 07:08:31 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1132948837\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-540700612 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">67FrIiAKRzqdDDYE9lpf1v63S50ahQba2svcMgmp</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"49 characters\">http://recland.local/admin/collaborator/7486/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-540700612\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://recland.local/admin/jobs-management", "action_name": "jobs-management.index", "controller_action": "App\\Http\\Controllers\\Admin\\JobCrudController@index"}, "badge": null}}