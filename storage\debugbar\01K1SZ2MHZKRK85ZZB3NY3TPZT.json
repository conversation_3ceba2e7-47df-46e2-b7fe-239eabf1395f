{"__meta": {"id": "01K1SZ2MHZKRK85ZZB3NY3TPZT", "datetime": "2025-08-04 14:17:31", "utime": **********.840148, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.1.6", "interface": "apache2handler"}, "messages": {"count": 7, "messages": [{"message": "[14:17:23] LOG.warning: Optional parameter $type declared before required parameter $position is implicitly treated as a required parameter in D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\BannerService.php on line 19", "message_html": null, "is_string": false, "label": "warning", "time": **********.872934, "xdebug_link": null, "collector": "log"}, {"message": "[14:17:23] LOG.warning: Optional parameter $type declared before required parameter $position is implicitly treated as a required parameter in D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BannerRepository.php on line 53", "message_html": null, "is_string": false, "label": "warning", "time": **********.893688, "xdebug_link": null, "collector": "log"}, {"message": "[14:17:24] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http:\\/\\/recland.local', '', '[\\\"en-us\\\",\\\"en\\\",\\\"vi\\\",\\\"nl\\\"]', 'Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********', '{\\\"host\\\":[\\\"recland.local\\\"],\\\"connection\\\":[\\\"keep-alive\\\"],\\\"upgrade-insecure-requests\\\":[\\\"1\\\"],\\\"user-agent\\\":[\\\"Mozilla\\\\\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\\\\\/537.36 (KHTML, like Gecko) Chrome\\\\\\/********* Safari\\\\\\/537.36 Edg\\\\\\/*********\\\"],\\\"accept\\\":[\\\"text\\\\\\/html,application\\\\\\/xhtml+xml,application\\\\\\/xml;q=0.9,image\\\\\\/avif,image\\\\\\/webp,image\\\\\\/apng,*\\\\\\/*;q=0.8,application\\\\\\/signed-exchange;v=b3;q=0.7\\\"],\\\"accept-encoding\\\":[\\\"gzip, deflate\\\"],\\\"accept-language\\\":[\\\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\\\"],\\\"cookie\\\":[\\\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkE0TlFQNUszZjY3V3ZhbmwwZXViWXc9PSIsInZhbHVlIjoiT1I0MmNIZXBESDVOT3lqR2VJQkZqbWdnRlAvZTJuS3Q0MTN5NnU3MGhCU1M0WFdYTmpGRVV3em0vTC9GYXpnNkdTYUlyTmFacFFzeXhxWmpNME4rZHNtZzR2MlIrZncyOHJMUkowUnhqbC9YWGpuNGc2QVltb3FUaFpKTHJqMjkiLCJtYWMiOiIzZjJkZjI4ZTFmZDNjZGFkOWU0ODM2Mzc3ODA5NDU3MzExMzMwNDAxN2VhZWU1YzFhZWE0OWViYTA1NDk4YWJiIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6Ijd6emVFOXJMUG02aTBYaUFidFQ0NlE9PSIsInZhbHVlIjoiZUZxQlJyRmg4RTlDcDB6dHQwSlJ2NHVmcG9sTlhLSnFYbERxdFVmZy81QmluQnZabWprcmJMTWFLdmhEZW80dTJYbkMvV1V2c0FwTjdzTTYvY3czaS9UVEtFTmszUEQ2MlZ4K0JWQTJ5ODB4aXdCM1dnYTBmU3MzRVl6aGhkK1kiLCJtYWMiOiJmMzk4OTA0YzNlZTZhNDIyZTc2ODdkZThlNjBhYjM4M2E0MGEyZmNkNjgzNDg1YTM3YWIyNmFlYjY2OWI2MTc3IiwidGFnIjoiIn0%3D\\\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-04 14:17:24', '2025-08-04 14:17:24')\\n-- \",\n    \"Time:\": 13.48\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.486058, "xdebug_link": null, "collector": "log"}, {"message": "[14:17:24] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `seos` where `key` = 'home' limit 1\\n-- \",\n    \"Time:\": 0.73\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.491683, "xdebug_link": null, "collector": "log"}, {"message": "[14:17:30] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `testimonials` where `type` = '0' and `is_active` = '1'\\n-- \",\n    \"Time:\": 0.89\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.450933, "xdebug_link": null, "collector": "log"}, {"message": "[14:17:30] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `posts` order by `id` desc limit 1\\n-- \",\n    \"Time:\": 0.66\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.56016, "xdebug_link": null, "collector": "log"}, {"message": "[14:17:30] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `categories`\\n-- \",\n    \"Time:\": 0.72\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.945049, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 3, "start": **********.368506, "end": **********.840181, "duration": 8.471675157546997, "duration_str": "8.47s", "measures": [{"label": "Booting", "start": **********.368506, "relative_start": 0, "end": **********.765265, "relative_end": **********.765265, "duration": 0.***************, "duration_str": "397ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.765276, "relative_start": 0.*****************, "end": **********.840184, "relative_end": 2.86102294921875e-06, "duration": 8.***************, "duration_str": "8.07s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.778362, "relative_start": 0.*****************, "end": **********.782906, "relative_end": **********.782906, "duration": 0.*****************, "duration_str": "4.54ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "60MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 7, "nb_templates": 7, "templates": [{"name": "1x frontend.pages.home.index", "param_count": null, "params": [], "start": **********.584594, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/pages/home/<USER>", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Fpages%2Fhome%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.pages.home.index"}, {"name": "1x frontend.layouts.v2", "param_count": null, "params": [], "start": **********.907381, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/v2.blade.phpfrontend.layouts.v2", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Fv2.blade.php&line=1", "ajax": false, "filename": "v2.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.layouts.v2"}, {"name": "1x frontend.inc_layouts.v2.header_script", "param_count": null, "params": [], "start": **********.908341, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/v2/header_script.blade.phpfrontend.inc_layouts.v2.header_script", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Fv2%2Fheader_script.blade.php&line=1", "ajax": false, "filename": "header_script.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.inc_layouts.v2.header_script"}, {"name": "1x frontend.inc_layouts.v2.home_header", "param_count": null, "params": [], "start": **********.946808, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/v2/home_header.blade.phpfrontend.inc_layouts.v2.home_header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Fv2%2Fhome_header.blade.php&line=1", "ajax": false, "filename": "home_header.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.inc_layouts.v2.home_header"}, {"name": "1x frontend.inc_layouts.v2.hh_footer", "param_count": null, "params": [], "start": **********.732443, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/v2/hh_footer.blade.phpfrontend.inc_layouts.v2.hh_footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Fv2%2Fhh_footer.blade.php&line=1", "ajax": false, "filename": "hh_footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.inc_layouts.v2.hh_footer"}, {"name": "1x admin.inc_layouts.toast.message", "param_count": null, "params": [], "start": **********.766643, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/inc_layouts/toast/message.blade.phpadmin.inc_layouts.toast.message", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Finc_layouts%2Ftoast%2Fmessage.blade.php&line=1", "ajax": false, "filename": "message.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.inc_layouts.toast.message"}, {"name": "1x frontend.inc_layouts.v2.contactus", "param_count": null, "params": [], "start": **********.76744, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/v2/contactus.blade.phpfrontend.inc_layouts.v2.contactus", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Fv2%2Fcontactus.blade.php&line=1", "ajax": false, "filename": "contactus.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.inc_layouts.v2.contactus"}]}, "route": {"uri": "GET /", "middleware": "web, localization, visit-website", "controller": "App\\Http\\Controllers\\Frontend\\HomeController@index<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=50\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Frontend", "as": "home", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=50\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/HomeController.php:50-67</a>"}, "queries": {"count": 5, "nb_statements": 5, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.016479999999999998, "accumulated_duration_str": "16.48ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http://recland.local', '', '[\\\"en-us\\\",\\\"en\\\",\\\"vi\\\",\\\"nl\\\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\\\"host\\\":[\\\"recland.local\\\"],\\\"connection\\\":[\\\"keep-alive\\\"],\\\"upgrade-insecure-requests\\\":[\\\"1\\\"],\\\"user-agent\\\":[\\\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\\\"],\\\"accept\\\":[\\\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\\\"],\\\"accept-encoding\\\":[\\\"gzip, deflate\\\"],\\\"accept-language\\\":[\\\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\\\"],\\\"cookie\\\":[\\\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkE0TlFQNUszZjY3V3ZhbmwwZXViWXc9PSIsInZhbHVlIjoiT1I0MmNIZXBESDVOT3lqR2VJQkZqbWdnRlAvZTJuS3Q0MTN5NnU3MGhCU1M0WFdYTmpGRVV3em0vTC9GYXpnNkdTYUlyTmFacFFzeXhxWmpNME4rZHNtZzR2MlIrZncyOHJMUkowUnhqbC9YWGpuNGc2QVltb3FUaFpKTHJqMjkiLCJtYWMiOiIzZjJkZjI4ZTFmZDNjZGFkOWU0ODM2Mzc3ODA5NDU3MzExMzMwNDAxN2VhZWU1YzFhZWE0OWViYTA1NDk4YWJiIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6Ijd6emVFOXJMUG02aTBYaUFidFQ0NlE9PSIsInZhbHVlIjoiZUZxQlJyRmg4RTlDcDB6dHQwSlJ2NHVmcG9sTlhLSnFYbERxdFVmZy81QmluQnZabWprcmJMTWFLdmhEZW80dTJYbkMvV1V2c0FwTjdzTTYvY3czaS9UVEtFTmszUEQ2MlZ4K0JWQTJ5ODB4aXdCM1dnYTBmU3MzRVl6aGhkK1kiLCJtYWMiOiJmMzk4OTA0YzNlZTZhNDIyZTc2ODdkZThlNjBhYjM4M2E0MGEyZmNkNjgzNDg1YTM3YWIyNmFlYjY2OWI2MTc3IiwidGFnIjoiIn0%3D\\\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-04 14:17:24', '2025-08-04 14:17:24')", "type": "query", "params": [], "bindings": ["GET", "[]", "http://recland.local", null, "[\"en-us\",\"en\",\"vi\",\"nl\"]", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkE0TlFQNUszZjY3V3ZhbmwwZXViWXc9PSIsInZhbHVlIjoiT1I0MmNIZXBESDVOT3lqR2VJQkZqbWdnRlAvZTJuS3Q0MTN5NnU3MGhCU1M0WFdYTmpGRVV3em0vTC9GYXpnNkdTYUlyTmFacFFzeXhxWmpNME4rZHNtZzR2MlIrZncyOHJMUkowUnhqbC9YWGpuNGc2QVltb3FUaFpKTHJqMjkiLCJtYWMiOiIzZjJkZjI4ZTFmZDNjZGFkOWU0ODM2Mzc3ODA5NDU3MzExMzMwNDAxN2VhZWU1YzFhZWE0OWViYTA1NDk4YWJiIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6Ijd6emVFOXJMUG02aTBYaUFidFQ0NlE9PSIsInZhbHVlIjoiZUZxQlJyRmg4RTlDcDB6dHQwSlJ2NHVmcG9sTlhLSnFYbERxdFVmZy81QmluQnZabWprcmJMTWFLdmhEZW80dTJYbkMvV1V2c0FwTjdzTTYvY3czaS9UVEtFTmszUEQ2MlZ4K0JWQTJ5ODB4aXdCM1dnYTBmU3MzRVl6aGhkK1kiLCJtYWMiOiJmMzk4OTA0YzNlZTZhNDIyZTc2ODdkZThlNjBhYjM4M2E0MGEyZmNkNjgzNDg1YTM3YWIyNmFlYjY2OWI2MTc3IiwidGFnIjoiIn0%3D\"]}", "WebKit", "Windows", "Edge", "127.0.0.1", null, null, "2025-08-04 14:17:24", "2025-08-04 14:17:24"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/shetabit/visitor/src/Visitor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\shetabit\\visitor\\src\\Visitor.php", "line": 245}, {"index": 22, "namespace": "middleware", "name": "visit-website", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\VisitWebsiteMiddleware.php", "line": 20}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 24, "namespace": "middleware", "name": "localization", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\Localization.php", "line": 24}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.4728901, "duration": 0.01348, "duration_str": "13.48ms", "memory": 0, "memory_str": null, "filename": "Visitor.php:245", "source": {"index": 21, "namespace": null, "name": "vendor/shetabit/visitor/src/Visitor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\shetabit\\visitor\\src\\Visitor.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fshetabit%2Fvisitor%2Fsrc%2FVisitor.php&line=245", "ajax": false, "filename": "Visitor.php", "line": "245"}, "connection": "hri_recland_product", "explain": null, "start_percent": 0, "width_percent": 81.796}, {"sql": "select * from `seos` where `key` = 'home' limit 1", "type": "query", "params": [], "bindings": ["home"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/SeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SeoRepository.php", "line": 14}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/SeoService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\SeoService.php", "line": 22}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 53}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.491018, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "SeoRepository.php:14", "source": {"index": 15, "namespace": null, "name": "app/Repositories/SeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SeoRepository.php", "line": 14}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSeoRepository.php&line=14", "ajax": false, "filename": "SeoRepository.php", "line": "14"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `seos` where `key` = ? limit 1", "hash": "4fc5d5cd92e1edd22931af0dfab11a79cf14ebae41ccdc8e68a778258c2710a5"}, "start_percent": 81.796, "width_percent": 4.43}, {"sql": "select * from `testimonials` where `type` = 0 and `is_active` = 1", "type": "query", "params": [], "bindings": [0, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/TestimonialRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\TestimonialRepository.php", "line": 54}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/TestimonialService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\TestimonialService.php", "line": 20}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 56}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.450312, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "TestimonialRepository.php:54", "source": {"index": 14, "namespace": null, "name": "app/Repositories/TestimonialRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\TestimonialRepository.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FTestimonialRepository.php&line=54", "ajax": false, "filename": "TestimonialRepository.php", "line": "54"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `testimonials` where `type` = ? and `is_active` = ?", "hash": "381c6702a1846dfebb02526700a260eb3c240f7b2e71910852e1c5484a87aeff"}, "start_percent": 86.226, "width_percent": 5.4}, {"sql": "select * from `posts` order by `id` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/PostRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\PostRepository.php", "line": 207}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/PostService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\PostService.php", "line": 73}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.5596611, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "PostRepository.php:207", "source": {"index": 15, "namespace": null, "name": "app/Repositories/PostRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\PostRepository.php", "line": 207}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FPostRepository.php&line=207", "ajax": false, "filename": "PostRepository.php", "line": "207"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `posts` order by `id` desc limit 1", "hash": "03c71db9530baa80c7f279b6ce87376e4f31e539a6b4c82a75ace86a94fe60b2"}, "start_percent": 91.626, "width_percent": 4.005}, {"sql": "select * from `categories`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Providers\\AppServiceProvider.php", "line": 55}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}, {"index": 21, "namespace": "view", "name": "frontend.layouts.v2", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/v2.blade.php", "line": 46}], "start": **********.944409, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:55", "source": {"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Providers\\AppServiceProvider.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FProviders%2FAppServiceProvider.php&line=55", "ajax": false, "filename": "AppServiceProvider.php", "line": "55"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `categories`", "hash": "7e13c5da69976ceecb727777c5a91eea3e71bf2b1b50247c4cb202cd9a066582"}, "start_percent": 95.631, "width_percent": 4.369}]}, "models": {"data": {"App\\Models\\Category": {"retrieved": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\Testimonial": {"retrieved": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FTestimonial.php&line=1", "ajax": false, "filename": "Testimonial.php", "line": "?"}}, "Shetabit\\Visitor\\Models\\Visit": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fshetabit%2Fvisitor%2Fsrc%2FModels%2FVisit.php&line=1", "ajax": false, "filename": "Visit.php", "line": "?"}}, "App\\Models\\Seo": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FSeo.php&line=1", "ajax": false, "filename": "Seo.php", "line": "?"}}, "App\\Models\\Post": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FPost.php&line=1", "ajax": false, "filename": "Post.php", "line": "?"}}}, "count": 11, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"created": 1, "retrieved": 10}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "67FrIiAKRzqdDDYE9lpf1v63S50ahQba2svcMgmp", "_previous": "array:1 [\n  \"url\" => \"http://recland.local/admin/new-collaborator\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://recland.local", "action_name": "home", "controller_action": "App\\Http\\Controllers\\Frontend\\HomeController@index", "uri": "GET /", "controller": "App\\Http\\Controllers\\Frontend\\HomeController@index<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=50\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Frontend", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=50\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/HomeController.php:50-67</a>", "middleware": "web, localization, visit-website", "duration": "8.47s", "peak_memory": "64MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-512251538 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-512251538\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1900322173 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1900322173\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-97183221 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,vi;q=0.8,nl;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1257 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkE0TlFQNUszZjY3V3ZhbmwwZXViWXc9PSIsInZhbHVlIjoiT1I0MmNIZXBESDVOT3lqR2VJQkZqbWdnRlAvZTJuS3Q0MTN5NnU3MGhCU1M0WFdYTmpGRVV3em0vTC9GYXpnNkdTYUlyTmFacFFzeXhxWmpNME4rZHNtZzR2MlIrZncyOHJMUkowUnhqbC9YWGpuNGc2QVltb3FUaFpKTHJqMjkiLCJtYWMiOiIzZjJkZjI4ZTFmZDNjZGFkOWU0ODM2Mzc3ODA5NDU3MzExMzMwNDAxN2VhZWU1YzFhZWE0OWViYTA1NDk4YWJiIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6Ijd6emVFOXJMUG02aTBYaUFidFQ0NlE9PSIsInZhbHVlIjoiZUZxQlJyRmg4RTlDcDB6dHQwSlJ2NHVmcG9sTlhLSnFYbERxdFVmZy81QmluQnZabWprcmJMTWFLdmhEZW80dTJYbkMvV1V2c0FwTjdzTTYvY3czaS9UVEtFTmszUEQ2MlZ4K0JWQTJ5ODB4aXdCM1dnYTBmU3MzRVl6aGhkK1kiLCJtYWMiOiJmMzk4OTA0YzNlZTZhNDIyZTc2ODdkZThlNjBhYjM4M2E0MGEyZmNkNjgzNDg1YTM3YWIyNmFlYjY2OWI2MTc3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-97183221\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|cm8f4SgFu8wrAd7wPR9b0gsQwQ8YYGlGSWS8Y9hgKQXIsj7qT8VG3KwC3dMx|$2y$10$iTSniY9ReMSnjm6GaX0bRuGWPNsyk9pQat6r8dKHdyoQSgwMkVojS</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">67FrIiAKRzqdDDYE9lpf1v63S50ahQba2svcMgmp</span>\"\n  \"<span class=sf-dump-key>recland_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aK6CrmwILvnr50B6VrqyQAI9QjCWvYUdKGChzrVE</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-107691266 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 04 Aug 2025 07:17:30 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-107691266\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-131538282 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">67FrIiAKRzqdDDYE9lpf1v63S50ahQba2svcMgmp</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://recland.local/admin/new-collaborator</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-131538282\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://recland.local", "action_name": "home", "controller_action": "App\\Http\\Controllers\\Frontend\\HomeController@index"}, "badge": null}}