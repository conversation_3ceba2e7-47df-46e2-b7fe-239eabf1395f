[2025-08-04 08:58:46] local.ERROR: __VSCODE_LARAVEL_STARTUP_ERROR__: include(D:\Projects\HRI\RecLand\vendor\composer/../backpack/pro/src/AddonServiceProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (Error(code: 0): __VSCODE_LARAVEL_STARTUP_ERROR__: include(D:\\Projects\\HRI\\RecLand\\vendor\\composer/../backpack/pro/src/AddonServiceProvider.php): Failed to open stream: No such file or directory at D:\\Projects\\HRI\\RecLand\\vendor\\_laravel_ide\\discover-77e9e6a2445455b87d4f4cd834e4e23d.php:33)
[stacktrace]
#0 D:\\Projects\\HRI\\RecLand\\vendor\\_laravel_ide\\discover-77e9e6a2445455b87d4f4cd834e4e23d.php(62): LaravelVsCode::startupError(Object(ErrorException))
#1 {main}
"} 
[2025-08-04 08:58:46] local.ERROR: __VSCODE_LARAVEL_STARTUP_ERROR__: include(D:\Projects\HRI\RecLand\vendor\composer/../backpack/pro/src/AddonServiceProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (Error(code: 0): __VSCODE_LARAVEL_STARTUP_ERROR__: include(D:\\Projects\\HRI\\RecLand\\vendor\\composer/../backpack/pro/src/AddonServiceProvider.php): Failed to open stream: No such file or directory at D:\\Projects\\HRI\\RecLand\\vendor\\_laravel_ide\\discover-5802e590a92e23606bb54d2909564178.php:33)
[stacktrace]
#0 D:\\Projects\\HRI\\RecLand\\vendor\\_laravel_ide\\discover-5802e590a92e23606bb54d2909564178.php(62): LaravelVsCode::startupError(Object(ErrorException))
#1 {main}
"} 
[2025-08-04 08:58:46] local.ERROR: __VSCODE_LARAVEL_STARTUP_ERROR__: include(D:\Projects\HRI\RecLand\vendor\composer/../backpack/pro/src/AddonServiceProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (Error(code: 0): __VSCODE_LARAVEL_STARTUP_ERROR__: include(D:\\Projects\\HRI\\RecLand\\vendor\\composer/../backpack/pro/src/AddonServiceProvider.php): Failed to open stream: No such file or directory at D:\\Projects\\HRI\\RecLand\\vendor\\_laravel_ide\\discover-d769f81c5eaf96f1f253a56481405376.php:33)
[stacktrace]
#0 D:\\Projects\\HRI\\RecLand\\vendor\\_laravel_ide\\discover-d769f81c5eaf96f1f253a56481405376.php(62): LaravelVsCode::startupError(Object(ErrorException))
#1 {main}
"} 
[2025-08-04 08:58:48] local.ERROR: __VSCODE_LARAVEL_STARTUP_ERROR__: include(D:\Projects\HRI\RecLand\vendor\composer/../backpack/pro/src/AddonServiceProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (Error(code: 0): __VSCODE_LARAVEL_STARTUP_ERROR__: include(D:\\Projects\\HRI\\RecLand\\vendor\\composer/../backpack/pro/src/AddonServiceProvider.php): Failed to open stream: No such file or directory at D:\\Projects\\HRI\\RecLand\\vendor\\_laravel_ide\\discover-67043687ec5f16a5c4357b3dc049d2d5.php:33)
[stacktrace]
#0 D:\\Projects\\HRI\\RecLand\\vendor\\_laravel_ide\\discover-67043687ec5f16a5c4357b3dc049d2d5.php(62): LaravelVsCode::startupError(Object(ErrorException))
#1 {main}
"} 
[2025-08-04 08:58:48] local.ERROR: __VSCODE_LARAVEL_STARTUP_ERROR__: include(D:\Projects\HRI\RecLand\vendor\composer/../backpack/pro/src/AddonServiceProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (Error(code: 0): __VSCODE_LARAVEL_STARTUP_ERROR__: include(D:\\Projects\\HRI\\RecLand\\vendor\\composer/../backpack/pro/src/AddonServiceProvider.php): Failed to open stream: No such file or directory at D:\\Projects\\HRI\\RecLand\\vendor\\_laravel_ide\\discover-9abd86d674d0ba851f4ac7b31349196a.php:33)
[stacktrace]
#0 D:\\Projects\\HRI\\RecLand\\vendor\\_laravel_ide\\discover-9abd86d674d0ba851f4ac7b31349196a.php(62): LaravelVsCode::startupError(Object(ErrorException))
#1 {main}
"} 
[2025-08-04 08:58:48] local.ERROR: __VSCODE_LARAVEL_STARTUP_ERROR__: include(D:\Projects\HRI\RecLand\vendor\composer/../backpack/pro/src/AddonServiceProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (Error(code: 0): __VSCODE_LARAVEL_STARTUP_ERROR__: include(D:\\Projects\\HRI\\RecLand\\vendor\\composer/../backpack/pro/src/AddonServiceProvider.php): Failed to open stream: No such file or directory at D:\\Projects\\HRI\\RecLand\\vendor\\_laravel_ide\\discover-d769f81c5eaf96f1f253a56481405376.php:33)
[stacktrace]
#0 D:\\Projects\\HRI\\RecLand\\vendor\\_laravel_ide\\discover-d769f81c5eaf96f1f253a56481405376.php(62): LaravelVsCode::startupError(Object(ErrorException))
#1 {main}
"} 
[2025-08-04 08:58:48] local.ERROR: __VSCODE_LARAVEL_STARTUP_ERROR__: include(D:\Projects\HRI\RecLand\vendor\composer/../backpack/pro/src/AddonServiceProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (Error(code: 0): __VSCODE_LARAVEL_STARTUP_ERROR__: include(D:\\Projects\\HRI\\RecLand\\vendor\\composer/../backpack/pro/src/AddonServiceProvider.php): Failed to open stream: No such file or directory at D:\\Projects\\HRI\\RecLand\\vendor\\_laravel_ide\\discover-5802e590a92e23606bb54d2909564178.php:33)
[stacktrace]
#0 D:\\Projects\\HRI\\RecLand\\vendor\\_laravel_ide\\discover-5802e590a92e23606bb54d2909564178.php(62): LaravelVsCode::startupError(Object(ErrorException))
#1 {main}
"} 
[2025-08-04 08:58:49] local.ERROR: __VSCODE_LARAVEL_STARTUP_ERROR__: include(D:\Projects\HRI\RecLand\vendor\composer/../backpack/pro/src/AddonServiceProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (Error(code: 0): __VSCODE_LARAVEL_STARTUP_ERROR__: include(D:\\Projects\\HRI\\RecLand\\vendor\\composer/../backpack/pro/src/AddonServiceProvider.php): Failed to open stream: No such file or directory at D:\\Projects\\HRI\\RecLand\\vendor\\_laravel_ide\\discover-9abd86d674d0ba851f4ac7b31349196a.php:33)
[stacktrace]
#0 D:\\Projects\\HRI\\RecLand\\vendor\\_laravel_ide\\discover-9abd86d674d0ba851f4ac7b31349196a.php(62): LaravelVsCode::startupError(Object(ErrorException))
#1 {main}
"} 
[2025-08-04 08:58:49] local.ERROR: __VSCODE_LARAVEL_STARTUP_ERROR__: include(D:\Projects\HRI\RecLand\vendor\composer/../backpack/pro/src/AddonServiceProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (Error(code: 0): __VSCODE_LARAVEL_STARTUP_ERROR__: include(D:\\Projects\\HRI\\RecLand\\vendor\\composer/../backpack/pro/src/AddonServiceProvider.php): Failed to open stream: No such file or directory at D:\\Projects\\HRI\\RecLand\\vendor\\_laravel_ide\\discover-67043687ec5f16a5c4357b3dc049d2d5.php:33)
[stacktrace]
#0 D:\\Projects\\HRI\\RecLand\\vendor\\_laravel_ide\\discover-67043687ec5f16a5c4357b3dc049d2d5.php(62): LaravelVsCode::startupError(Object(ErrorException))
#1 {main}
"} 
[2025-08-04 08:58:51] local.ERROR: include(D:\Projects\HRI\RecLand\vendor\composer/../backpack/pro/src/AddonServiceProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\Projects\\HRI\\RecLand\\vendor\\composer/../backpack/pro/src/AddonServiceProvider.php): Failed to open stream: No such file or directory at D:\\Projects\\HRI\\RecLand\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(272): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\Proj...', 'D:\\\\Projects\\\\HRI...', 571)
#1 D:\\Projects\\HRI\\RecLand\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\Proj...', 'D:\\\\Projects\\\\HRI...', 571)
#2 D:\\Projects\\HRI\\RecLand\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\Projects\\\\HRI...')
#3 D:\\Projects\\HRI\\RecLand\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\Projects\\\\HRI...')
#4 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(775): Composer\\Autoload\\ClassLoader->loadClass('Backpack\\\\Pro\\\\Ad...')
#5 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(709): Illuminate\\Foundation\\Application->resolveProvider('Backpack\\\\Pro\\\\Ad...')
#6 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register('Backpack\\\\Pro\\\\Ad...')
#7 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(689): Illuminate\\Foundation\\ProviderRepository->load(Array)
#8 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#9 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(265): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#10 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(383): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#11 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(153): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 {main}
"} 
[2025-08-04 08:58:51] local.ERROR: include(D:\Projects\HRI\RecLand\vendor\composer/../backpack/pro/src/AddonServiceProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\Projects\\HRI\\RecLand\\vendor\\composer/../backpack/pro/src/AddonServiceProvider.php): Failed to open stream: No such file or directory at D:\\Projects\\HRI\\RecLand\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(272): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\Proj...', 'D:\\\\Projects\\\\HRI...', 571)
#1 D:\\Projects\\HRI\\RecLand\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\Proj...', 'D:\\\\Projects\\\\HRI...', 571)
#2 D:\\Projects\\HRI\\RecLand\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\Projects\\\\HRI...')
#3 D:\\Projects\\HRI\\RecLand\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\Projects\\\\HRI...')
#4 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(775): Composer\\Autoload\\ClassLoader->loadClass('Backpack\\\\Pro\\\\Ad...')
#5 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(709): Illuminate\\Foundation\\Application->resolveProvider('Backpack\\\\Pro\\\\Ad...')
#6 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register('Backpack\\\\Pro\\\\Ad...')
#7 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(689): Illuminate\\Foundation\\ProviderRepository->load(Array)
#8 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#9 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(265): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#10 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(383): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#11 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(153): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 {main}
"} 
[2025-08-04 08:58:51] local.ERROR: include(D:\Projects\HRI\RecLand\vendor\composer/../backpack/pro/src/AddonServiceProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\Projects\\HRI\\RecLand\\vendor\\composer/../backpack/pro/src/AddonServiceProvider.php): Failed to open stream: No such file or directory at D:\\Projects\\HRI\\RecLand\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(272): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\Proj...', 'D:\\\\Projects\\\\HRI...', 571)
#1 D:\\Projects\\HRI\\RecLand\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\Proj...', 'D:\\\\Projects\\\\HRI...', 571)
#2 D:\\Projects\\HRI\\RecLand\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\Projects\\\\HRI...')
#3 D:\\Projects\\HRI\\RecLand\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\Projects\\\\HRI...')
#4 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(775): Composer\\Autoload\\ClassLoader->loadClass('Backpack\\\\Pro\\\\Ad...')
#5 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(709): Illuminate\\Foundation\\Application->resolveProvider('Backpack\\\\Pro\\\\Ad...')
#6 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register('Backpack\\\\Pro\\\\Ad...')
#7 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(689): Illuminate\\Foundation\\ProviderRepository->load(Array)
#8 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#9 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(265): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#10 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(383): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#11 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(153): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 {main}
"} 
[2025-08-04 08:58:51] local.ERROR: include(D:\Projects\HRI\RecLand\vendor\composer/../backpack/pro/src/AddonServiceProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\Projects\\HRI\\RecLand\\vendor\\composer/../backpack/pro/src/AddonServiceProvider.php): Failed to open stream: No such file or directory at D:\\Projects\\HRI\\RecLand\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(272): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\Proj...', 'D:\\\\Projects\\\\HRI...', 571)
#1 D:\\Projects\\HRI\\RecLand\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\Proj...', 'D:\\\\Projects\\\\HRI...', 571)
#2 D:\\Projects\\HRI\\RecLand\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\Projects\\\\HRI...')
#3 D:\\Projects\\HRI\\RecLand\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\Projects\\\\HRI...')
#4 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(775): Composer\\Autoload\\ClassLoader->loadClass('Backpack\\\\Pro\\\\Ad...')
#5 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(709): Illuminate\\Foundation\\Application->resolveProvider('Backpack\\\\Pro\\\\Ad...')
#6 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register('Backpack\\\\Pro\\\\Ad...')
#7 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(689): Illuminate\\Foundation\\ProviderRepository->load(Array)
#8 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#9 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(265): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#10 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(383): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#11 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(153): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 {main}
"} 
[2025-08-04 08:58:51] local.ERROR: include(D:\Projects\HRI\RecLand\vendor\composer/../backpack/pro/src/AddonServiceProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\Projects\\HRI\\RecLand\\vendor\\composer/../backpack/pro/src/AddonServiceProvider.php): Failed to open stream: No such file or directory at D:\\Projects\\HRI\\RecLand\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(272): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\Proj...', 'D:\\\\Projects\\\\HRI...', 571)
#1 D:\\Projects\\HRI\\RecLand\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\Proj...', 'D:\\\\Projects\\\\HRI...', 571)
#2 D:\\Projects\\HRI\\RecLand\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\Projects\\\\HRI...')
#3 D:\\Projects\\HRI\\RecLand\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\Projects\\\\HRI...')
#4 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(775): Composer\\Autoload\\ClassLoader->loadClass('Backpack\\\\Pro\\\\Ad...')
#5 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(709): Illuminate\\Foundation\\Application->resolveProvider('Backpack\\\\Pro\\\\Ad...')
#6 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register('Backpack\\\\Pro\\\\Ad...')
#7 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(689): Illuminate\\Foundation\\ProviderRepository->load(Array)
#8 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#9 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(265): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#10 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(383): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#11 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(153): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 {main}
"} 
[2025-08-04 08:59:26] local.ERROR: include(D:\Projects\HRI\RecLand\vendor\composer/../backpack/pro/src/AddonServiceProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\Projects\\HRI\\RecLand\\vendor\\composer/../backpack/pro/src/AddonServiceProvider.php): Failed to open stream: No such file or directory at D:\\Projects\\HRI\\RecLand\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(272): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\Proj...', 'D:\\\\Projects\\\\HRI...', 571)
#1 D:\\Projects\\HRI\\RecLand\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\Proj...', 'D:\\\\Projects\\\\HRI...', 571)
#2 D:\\Projects\\HRI\\RecLand\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\Projects\\\\HRI...')
#3 D:\\Projects\\HRI\\RecLand\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\Projects\\\\HRI...')
#4 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(775): Composer\\Autoload\\ClassLoader->loadClass('Backpack\\\\Pro\\\\Ad...')
#5 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(709): Illuminate\\Foundation\\Application->resolveProvider('Backpack\\\\Pro\\\\Ad...')
#6 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register('Backpack\\\\Pro\\\\Ad...')
#7 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(689): Illuminate\\Foundation\\ProviderRepository->load(Array)
#8 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#9 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(265): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#10 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(383): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#11 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(153): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 {main}
"} 
[2025-08-04 08:59:26] local.ERROR: include(D:\Projects\HRI\RecLand\vendor\composer/../backpack/pro/src/AddonServiceProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\Projects\\HRI\\RecLand\\vendor\\composer/../backpack/pro/src/AddonServiceProvider.php): Failed to open stream: No such file or directory at D:\\Projects\\HRI\\RecLand\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(272): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\Proj...', 'D:\\\\Projects\\\\HRI...', 571)
#1 D:\\Projects\\HRI\\RecLand\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\Proj...', 'D:\\\\Projects\\\\HRI...', 571)
#2 D:\\Projects\\HRI\\RecLand\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\Projects\\\\HRI...')
#3 D:\\Projects\\HRI\\RecLand\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\Projects\\\\HRI...')
#4 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(775): Composer\\Autoload\\ClassLoader->loadClass('Backpack\\\\Pro\\\\Ad...')
#5 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(709): Illuminate\\Foundation\\Application->resolveProvider('Backpack\\\\Pro\\\\Ad...')
#6 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register('Backpack\\\\Pro\\\\Ad...')
#7 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(689): Illuminate\\Foundation\\ProviderRepository->load(Array)
#8 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#9 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(265): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#10 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(383): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#11 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(153): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 {main}
"} 
[2025-08-04 09:00:26] local.ERROR: include(D:\Projects\HRI\RecLand\vendor\composer/../backpack/pro/src/AddonServiceProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\Projects\\HRI\\RecLand\\vendor\\composer/../backpack/pro/src/AddonServiceProvider.php): Failed to open stream: No such file or directory at D:\\Projects\\HRI\\RecLand\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(272): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\Proj...', 'D:\\\\Projects\\\\HRI...', 571)
#1 D:\\Projects\\HRI\\RecLand\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\Proj...', 'D:\\\\Projects\\\\HRI...', 571)
#2 D:\\Projects\\HRI\\RecLand\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\Projects\\\\HRI...')
#3 D:\\Projects\\HRI\\RecLand\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\Projects\\\\HRI...')
#4 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(775): Composer\\Autoload\\ClassLoader->loadClass('Backpack\\\\Pro\\\\Ad...')
#5 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(709): Illuminate\\Foundation\\Application->resolveProvider('Backpack\\\\Pro\\\\Ad...')
#6 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register('Backpack\\\\Pro\\\\Ad...')
#7 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(689): Illuminate\\Foundation\\ProviderRepository->load(Array)
#8 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#9 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(265): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#10 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(383): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#11 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(153): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 {main}
"} 
[2025-08-04 09:00:27] local.ERROR: include(D:\Projects\HRI\RecLand\vendor\composer/../backpack/pro/src/AddonServiceProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\Projects\\HRI\\RecLand\\vendor\\composer/../backpack/pro/src/AddonServiceProvider.php): Failed to open stream: No such file or directory at D:\\Projects\\HRI\\RecLand\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(272): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\Proj...', 'D:\\\\Projects\\\\HRI...', 571)
#1 D:\\Projects\\HRI\\RecLand\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\Proj...', 'D:\\\\Projects\\\\HRI...', 571)
#2 D:\\Projects\\HRI\\RecLand\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\Projects\\\\HRI...')
#3 D:\\Projects\\HRI\\RecLand\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\Projects\\\\HRI...')
#4 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(775): Composer\\Autoload\\ClassLoader->loadClass('Backpack\\\\Pro\\\\Ad...')
#5 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(709): Illuminate\\Foundation\\Application->resolveProvider('Backpack\\\\Pro\\\\Ad...')
#6 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register('Backpack\\\\Pro\\\\Ad...')
#7 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(689): Illuminate\\Foundation\\ProviderRepository->load(Array)
#8 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#9 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(265): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#10 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(383): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#11 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(153): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 {main}
"} 
[2025-08-04 09:01:27] local.ERROR: include(D:\Projects\HRI\RecLand\vendor\composer/../backpack/pro/src/AddonServiceProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\Projects\\HRI\\RecLand\\vendor\\composer/../backpack/pro/src/AddonServiceProvider.php): Failed to open stream: No such file or directory at D:\\Projects\\HRI\\RecLand\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(272): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\Proj...', 'D:\\\\Projects\\\\HRI...', 571)
#1 D:\\Projects\\HRI\\RecLand\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\Proj...', 'D:\\\\Projects\\\\HRI...', 571)
#2 D:\\Projects\\HRI\\RecLand\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\Projects\\\\HRI...')
#3 D:\\Projects\\HRI\\RecLand\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\Projects\\\\HRI...')
#4 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(775): Composer\\Autoload\\ClassLoader->loadClass('Backpack\\\\Pro\\\\Ad...')
#5 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(709): Illuminate\\Foundation\\Application->resolveProvider('Backpack\\\\Pro\\\\Ad...')
#6 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register('Backpack\\\\Pro\\\\Ad...')
#7 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(689): Illuminate\\Foundation\\ProviderRepository->load(Array)
#8 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#9 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(265): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#10 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(383): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#11 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(153): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 {main}
"} 
[2025-08-04 09:01:27] local.ERROR: include(D:\Projects\HRI\RecLand\vendor\composer/../backpack/pro/src/AddonServiceProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\Projects\\HRI\\RecLand\\vendor\\composer/../backpack/pro/src/AddonServiceProvider.php): Failed to open stream: No such file or directory at D:\\Projects\\HRI\\RecLand\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(272): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\Proj...', 'D:\\\\Projects\\\\HRI...', 571)
#1 D:\\Projects\\HRI\\RecLand\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\Proj...', 'D:\\\\Projects\\\\HRI...', 571)
#2 D:\\Projects\\HRI\\RecLand\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\Projects\\\\HRI...')
#3 D:\\Projects\\HRI\\RecLand\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\Projects\\\\HRI...')
#4 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(775): Composer\\Autoload\\ClassLoader->loadClass('Backpack\\\\Pro\\\\Ad...')
#5 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(709): Illuminate\\Foundation\\Application->resolveProvider('Backpack\\\\Pro\\\\Ad...')
#6 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register('Backpack\\\\Pro\\\\Ad...')
#7 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(689): Illuminate\\Foundation\\ProviderRepository->load(Array)
#8 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#9 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(265): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#10 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(383): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#11 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(153): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 {main}
"} 
[2025-08-04 09:01:27] local.ERROR: include(D:\Projects\HRI\RecLand\vendor\composer/../backpack/pro/src/AddonServiceProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\Projects\\HRI\\RecLand\\vendor\\composer/../backpack/pro/src/AddonServiceProvider.php): Failed to open stream: No such file or directory at D:\\Projects\\HRI\\RecLand\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(272): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\Proj...', 'D:\\\\Projects\\\\HRI...', 571)
#1 D:\\Projects\\HRI\\RecLand\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\Proj...', 'D:\\\\Projects\\\\HRI...', 571)
#2 D:\\Projects\\HRI\\RecLand\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\Projects\\\\HRI...')
#3 D:\\Projects\\HRI\\RecLand\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\Projects\\\\HRI...')
#4 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(775): Composer\\Autoload\\ClassLoader->loadClass('Backpack\\\\Pro\\\\Ad...')
#5 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(709): Illuminate\\Foundation\\Application->resolveProvider('Backpack\\\\Pro\\\\Ad...')
#6 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register('Backpack\\\\Pro\\\\Ad...')
#7 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(689): Illuminate\\Foundation\\ProviderRepository->load(Array)
#8 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#9 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(265): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#10 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(383): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#11 D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(153): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 {main}
"} 
