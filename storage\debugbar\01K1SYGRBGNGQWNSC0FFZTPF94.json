{"__meta": {"id": "01K1SYGRBGNGQWNSC0FFZTPF94", "datetime": "2025-08-04 14:07:45", "utime": **********.905246, "method": "GET", "uri": "/admin/collaborator/submit-cv-datatable/7486?draw=1&columns%5B0%5D%5Bdata%5D=id&columns%5B0%5D%5Bname%5D=&columns%5B0%5D%5Bsearchable%5D=true&columns%5B0%5D%5Borderable%5D=false&columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B1%5D%5Bdata%5D=id&columns%5B1%5D%5Bname%5D=&columns%5B1%5D%5Bsearchable%5D=true&columns%5B1%5D%5Borderable%5D=false&columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B2%5D%5Bdata%5D=submit_cv_meta_candidate_name&columns%5B2%5D%5Bname%5D=&columns%5B2%5D%5Bsearchable%5D=true&columns%5B2%5D%5Borderable%5D=false&columns%5B2%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B2%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B3%5D%5Bdata%5D=submit_cv_meta_candidate_email&columns%5B3%5D%5Bname%5D=&columns%5B3%5D%5Bsearchable%5D=true&columns%5B3%5D%5Borderable%5D=false&columns%5B3%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B3%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B4%5D%5Bdata%5D=submit_cv_meta_candidate_mobile&columns%5B4%5D%5Bname%5D=&columns%5B4%5D%5Bsearchable%5D=true&columns%5B4%5D%5Borderable%5D=false&columns%5B4%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B4%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B5%5D%5Bdata%5D=submit_cv_meta_url_cv_public&columns%5B5%5D%5Bname%5D=&columns%5B5%5D%5Bsearchable%5D=true&columns%5B5%5D%5Borderable%5D=false&columns%5B5%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B5%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B6%5D%5Bdata%5D=submit_cv_meta_url_cv_private&columns%5B6%5D%5Bname%5D=&columns%5B6%5D%5Bsearchable%5D=true&columns%5B6%5D%5Borderable%5D=false&columns%5B6%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B6%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B7%5D%5Bdata%5D=job_name&columns%5B7%5D%5Bname%5D=&columns%5B7%5D%5Bsearchable%5D=true&columns%5B7%5D%5Borderable%5D=false&columns%5B7%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B7%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B8%5D%5Bdata%5D=company_name&columns%5B8%5D%5Bname%5D=&columns%5B8%5D%5Bsearchable%5D=true&columns%5B8%5D%5Borderable%5D=false&columns%5B8%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B8%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B9%5D%5Bdata%5D=employer_email&columns%5B9%5D%5Bname%5D=&columns%5B9%5D%5Bsearchable%5D=true&columns%5B9%5D%5Borderable%5D=false&columns%5B9%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B9%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B10%5D%5Bdata%5D=status_value&columns%5B10%5D%5Bname%5D=&columns%5B10%5D%5Bsearchable%5D=true&columns%5B10%5D%5Borderable%5D=false&columns%5B10%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B10%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B11%5D%5Bdata%5D=expected_date_value&columns%5B11%5D%5Bname%5D=&columns%5B11%5D%5Bsearchable%5D=true&columns%5B11%5D%5Borderable%5D=false&columns%5B11%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B11%5D%5Bsearch%5D%5Bregex%5D=false&start=0&length=10&search%5Bvalue%5D=&search%5Bregex%5D=false&_=1754291264963", "ip": "127.0.0.1"}, "php": {"version": "8.1.6", "interface": "apache2handler"}, "messages": {"count": 3, "messages": [{"message": "[14:07:45] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `id` = '1' limit 1\\n-- \",\n    \"Time:\": 3.58\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.783056, "xdebug_link": null, "collector": "log"}, {"message": "[14:07:45] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)\\n-- \",\n    \"Time:\": 0.59\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.801901, "xdebug_link": null, "collector": "log"}, {"message": "[14:07:45] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `submit_cvs` where `user_id` = '7486'\\n-- \",\n    \"Time:\": 3.13\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.808666, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 3, "start": **********.348179, "end": **********.90527, "duration": 0.5570909976959229, "duration_str": "557ms", "measures": [{"label": "Booting", "start": **********.348179, "relative_start": 0, "end": **********.72469, "relative_end": **********.72469, "duration": 0.****************, "duration_str": "377ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.724701, "relative_start": 0.****************, "end": **********.905272, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "181ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.737546, "relative_start": 0.*****************, "end": **********.745197, "relative_end": **********.745197, "duration": 0.007651090621948242, "duration_str": "7.65ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "39MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "GET admin/collaborator/submit-cv-datatable/{user_id}", "middleware": "web, check-admin, check-role", "controller": "App\\Http\\Controllers\\Admin\\CollaboratorController@submitCsvDatatable<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FCollaboratorController.php&line=64\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "admin/collaborator", "as": "submit.cv-datatable", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FCollaboratorController.php&line=64\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Admin/CollaboratorController.php:64-70</a>"}, "queries": {"count": 3, "nb_statements": 3, "nb_visible_statements": 3, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0073, "accumulated_duration_str": "7.3ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "check-admin", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckLoginAdmin.php", "line": 20}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.7801208, "duration": 0.0035800000000000003, "duration_str": "3.58ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `id` = ? limit 1", "hash": "ac0ef66d2bf40f3993c8e40de021b0d99da887a13adec01b9fbb0601a2bf69c5"}, "start_percent": 0, "width_percent": 49.041}, {"sql": "select `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Services/Admin/PermissionService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\PermissionService.php", "line": 19}, {"index": 19, "namespace": null, "name": "app/Services/Admin/PermissionService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\PermissionService.php", "line": 31}, {"index": 20, "namespace": "middleware", "name": "check-role", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckRole.php", "line": 26}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 22, "namespace": "middleware", "name": "check-admin", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckLoginAdmin.php", "line": 21}], "start": **********.8013809, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "PermissionService.php:19", "source": {"index": 18, "namespace": null, "name": "app/Services/Admin/PermissionService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\PermissionService.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FServices%2FAdmin%2FPermissionService.php&line=19", "ajax": false, "filename": "PermissionService.php", "line": "19"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)", "hash": "09b63fe1be07508dfb18b73a990c5ee0249dd740fb6501945ccdb8043c3b971c"}, "start_percent": 49.041, "width_percent": 8.082}, {"sql": "select count(*) as aggregate from `submit_cvs` where `user_id` = '7486'", "type": "query", "params": [], "bindings": ["7486"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 33}, {"index": 16, "namespace": null, "name": "app/Services/Admin/SubmitCvService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\SubmitCvService.php", "line": 67}, {"index": 17, "namespace": null, "name": "app/Services/Admin/SubmitCvService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\SubmitCvService.php", "line": 622}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/CollaboratorController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\CollaboratorController.php", "line": 68}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.805604, "duration": 0.00313, "duration_str": "3.13ms", "memory": 0, "memory_str": null, "filename": "SubmitCvRepository.php:33", "source": {"index": 15, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSubmitCvRepository.php&line=33", "ajax": false, "filename": "SubmitCvRepository.php", "line": "33"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `submit_cvs` where `user_id` = ?", "hash": "266b97cecbaf573df8868da3683f52648c371c2c8095bae8404b1e4a681af7c8"}, "start_percent": 57.123, "width_percent": 42.877}]}, "models": {"data": {"App\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Role": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 2}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "67FrIiAKRzqdDDYE9lpf1v63S50ahQba2svcMgmp", "_previous": "array:1 [\n  \"url\" => \"http://recland.local/admin/collaborator/7486/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://recland.local/admin/collaborator/submit-cv-datatable/7486?_=1754291264963&columns%5B0%5D%5Bda...", "action_name": "submit.cv-datatable", "controller_action": "App\\Http\\Controllers\\Admin\\CollaboratorController@submitCsvDatatable", "uri": "GET admin/collaborator/submit-cv-datatable/{user_id}", "controller": "App\\Http\\Controllers\\Admin\\CollaboratorController@submitCsvDatatable<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FCollaboratorController.php&line=64\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "admin/collaborator", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FCollaboratorController.php&line=64\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Admin/CollaboratorController.php:64-70</a>", "middleware": "web, check-admin, check-role", "duration": "559ms", "peak_memory": "48MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1265214526 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>draw</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>columns</span>\" => <span class=sf-dump-note>array:12</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"29 characters\">submit_cv_meta_candidate_name</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"30 characters\">submit_cv_meta_candidate_email</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"31 characters\">submit_cv_meta_candidate_mobile</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"28 characters\">submit_cv_meta_url_cv_public</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"29 characters\">submit_cv_meta_url_cv_private</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"8 characters\">job_name</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"12 characters\">company_name</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"14 characters\">employer_email</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"12 characters\">status_value</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"19 characters\">expected_date_value</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>start</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>length</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_</span>\" => \"<span class=sf-dump-str title=\"13 characters\">1754291264963</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1265214526\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-350594134 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-350594134\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1574273106 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"49 characters\">http://recland.local/admin/collaborator/7486/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,vi;q=0.8,nl;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1257 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ik1FVFpNYXJsVzhFbnFBOFdNMk1ZckE9PSIsInZhbHVlIjoiUnovbE8rekFyUTErSEk4UTFTT3FCZmZDcktKK0pvb3RsTmdDeEpZSnJ1UzJwNkFiZVhHU05ENWs3cnVTNDlmREc4UldlTHAxQU1IL3hJV1k3cWh5SE12a2VDeUxBZlJNTFZOWjF2L2sxWUd4cFdZNFdtMmQvNmQwcTkzbzFUQWEiLCJtYWMiOiIzYTZlNTY0OTAwNWJlMDlmOGI1YTU2ODU2YTkwZTlhYzFjMzBjYjBiYmY0NGViYTRlYzgxMmQ4YzlhNjhiYjY3IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IlUxV1JGQTJieStNL3lsb0d3dnFoTWc9PSIsInZhbHVlIjoiMEZFQ250N0daODlrZ0tsZ2lYSG4ySm4rQ0orVk92a0o0QVNGZEhiMnY3M3AvODJIQ3NLTjJwbTBEWHEybFF0cGM3NDEySGdadE1qWEZaQURycjBQTi9WMjlBMGUrSW02RmZXSXlJamlKREQ1UElLaU1KNlp2ZFkxQS9qbHdTTEUiLCJtYWMiOiJkNmRjZGE1NzhlMzk3MDk0NWE4NWRhYWRhZDgxYTg2MGY3ZmM2OTg4OWViY2VjYjExMzczYjAwMjY0YTViNDhiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1574273106\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1515837983 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|cm8f4SgFu8wrAd7wPR9b0gsQwQ8YYGlGSWS8Y9hgKQXIsj7qT8VG3KwC3dMx|$2y$10$iTSniY9ReMSnjm6GaX0bRuGWPNsyk9pQat6r8dKHdyoQSgwMkVojS</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">67FrIiAKRzqdDDYE9lpf1v63S50ahQba2svcMgmp</span>\"\n  \"<span class=sf-dump-key>recland_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aK6CrmwILvnr50B6VrqyQAI9QjCWvYUdKGChzrVE</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1515837983\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-980737914 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 04 Aug 2025 07:07:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-980737914\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1192349832 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">67FrIiAKRzqdDDYE9lpf1v63S50ahQba2svcMgmp</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"49 characters\">http://recland.local/admin/collaborator/7486/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1192349832\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://recland.local/admin/collaborator/submit-cv-datatable/7486?_=1754291264963&columns%5B0%5D%5Bda...", "action_name": "submit.cv-datatable", "controller_action": "App\\Http\\Controllers\\Admin\\CollaboratorController@submitCsvDatatable"}, "badge": null}}