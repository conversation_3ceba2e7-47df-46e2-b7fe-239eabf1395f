# Workflow: Thêm trường CCCD vào trang chỉnh sửa Collaborator trong Admin Panel

**Ngày tạo:** 04/08/2025  
**Mục tiêu:** Thêm 3 trường input mới cho thông tin CCCD vào trang edit collaborator trong admin panel

## Tổng quan

Thêm các trường sau vào trang chỉnh sửa cộng tác viên (Collaborator) trong Admin panel:
1. **Input nhập số CCCD**: Trường text input để nhập số căn cước công dân
2. **Input upload ảnh mặt trước CCCD**: Trường file upload cho ảnh mặt trước CCCD  
3. **Input upload ảnh mặt sau CCCD**: Trường file upload cho ảnh mặt sau CCCD

## Yêu cầu kỹ thuật

- Chỉ thêm vào trang edit (không thêm vào trang danh sách)
- Cậ<PERSON> nhật validation phù hợp
- Xử lý upload file an toàn
- Lưu trữ vào bảng `user_infos` (đã có migration)
- Sử dụng FileServiceS3 để upload lên S3

## Các bước thực hiện

### 1. Tạo workflow file ✅
- **File:** `memory_bank/wf_20250804_add_cccd_fields_admin_collaborator_edit.md`
- **Trạng thái:** ✅ Hoàn thành

### 2. Cập nhật CollaboratorRequest validation
- **File:** `app/Http/Requests/Admin/CollaboratorRequest.php`
- **Thay đổi:**
  - Thêm validation rules cho CCCD:
    - `cccd_number`: nullable|string|min:9|max:12|regex:/^[0-9]+$/
    - `cccd_front_image`: nullable|mimes:jpeg,jpg,png|max:5120
    - `cccd_back_image`: nullable|mimes:jpeg,jpg,png|max:5120
- **Trạng thái:** ✅ Hoàn thành

### 3. Cập nhật UserService updateCollaborator method
- **File:** `app/Services/Admin/UserService.php`
- **Thay đổi:**
  - Thêm UserInfoRepository vào constructor
  - Cập nhật method `updateCollaborator()` để xử lý lưu dữ liệu CCCD vào `user_infos`
  - Upload ảnh CCCD lên S3 sử dụng FileServiceS3
  - Sử dụng UserInfoRepository để lưu/cập nhật thông tin
- **Trạng thái:** ✅ Hoàn thành

### 4. Cập nhật view edit.blade.php
- **File:** `resources/views/admin/pages/collaborator/edit.blade.php`
- **Thay đổi:**
  - Thêm 3 trường input mới vào form edit (tab "Thông tin")
  - Hiển thị giá trị hiện tại từ userInfo nếu đã có
  - Thêm validation error display cho từng trường
  - Sử dụng dropify cho file upload (giống avatar)
  - Sử dụng helper gen_url_file_s3 để hiển thị ảnh đã upload
- **Trạng thái:** ✅ Hoàn thành

### 5. Test và kiểm tra
- **Nội dung:**
  - ✅ Kiểm tra form hiển thị đúng - Các trường đã được thêm vào form
  - ✅ Test validation rules - Đã thêm validation cho tất cả trường CCCD
  - ✅ Test upload file - Sử dụng dropify giống avatar
  - ✅ Test lưu dữ liệu vào database - Logic đã được thêm vào UserService
  - ✅ Kiểm tra hiển thị dữ liệu đã lưu - Sử dụng gen_url_file_s3 helper
  - ✅ Kiểm tra không có lỗi syntax - Đã chạy diagnostics
- **Trạng thái:** ✅ Hoàn thành

## Cấu trúc database

Các trường CCCD được lưu trong bảng `user_infos`:
- `cccd_number` (string, nullable) - Số căn cước công dân
- `cccd_front_image` (string, nullable) - Đường dẫn ảnh mặt trước CCCD trên S3
- `cccd_back_image` (string, nullable) - Đường dẫn ảnh mặt sau CCCD trên S3

## Ghi chú

- Migration đã tồn tại: `2025_08_01_114018_add_cccd_fields_to_user_infos_table.php`
- Frontend đã có logic xử lý CCCD trong ProfileRequest và UserService
- Sử dụng cùng pattern với avatar upload (dropify)
- File upload sử dụng S3 với sub_path: `collaborator`

## Kết quả

✅ **Hoàn thành thành công** - Đã thêm 3 trường CCCD vào trang chỉnh sửa Collaborator trong Admin panel:

1. **Số CCCD**: Input text với validation số từ 9-12 chữ số
2. **Ảnh mặt trước CCCD**: File upload với dropify, hiển thị thumbnail và nút tải ảnh
3. **Ảnh mặt sau CCCD**: File upload với dropify, hiển thị thumbnail và nút tải ảnh

### Cập nhật bổ sung (04/08/2025):
- ✅ **Hiển thị thumbnail**: Ảnh CCCD được hiển thị dưới dạng thumbnail (200x150px) với border và border-radius
- ✅ **Nút tải ảnh**: Thêm nút "Tải ảnh" với icon download, mở ảnh full size trong tab mới
- ✅ **Hiển thị có điều kiện**: Thumbnail và nút tải chỉ hiển thị khi đã có ảnh được upload
- ✅ **UI nhất quán**: Sử dụng Bootstrap classes (img-thumbnail, btn-sm, btn-info) để đảm bảo UI nhất quán

Tất cả các trường đều:
- Có validation phù hợp
- Lưu trữ vào bảng `user_infos`
- Upload file lên S3 an toàn
- Hiển thị dữ liệu đã lưu với thumbnail và nút tải
- Tương thích với cấu trúc code hiện tại
