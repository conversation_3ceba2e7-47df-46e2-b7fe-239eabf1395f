<?php

return [
    'find_job'                   => 'Job Search',
    'blog'                       => 'Blog',
    'introduce'                  => 'About',
    'contact'                    => 'Contact',
    'login'                      => 'Login',
    'employer'                   => 'Employer',
    'footer_description'         => 'Bringing jobs and attractive offers to <PERSON><PERSON> and <PERSON>di<PERSON>',
    'about_us'                   => 'About Us',
    'information_about'          => 'Information About',
    'qa'                         => 'Q&A',
    'usage_agreement'            => 'Usage Agreement',
    'privacy_policy'             => 'Privacy policy',
    'recruit'                    => 'Recruit',
    'post_job_vacancies'         => 'Post Job Vacancies',
    'mechanism_of_action'        => 'Mechanism Of Action',
    'search_profiles'            => 'Search Profiles',
    'terms_of_services'          => 'Other Products And Services',
    'partner'                    => 'Partner',
    'welcome_to'                 => 'Welcome to Recland',
    'or_login_by'                => 'OR LOG IN BY',
    'username'                   => 'User name',
    'password'                   => 'Password',
    'save_account'               => 'Save account',
    'forgot_password'            => 'Forgot password',
    'select_login_method'        => 'Please select a login method below',
    'ask_new_member'             => 'No account?',
    'create_new_account'        => 'Create new account',
    'remember_me'        => 'Remember me',
    'new_member'                 => 'New member?',
    'register_now'               => 'Register now to become a member of RecLand',
    'register'                   => 'Register',
    'already_account'            => 'Do you already have an account?',
    'login_now'                  => 'Please login now',
    'register_account'           => 'Register an account',
    'rec'                        => 'Collaborators',
    'name'                       => 'First and last name',
    'email'                      => 'Email',
    'phone'                      => 'Phone number',
    'confirm_password'           => 'Confirm password',
    'referral_code'              => 'Referral code',
    'input'                      => 'Input',
    'employer_login'             => 'Login to your employer account',
    'company_name'               => 'Company name',
    'your_source'                => 'Choose the source you know us',
    'reset_password'             => 'Reset Password',
    'update'                     => 'Update',
    'profile'                    => 'Profile',
    'ware_house_cv'              => 'Candidate CV repository',
    'manage_cv'                  => 'Manage CV',
    'logout'                     => 'Logout',
    'job_manage'                 => 'Job management',
    'forgot_password_permission' => 'Accounts logged in with google or facebook cannot perform this function',
    'enter_your_email'           => 'Enter your email',
    'password_retrieval'         => 'Password retrieval',
    'please_check_email'         => 'Please check email to verify account',
    'register_error'             => 'Register account failed',
    'token_expired'              => 'Token expired or wrong',
    'update_password_success'    => 'Update password success',
    'create_account_success'     => 'Create account success',
    'save_cv_success'            => 'CV has been sent successfully',
    'save_cv_draft_success'      => 'CV has been saved draft successfully',
    'save_cv_error'              => 'Error saving. Please try again',
    'notification'               => 'Notification',
    'seen_all'                   => 'Viewed all',
    'no_notification'            => 'No notification',
    'dashboard'                  => 'Overview',
    'company_profile'            => 'Company profile',
    'mst'                        => 'Tax code',
    'company'                    => 'Company',
    'truongnaykhongthethaydoi'   => 'This field cannot be changed',
    'market_cv'                  => 'Market CV',
    'is_employer'                => 'You are an employer?',
    'content_is_employer'        => 'Many job applications are waiting to be reviewed for submission to your company..',
    'ai_phu_hop' => 'Who is suitable to become a Recland collaborator?',
    'freelancer' => 'Freelance',
    'freelancer_desc' => 'You\'re a freelancer, create additional income streams by expanding with the online candidate referral platform Recland',
    'hr_headhunter' => 'HR, Headhunter',
    'hr_headhunter_desc' => 'You are an HR or Headhunter working in any industry but still want to have a new source of income alongside the earnings from your main job',
    'student_out_of_field' => 'Student, Out of Field',
    'student_out_of_field_desc' => 'You\'re a student or someone who has just left a company... take advantage of your free time to earn additional stable income and try yourself in a new and potential field.',
    'hinh_thuc_gioi_thieu' => 'Candidate referral methods at <a href="/" style="color: #F79720;">Recland.co</a>',
    'method_1_title' => 'CV Referral',
    'method_1_amount' => 'Reward up to 5,000,000/UV',
    'method_1_desc_1' => 'Candidates with experience and skills that meet job requirements',
    'method_1_desc_2' => 'Candidates interested in applying for the job posted by the client',
    'method_2_title' => 'Interview Referral',
    'method_2_amount' => 'Reward up to 15,000,000/UV',
    'method_2_desc_1' => 'Candidates with experience and skills that meet job requirements',
    'method_2_desc_2' => 'Candidates interested in applying for the job posted by the client',
    'method_2_desc_3' => 'Candidates participate in interviews',
    'method_3_title' => 'Onboard Referral',
    'method_3_amount' => 'Reward up to 80,000,000/UV',
    'method_3_desc_1' => 'Candidates with experience and skills that meet job requirements',
    'method_3_desc_2' => 'Candidates interested in applying for the job posted by the client',
    'method_3_desc_3' => 'Candidates accept to work for the Client',
    'start_now' => 'Start <a href="#" style="color: #F79720;">simply</a> right now!',
    'step' => 'Step',
    'step_1' => 'Register to become a partner at: <a class="text-link underline" href="/register">https://recland.co/register</a> and check your email to activate your account',
    'step_2' => 'Log in and view jobs, methods, and reward levels at: <a class="text-link underline" href="/job">https://recland.co/job</a>',
    'step_3' => 'Select CVs that meet job requirements and send the “Candidate Referral” CV. Don\'t forget to remind candidates to check their email to confirm the referral!',
    'step_4' => 'Track the status of Referrals in “Manage Candidate Referrals”. Support customers with the “Interview” and “Onboard” referral methods',
    'step_5' => 'Successful referral, add bank information in “Personal Profile” and wait for the ting ting.',
    'choose_recland' => 'Choose <a href="/" style="color: #F79720;">Recland</a> to increase your income with us',
    'choose_1_title' => 'Diverse jobs, many attractive options',
    'choose_1_desc' => 'Over 200+ diverse IT jobs, providing employment to suitable candidates.',
    'choose_2_title' => 'Attractive, Transparent, and Clear Commission Mechanism',
    'choose_2_desc' => 'Recland provides a clear and transparent affiliate policy with highly attractive commission rates.',
    'choose_3_title' => 'Simple and Effective Working Mechanism',
    'choose_3_desc' => 'You can work anywhere and anytime while ensuring a steady source of income.',
    'choose_4_title' => 'Information Security',
    'choose_4_desc' => 'Recland is committed to absolute information security for affiliates and candidates.',
    'choose_5_title' => 'Expand Your Network within the Community',
    'choose_5_desc' => 'Opportunities for growth with Recland and expanding your network in the recruitment field.',
    'are_you_ready' => 'Are you ready to become a partner at <a href="#" style="color: #F79720;">Recland?</a>',
    'register_now_2' => 'Register now',
    'xem_job_ngay' => 'View jobs now',
    'job_posting' => 'Job posting ',
    'submit_cv ' => 'Manage candidate CVs ',
    'cv-bought' => 'Manage purchased CVs',

];
