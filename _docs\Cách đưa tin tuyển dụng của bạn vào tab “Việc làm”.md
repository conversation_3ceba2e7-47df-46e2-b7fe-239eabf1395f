<img src="https://r2cdn.perplexity.ai/pplx-full-logo-primary-dark%402x.png" class="logo" width="120"/>

### Cách đưa tin tuyển dụng của bạn vào tab “Việc làm” của Google Search

Google chỉ hiển thị các vị trí tuyển dụng trong giao diện “Google Việclàm” (Google Job Search) đối với những trang **có dữ liệu cấu trúc JobPosting hợp lệ** và được Google thu thập – lập chỉ mục. Để website tuyển dụng của bạn đủ điều kiện, thực hiện lần lượt các bước sau.

#### 1. Thêm dữ liệu cấu trúc JobPosting vào từng trang tuyển dụng

1. Chọn định dạng JSON-LD (khuyến nghị) và chèn trong thẻ `<script type="application/ld+json"> … </script>` ở phần `<head>` hoặc cuối `<body>`.
2. <PERSON><PERSON><PERSON> buộc phải khai báo chính xác các trường tối thiểu sau theo chuẩn Schema.org:
| Thuộc tính | Ý nghĩa | Ví dụ | Bắt buộc |
| :-- | :-- | :-- | :-- |
| `title` | Chức danh | “Nhân viên Kinh doanh” | ✔︎ |
| `description` | Mô tả công việc (HTML cho phép) | “Tư vấn, chăm sóc khách hàng…” | ✔︎ |
| `datePosted` | Ngày đăng | “2025-07-25” | ✔︎ |
| `validThrough` | Ngày hết hạn | “2025-08-10T23:59” | ✔︎ |
| `employmentType` | Hình thức | “FULL_TIME” | ✔︎ |
| `hiringOrganization` | Tên, logo, URL DN | `{ "name":"ABC JSC", … }` | ✔︎ |
| `jobLocation` | Địa điểm (đầy đủ `addressCountry`) | `{ "addressLocality":"Hà Nội", … }` | ✔︎ |
| `identifier` | Mã tin duy nhất | `"ABC-SE-2025"` | ✔︎ |

Thêm các trường **khuyến khích** như `baseSalary`, `jobLocationType` (TELECOMMUTE cho remote), `directApply`, phúc lợi… để tăng tỷ lệ được hiển thị và lọc[^1][^2].

3. Với WordPress: cài plugin Rank Math, WP Job Manager, Instant Indexing… rồi chọn Schema Type → Job Posting và điền biểu mẫu[^3][^4][^5].
4. Với website tự code: tạo mã tại Schema Markup Generator rồi gắn vào từng trang[^6].

#### 2. Đảm bảo Google có thể thu thập và lập chỉ mục

1. **Robots.txt / thẻ `noindex`**: không chặn trang tuyển dụng.
2. **Sitemap XML chuyên cho jobs**: liệt kê URL các tin, cập nhật khi thêm-xóa, gửi trong Google Search Console[^7][^8].
3. **Indexing API**: đối với JobPosting, Google cho phép dùng Indexing API để thông báo ngay khi:

```json
POST https://indexing.googleapis.com/v3/urlNotifications:publish
{
  "url": "https://yourdomain.com/jobs/abc-se-2025",
  "type": "URL_UPDATED"
}
```

Bạn có thể gửi tối đa 200 URL/ngày để được crawl nhanh hơn sitemap[^9][^10].
4. Kiểm tra URL bằng **Rich Results Test** và **URL Inspection** để chắc chắn Google nhìn thấy schema hợp lệ và không báo lỗi[^1].

#### 3. Tuân thủ chính sách nội dung tuyển dụng của Google

- Không đăng tin đã hết hạn; cập nhật `validThrough` hoặc xóa trang và gửi `URL_DELETED` qua Indexing API.
- Không lặp lại cùng một vị trí với dữ liệu trùng lặp URL.
- Không chứa nội dung kỳ thị, sai lệch, lương “liên hệ”…—vi phạm sẽ bị loại khỏi tính năng[^1][^8].


#### 4. Tối ưu trải nghiệm \& độ phủ

1. **Thông tin rõ ràng, đầy đủ** (mức lương, địa điểm cụ thể, mô tả ngắn gọn đầu đoạn) giúp tăng tỷ lệ click và xếp hạng[^11][^12].
2. **Logo doanh nghiệp chuẩn tỷ lệ 1:1** (112×112 px, định dạng PNG/JPG) trong `hiringOrganization.logo`.
3. Cấu trúc URL duy nhất, tĩnh (không session ID).
4. Nếu dùng nhiều ngôn ngữ/địa điểm, khai báo thẻ `hreflang` và multiple `jobLocation` theo hướng dẫn[^12].

#### 5. Lộ trình triển khai gợi ý

1. **Kiểm tra mẫu**: chọn một tin, thêm JobPosting JSON-LD, chạy Rich Results Test.
2. **Xây dựng quy trình**: mỗi khi đăng / sửa / xóa tin → cập nhật schema → gọi Indexing API.
3. **Theo dõi**: trong Search Console > Hiển thị kết quả phong phú > Job postings để xem lỗi và lượt hiển thị.
4. **Mở rộng**: áp dụng cho toàn bộ tin, thêm plugin tự động tạo schema, cron gửi Indexing API định kỳ.

### Kết luận

Chỉ cần **(1) dữ liệu cấu trúc JobPosting hợp lệ**, **(2) Google thu thập-lập chỉ mục kịp thời** (sitemap + Indexing API) và **(3) tuân thủ chính sách nội dung**, các tin tuyển dụng từ website của bạn sẽ đủ điều kiện xuất hiện trong tab “Việc làm” của Google, tăng đáng kể lưu lượng ứng tuyển chất lượng[^1][^8][^9].

<div style="text-align: center">⁂</div>

[^1]: https://developers.google.com/search/docs/appearance/structured-data/job-posting

[^2]: https://schema.org/JobPosting

[^3]: https://rankmath.com/kb/jobposting-schema/

[^4]: https://wpjobmanager.com/2023/06/07/job-posting-structured-data/

[^5]: https://vi.wordpress.org/plugins/fast-indexing-api/

[^6]: https://www.brandsvietnam.com/congdong/topic/19953-google-viec-lam-cach-dua-tin-tuyen-dung-len-google-viec-lam

[^7]: https://docs.cariera.co/knowledgebase/google-job-search-integration/

[^8]: https://www.searchenginejournal.com/google-clarifies-job-posting-structured-data-guidance/505284/

[^9]: https://developers.google.com/search/apis/indexing-api/v3/using-api

[^10]: https://openplanning.net/14285/java-google-indexing-api

[^11]: https://proweb.com.vn/schema-job-posting-schema-danh-cho-nha-tuyen-dung/

[^12]: https://v4seowebsite.vn/toi-uu-hoa-trai-nghiem-tuyen-dung-voi-du-lieu-co-cau-truc-tin-tuyen-dung/

[^13]: https://www.jobboardly.com/blog/google-jobs-schema-markup-guide

[^14]: https://wpjobmanager.com/document/troubleshooting/google-job-search-integration/

[^15]: http://help.vincere.io/en/articles/3507177-google-for-jobs-structured-data

[^16]: https://aioseo.com/seo-glossary/job-posting-schema-markup/

[^17]: https://vietnix.vn/schema-job-posting/

[^18]: https://cloud.google.com/talent-solution/job-search/docs/basics

[^19]: https://jobs.google.com/about/

[^20]: https://hashmeta.com/blog/job-posting-seo-implementing-schema-markup-to-land-roles-on-google-for-jobs/

[^21]: https://docs.purethemes.net/workscout/knowledge-base/google-job-search-integration/

[^22]: https://cloud.google.com/talent-solution/job-search/v3/docs/basics

[^23]: https://www.redshiftrecruiting.com/career-blog/job-posting-schema

[^24]: https://serpapi.com/google-jobs-api

[^25]: https://developers.google.com/search/docs/appearance/structured-data/search-gallery

[^26]: https://help.sap.com/docs/successfactors-release-information/8e0d540f96474717bbf18df51e54e522/5c543c890ac6406492d2ab8a3f9cb857.html

[^27]: https://fastwork.vn/16-cach-dang-tin-tuyen-dung-tren-google/

[^28]: https://congtyannhien.com/toi-uu-hoa-bai-dang-tuyen-dung/

[^29]: https://support.google.com/websearch/thread/211915469/em-muốn-hỏi-về-tính-năng-tìm-việc-làm-trên-google

[^30]: https://www.linkedin.com/pulse/post-job-lên-google-tại-sao-không-tu-thien

[^31]: https://seoplus.com.vn/su-dung-google-index-api-de-crawl-data-website/

[^32]: https://worklink.vn/cach-tuyen-nhan-vien-dang-tin-quang-cao-tai-nha-qua-google-for-jobs/

[^33]: https://july156.mywebsite.vn/cach-dua-tin-tuyen-dung-len-google-viec-lam.html

[^34]: https://blog.lengoc.me/cach-index-bai-viet-nhanh-va-mien-phi-voi-google-indexing-api/

[^35]: https://blog.tomorrowmarketers.org/chay-quang-cao-tuyen-dung-tren-google-ads/

[^36]: https://vuottroi.vn/blog/google-index-la-gi-cach-index-google-2022/

[^37]: https://hrchannels.com/uptalent/cach-dang-tin-tuyen-dung-mien-phi.html

[^38]: https://az9s.com/lam-the-nao-de-google-thu-thap-du-lieu-trang-web-cua-ban-ngay-lap-tuc

[^39]: https://careerviet.vn/vi/hiringsite/google-them-chuc-nang-tim-viec-lam-cho-nguoi-dung-viet-nam.35A4EE31.html

