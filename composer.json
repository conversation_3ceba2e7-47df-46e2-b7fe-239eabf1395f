{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.0.2", "alexusmai/laravel-file-manager": "^3.0", "anhskohbo/no-captcha": "^3.6", "artesaos/seotools": "^0.22.0", "backpack/crud": "^5.6", "backpack/pro": "*", "barryvdh/laravel-dompdf": "2.2", "doctrine/dbal": "^3.3", "guzzlehttp/guzzle": "^7.2", "laravel/framework": "^9.2", "laravel/horizon": "^5.33", "laravel/sanctum": "^2.14.1", "laravel/socialite": "^5.5", "laravel/tinker": "^2.7", "league/flysystem-aws-s3-v3": "3.0", "league/html-to-markdown": "^5.1", "maatwebsite/excel": "^3.1", "owen-it/laravel-auditing": "^13.6", "predis/predis": "^2.2", "psr/simple-cache": "1.0", "shetabit/visitor": "^3.1", "yajra/laravel-datatables": "1.5", "zoha/laravel-meta": "^2.0"}, "require-dev": {"backpack/generators": "^3.3", "barryvdh/laravel-debugbar": "^3.6", "fakerphp/faker": "^1.9.1", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^6.1", "phpunit/phpunit": "^9.5.10", "spatie/laravel-ignition": "^1.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["helpers/const.php", "helpers/function.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "minimum-stability": "dev", "prefer-stable": true, "repositories": [{"type": "path", "url": "./packages/backpack-pro"}]}