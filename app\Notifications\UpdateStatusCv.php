<?php

namespace App\Notifications;

use App\Helpers\Common;
use App\Services\Frontend\SettingService;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class UpdateStatusCv extends Notification implements ShouldQueue
{
    use Queueable;

    protected $submitCv;
    protected $user;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($submitCv, $user)
    {
        $this->submitCv = $submitCv;
        $this->user = $user;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $name = $this->submitCv->submitCvMeta->candidate_name;
        $candidateJob = $this->submitCv->submitCvMeta->candidate_job_title;

        return (new MailMessage)
            ->view('email.updateStatusCv', [
                'name' => $name,
                'job' => $candidateJob,
                'recName' => $this->user->name,
            ])
            ->subject('[Recland] [THÔNG BÁO CV ĐÃ ĐƯỢC CẬP NHẬT TRẠNG THÁI]');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        $name = $this->submitCv->submitCvMeta->candidate_name;
        $candidateJob = $this->submitCv->submitCvMeta->candidate_job_title;

        $settings = resolve(SettingService::class);

        $str = 'notification';

        $arrLangVi = $settings->getAllKeyWithQuery($str, config('constant.language.vi'));
        $arrLangEn = $settings->getAllKeyWithQuery($str, config('constant.language.en'));

        $contentVi = Common::transLang($arrLangVi['ctv_cvungviendaduoccapnhattrangthai'], ['name' => $name, 'job' => $candidateJob]);
        $contentEn = Common::transLang($arrLangEn['ctv_cvungviendaduoccapnhattrangthai'], ['name' => $name, 'job' => $candidateJob]);

        return [
            //            'event'           => 'updateCv',
            'content_vi'         => $contentVi,
            'content_en'         => $contentEn,
            //            'description'     => $description,
            //            'submitcvid'      => $this->submitCv->id,
            'routename'       => 'rec-warehousecv'
        ];
    }
}
