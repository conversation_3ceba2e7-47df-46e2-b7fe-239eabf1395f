<!-- build:title --><PERSON><PERSON> <PERSON>uy<PERSON>n dụng pro
<!-- /build:title -->
<!-- build:content -->
<section class="section-top-home clearfix mb80">
    <div class="container">
        <div class="flex mb80">
            <div class="section-top-home__left fade-in-left">
                <div class="content">
                    <div class="lbl">Cộng tác từ xa - Kiếm tiền thả ga</div>
                    <h1 class="title">
                        Nền tảng tạo ra cơ hội kiếm tiền dành cho
                        <strong class="key">HR FREELANCER</strong>
                    </h1>
                    <div class="des">
                        Lorem ipsum dolor sit amet consectetur. Aliquam condimentum donec lorem praesent habitant sit.
                    </div>
                    <a class="btn-default">
                        Tìm việc ngay
                        <svg class="icon-svg ms-2">
                            <use xlink:href="images/icons/icon.svg#right"></use>
                        </svg>
                    </a>
                </div>
            </div>
            <div class="section-top-home__right fade-in-top">
                <a class="back-home" href="javascript:;"><span>Quay lại</span></a>
                <div class="scroll-height">
                    <div class="top-ctv">
                        <div class="head">
                            <svg xmlns="http://www.w3.org/2000/svg" width="35" height="20" viewBox="0 0 35 20"
                                fill="none">
                                <g style="mix-blend-mode:overlay" clip-path="url(#clip0_1_1631)">
                                    <path
                                        d="M11.0076 10.4702C12.9436 11.2288 14.9191 11.9004 16.9341 12.4615C16.6734 12.4852 16.4047 12.5247 16.136 12.5721C13.884 12.9751 11.9638 14.1604 10.5967 15.7882C12.4458 16.8471 14.6583 17.2817 16.9104 16.8708C19.1388 16.4757 21.0353 15.3141 22.3944 13.7179C24.4806 14.0972 26.6141 14.3659 28.7713 14.5318C28.3525 14.6108 27.9337 14.7136 27.5228 14.8479C25.0969 15.646 23.1609 17.2422 21.9124 19.2493C24.1092 20.1264 26.6141 20.2687 29.04 19.4785C30.249 19.0834 31.3395 18.4828 32.2798 17.7321C32.754 17.3528 33.1965 16.934 33.5916 16.4836C34.0262 15.9937 34.4687 15.4326 34.6346 14.7689C34.8164 14.0577 34.7611 13.2438 34.6663 12.5247C34.4687 10.968 33.8919 9.44291 33.0384 8.13117C31.6477 5.98973 29.609 4.52785 27.349 3.83248C27.0645 6.17938 27.5702 8.6369 28.9531 10.7783C29.7986 12.0743 30.8812 13.1173 32.106 13.8996C28.8741 13.8285 25.6975 13.5124 22.6078 12.9514C22.9476 11.0944 22.7421 9.11893 21.8808 7.27776C20.9088 5.19953 19.2731 3.64283 17.3529 2.742C16.8077 4.79652 16.9499 7.0486 17.9219 9.12683C18.5777 10.5334 19.5418 11.695 20.6876 12.58C17.4557 11.8846 14.3186 10.9285 11.2921 9.73528C11.8136 8.17859 11.9005 6.46385 11.4343 4.76491C10.8812 2.7499 9.65637 1.10628 8.06807 0C7.26206 1.76215 7.04081 3.80877 7.59395 5.82378C7.90213 6.94587 8.42366 7.95733 9.10323 8.81865C8.29723 8.46306 7.49912 8.08376 6.70892 7.68866C5.91872 7.29356 5.14432 6.88266 4.37783 6.45595C4.06175 6.2821 3.75356 6.10036 3.43748 5.92651C3.18462 5.77637 2.93176 5.59463 2.65519 5.484C2.37862 5.37337 2.02303 5.28645 1.75436 5.44449C1.36716 5.67365 2.18896 6.13987 2.36281 6.24259C2.62358 6.39273 2.88435 6.54287 3.13721 6.69301C3.52441 6.91426 3.91161 7.13552 4.29881 7.34887C5.0811 7.77558 5.87131 8.18649 6.66941 8.58159C6.81955 8.65271 6.96969 8.72382 7.11192 8.79494C6.58249 8.69222 6.02935 8.6369 5.4604 8.6448C3.37427 8.66061 1.46988 9.4271 0.000110626 10.6835C1.49359 11.9162 3.41378 12.6511 5.49991 12.6274C7.58604 12.6116 9.49043 11.8451 10.9681 10.5887C10.9839 10.5492 10.9918 10.5176 11.0076 10.4781V10.4702Z"
                                        fill="white" />
                                </g>
                                <defs>
                                    <clipPath id="clip0_1_1631">
                                        <rect width="34.7531" height="20" fill="white"
                                            transform="matrix(-1 0 0 1 34.7532 0)" />
                                    </clipPath>
                                </defs>
                            </svg>
                            <span>Top Cộng tác viên</span>
                            <svg xmlns="http://www.w3.org/2000/svg" width="35" height="20" viewBox="0 0 35 20"
                                fill="none">
                                <g style="mix-blend-mode:overlay" clip-path="url(#clip0_1_1628)">
                                    <path
                                        d="M23.7455 10.4702C21.8095 11.2288 19.834 11.9004 17.819 12.4615C18.0798 12.4852 18.3485 12.5247 18.6171 12.5721C20.8692 12.9751 22.7894 14.1604 24.1564 15.7882C22.3074 16.8471 20.0948 17.2817 17.8427 16.8708C15.6144 16.4757 13.7179 15.3141 12.3587 13.7179C10.2726 14.0972 8.13906 14.3659 5.98181 14.5318C6.40062 14.6108 6.81943 14.7136 7.23033 14.8479C9.65625 15.646 11.5922 17.2422 12.8408 19.2493C10.644 20.1264 8.13906 20.2687 5.71314 19.4785C4.50414 19.0834 3.41366 18.4828 2.47332 17.7321C1.9992 17.3528 1.55668 16.934 1.16158 16.4836C0.726972 15.9937 0.284459 15.4326 0.118516 14.7689C-0.06323 14.0577 -0.00791586 13.2438 0.0869083 12.5247C0.284459 10.968 0.861306 9.44291 1.71472 8.13117C3.10548 5.98973 5.1442 4.52785 7.40417 3.83248C7.68865 6.17938 7.18292 8.6369 5.80007 10.7783C4.95455 12.0743 3.87197 13.1173 2.64716 13.8996C5.87909 13.8285 9.0557 13.5124 12.1454 12.9514C11.8056 11.0944 12.011 9.11893 12.8724 7.27776C13.8443 5.19953 15.48 3.64283 17.4002 2.742C17.9455 4.79652 17.8032 7.0486 16.8313 9.12683C16.1754 10.5334 15.2114 11.695 14.0656 12.58C17.2975 11.8846 20.4346 10.9285 23.4611 9.73528C22.9395 8.17859 22.8526 6.46385 23.3188 4.76491C23.872 2.7499 25.0968 1.10628 26.6851 0C27.4911 1.76215 27.7124 3.80877 27.1592 5.82378C26.851 6.94587 26.3295 7.95733 25.6499 8.81865C26.4559 8.46306 27.254 8.08376 28.0442 7.68866C28.8344 7.29356 29.6088 6.88266 30.3753 6.45595C30.6914 6.2821 30.9996 6.10036 31.3157 5.92651C31.5685 5.77637 31.8214 5.59463 32.098 5.484C32.3745 5.37337 32.7301 5.28645 32.9988 5.44449C33.386 5.67365 32.5642 6.13987 32.3903 6.24259C32.1296 6.39273 31.8688 6.54287 31.6159 6.69301C31.2287 6.91426 30.8416 7.13552 30.4544 7.34887C29.6721 7.77558 28.8819 8.18649 28.0837 8.58159C27.9336 8.65271 27.7835 8.72382 27.6412 8.79494C28.1707 8.69222 28.7238 8.6369 29.2928 8.6448C31.3789 8.66061 33.2833 9.4271 34.753 10.6835C33.2596 11.9162 31.3394 12.6511 29.2532 12.6274C27.1671 12.6116 25.2627 11.8451 23.7851 10.5887C23.7692 10.5492 23.7613 10.5176 23.7455 10.4781V10.4702Z"
                                        fill="white" />
                                </g>
                                <defs>
                                    <clipPath id="clip0_1_1628">
                                        <rect width="34.7531" height="20" fill="white" />
                                    </clipPath>
                                </defs>
                            </svg>
                        </div>

                        <div class="list-ctv">
                            <div class="scrollbar-height">
                                <div class="item hot">
                                    <div class="left">
                                        <span class="number">1</span>
                                        <span class="name">Nguyễn Thị Hồng Anh 🔥</span>
                                    </div>
                                    <div class="right">
                                        <span class="price">$ 5000</span>
                                        <span class="up">+30%</span>
                                    </div>
                                </div>
                                <div class="item hot">
                                    <div class="left">
                                        <span class="number">2</span>
                                        <span class="name">Nguyễn Thị Hồng Anh 🔥</span>
                                    </div>
                                    <div class="right">
                                        <span class="price">$ 5000</span>
                                        <span class="up">+30%</span>
                                    </div>
                                </div>
                                <div class="item">
                                    <div class="left">
                                        <span class="number">3</span>
                                        <span class="name">Nguyễn THị Hồng Anh</span>
                                    </div>
                                    <div class="right">
                                        <span class="price">$ 5000</span>
                                        <span class="up">+30%</span>
                                    </div>
                                </div>
                                <div class="item">
                                    <div class="left">
                                        <span class="number">4</span>
                                        <span class="name">Nguyễn THị Hồng Anh</span>
                                    </div>
                                    <div class="right">
                                        <span class="price">$ 5000</span>
                                        <span class="up">+30%</span>
                                    </div>
                                </div>
                                <div class="item">
                                    <div class="left">
                                        <span class="number">5</span>
                                        <span class="name">Nguyễn THị Hồng Anh</span>
                                    </div>
                                    <div class="right">
                                        <span class="price">$ 5000</span>
                                        <span class="up">+30%</span>
                                    </div>
                                </div>
                                <div class="item">
                                    <div class="left">
                                        <span class="number">6</span>
                                        <span class="name">Nguyễn THị Hồng Anh</span>
                                    </div>
                                    <div class="right">
                                        <span class="price">$ 5000</span>
                                        <span class="up">+30%</span>
                                    </div>
                                </div>
                                <div class="item">
                                    <div class="left">
                                        <span class="number">7</span>
                                        <span class="name">Nguyễn THị Hồng Anh</span>
                                    </div>
                                    <div class="right">
                                        <span class="price">$ 5000</span>
                                        <span class="up">+30%</span>
                                    </div>
                                </div>
                                <div class="item">
                                    <div class="left">
                                        <span class="number">8</span>
                                        <span class="name">Nguyễn THị Hồng Anh</span>
                                    </div>
                                    <div class="right">
                                        <span class="price">$ 5000</span>
                                        <span class="up">+30%</span>
                                    </div>
                                </div>
                                <div class="item">
                                    <div class="left">
                                        <span class="number">9</span>
                                        <span class="name">Nguyễn THị Hồng Anh</span>
                                    </div>
                                    <div class="right">
                                        <span class="price">$ 5000</span>
                                        <span class="up">+30%</span>
                                    </div>
                                </div>
                                <div class="item">
                                    <div class="left">
                                        <span class="number">10</span>
                                        <span class="name">Nguyễn THị Hồng Anh</span>
                                    </div>
                                    <div class="right">
                                        <span class="price">$ 5000</span>
                                        <span class="up">+30%</span>
                                    </div>
                                </div>
                                <div class="item">
                                    <div class="left">
                                        <span class="number">11</span>
                                        <span class="name">Nguyễn THị Hồng Anh</span>
                                    </div>
                                    <div class="right">
                                        <span class="price">$ 5000</span>
                                        <span class="up">+30%</span>
                                    </div>
                                </div>
                                <div class="item">
                                    <div class="left">
                                        <span class="number">12</span>
                                        <span class="name">Nguyễn THị Hồng Anh</span>
                                    </div>
                                    <div class="right">
                                        <span class="price">$ 5000</span>
                                        <span class="up">+30%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-pagination"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="new-post">
            <svg class="icon-svg me-2">
                <use xlink:href="images/icons/icon.svg#web"></use>
            </svg>
            <span>MỚI NHẤT</span>
            <a class="name" href="#">Lorem ipsum dolor sit amet consectetur. Aliquam condimentum donec lore...</a>
        </div>
    </div>
    <div class="bg-btn">
        <a class="btn-top-ctv" href="javascript:;">Top Cộng tác viên</a>
    </div>
</section>

<section class="section-home-2 clearfix mb80">
    <div class="container">
        <div class="row">
            <div class="col-md-6 mb30">
                <img class="w-100" src="images/graphics/img-about.png" alt="">
            </div>
            <div class="col-md-6 aos-init aos-animate" data-aos="fade-right" data-aos-duration="1000"
                data-aos-delay="100">
                <h2 class="title-cate">Nền tảng thương mại điện tử tuyển dụng hàng đầu Việt Nam</h2>
                <div class="des">
                    Recland là một đơn vị thành viên trong hệ sinh thái HRI, Recland cung cấp nền tảng thương mại điện
                    tử tuyển dụng để cộng tác viên tuyển dụng có thể kiếm tiền bằng cách giới thiệu, kết nối các ứng
                    viên ứng tuyển cho các doanh nghiệp.Cộng tác viên có thể
                    chủ động làm việc, gia tăng thu nhập không giới hạn tại bất cứ đâu, bất cứ thời gian nào.
                </div>
                <div class="row mb20">
                    <div class="col-md-4 col-6 mb20">
                        <div class="number">
                            <strong class="numscroller" data-min='1' data-max='5000' data-delay='1.5'
                                data-increment='20'></strong>
                            <span>+</span>
                        </div>
                        <div>Ứng viên mới</br> mỗi tháng</div>
                    </div>
                    <div class="col-md-4 col-6 mb20">
                        <div class="number">
                            <strong class="numscroller" data-min='1' data-max='100' data-delay='1.5'
                                data-increment='5'></strong>
                            <span>+</span>
                        </div>
                        <div>Việc làm IT</div>
                    </div>
                    <div class="col-md-4 col-6 mb20">
                        <div class="number">
                            <strong class="numscroller" data-min='1' data-max='7700' data-delay='1.5'
                                data-increment='20'></strong>
                            <span>+</span>
                        </div>
                        <div>Ứng viên</br> phù hợp</div>
                    </div>
                    <div class="col-md-4 col-6 mb20">
                        <div class="number">
                            <strong class="numscroller" data-min='1' data-max='2000' data-delay='1.5'
                                data-increment='10'></strong>
                            <span>+</span>
                        </div>
                        <div>CTV sử dụng</br> dịch v</div>
                    </div>
                    <div class="col-md-4 col-6 mb20">
                        <div class="number">
                            <strong class="numscroller" data-min='1' data-max='200' data-delay='1.5'
                                data-increment='5'></strong>
                            <span>+</span>
                        </div>
                        <div>Lượt tuyển dụng</br> thành công</div>
                    </div>
                    <div class="col-md-4 col-6 mb20">
                        <div class="number">
                            <strong class="numscroller" data-min='1' data-max='100' data-delay='1.5'
                                data-increment='5'></strong>
                            <span>+</span>
                        </div>
                        <div>Nhận hoa hồng</br> ngay lập tức</div>
                    </div>
                </div>
                <a class="btn-default">
                    Khám phá
                    <svg class="icon-svg ms-2">
                        <use xlink:href="images/icons/icon.svg#right"></use>
                    </svg>
                </a>
            </div>
        </div>
    </div>
</section>

<section class="section-doi-tac clearfix">
    <div class="swiper-container">
        <div class="swiper-wrapper">
            <div class="swiper-slide">
                <span class="ava"><img src="images/doi-tac/doitac1.png" alt=""></span>
            </div>
            <div class="swiper-slide">
                <span class="ava"><img src="images/doi-tac/doitac2.png" alt=""></span>
            </div>
            <div class="swiper-slide">
                <span class="ava"><img src="images/doi-tac/doitac3.png" alt=""></span>
            </div>
            <div class="swiper-slide">
                <span class="ava"><img src="images/doi-tac/doitac4.png" alt=""></span>
            </div>
            <div class="swiper-slide">
                <span class="ava"><img src="images/doi-tac/doitac5.png" alt=""></span>
            </div>
            <div class="swiper-slide">
                <span class="ava"><img src="images/doi-tac/doitac6.png" alt=""></span>
            </div>
            <div class="swiper-slide">
                <span class="ava"><img src="images/doi-tac/doitac7.png" alt=""></span>
            </div>
            <div class="swiper-slide">
                <span class="ava"><img src="images/doi-tac/doitac8.png" alt=""></span>
            </div>
            <div class="swiper-slide">
                <span class="ava"><img src="images/doi-tac/doitac9.png" alt=""></span>
            </div>
            <div class="swiper-slide">
                <span class="ava"><img src="images/doi-tac/doitac10.png" alt=""></span>
            </div>
        </div>
    </div>
</section>

<section class="section-home-3 clearfix mb80">
    <div class="container">
        <div class="head aos-init aos-animate" data-aos="fade-down" data-aos-duration="1000" data-aos-delay="100">
            <h2 class="title-cate2">
                Cơ hội kiếm tiền không giới hạn
            </h2>
            <p>200.000+ Job xịn – thưởng cao đến từ các doanh nghiệp lớn hàng đầu được cập nhật hàng ngày</p>
            <div class="tab">
                <a class="active" href="#">Development</a>
                <a href="#">Development</a>
                <a href="#">Development</a>
                <a href="#">Development</a>
                <a href="#">Development</a>
                <a href="#">Development</a>
                <a href="#">Development</a>
            </div>
        </div>
        <div class="list-job job-slide-column relative aos-init aos-animate" data-aos="fade-up" data-aos-duration="1000"
            data-aos-delay="200">
            <div class="swiper-container mb40">
                <div class="swiper-wrapper">
                    <div class="swiper-slide item">
                        <div class="warp">
                            <div class="job-cty">
                                <span class="logo"><img src="images/graphics/fpt.png" alt=""></span>
                                <div class="info">
                                    <div class="name">FPT Software</div>
                                    <div class="address">
                                        <svg class="icon-svg me-1">
                                            <use xlink:href="images/icons/icon.svg#location"></use>
                                        </svg> Hà Nội
                                    </div>
                                </div>
                                <a class="bookmark">
                                    <svg class="icon-svg me-1">
                                        <use xlink:href="images/icons/icon.svg#bookmark"></use>
                                    </svg>
                                </a>
                            </div>
                            <a class="job-name" href="#">Chuyên viên quản lý dữ liệu</a>
                            <div class="job-requirements">
                                <span>Full-time</span>
                                <span>On-site</span>
                                <span class="green">New</span>
                                <span class="orange">
                                    <svg class="icon-svg me-1">
                                        <use xlink:href="images/icons/icon.svg#paid"></use>
                                    </svg>
                                    Bonus Interview
                                </span>
                            </div>
                        </div>
                        <div class="job-bonus">Bonus: $ 800/ Interview</div>
                    </div>
                    <div class="swiper-slide item">
                        <div class="warp">
                            <div class="job-cty">
                                <span class="logo"><img src="images/graphics/fpt.png" alt=""></span>
                                <div class="info">
                                    <div class="name">FPT Software</div>
                                    <div class="address">
                                        <svg class="icon-svg me-1">
                                            <use xlink:href="images/icons/icon.svg#location"></use>
                                        </svg> Hà Nội
                                    </div>
                                </div>
                                <a class="bookmark">
                                    <svg class="icon-svg me-1">
                                        <use xlink:href="images/icons/icon.svg#bookmark"></use>
                                    </svg>
                                </a>
                            </div>
                            <a class="job-name" href="#">Chuyên viên quản lý dữ liệu</a>
                            <div class="job-requirements">
                                <span>Full-time</span>
                                <span>On-site</span>
                                <span class="green">New</span>
                                <span class="orange">
                                    <svg class="icon-svg me-1">
                                        <use xlink:href="images/icons/icon.svg#paid"></use>
                                    </svg>
                                    Bonus Interview
                                </span>
                            </div>
                        </div>
                        <div class="job-bonus">Bonus: $ 800/ Interview</div>
                    </div>
                    <div class="swiper-slide item">
                        <div class="warp">
                            <div class="job-cty">
                                <span class="logo"><img src="images/graphics/fpt.png" alt=""></span>
                                <div class="info">
                                    <div class="name">FPT Software</div>
                                    <div class="address">
                                        <svg class="icon-svg me-1">
                                            <use xlink:href="images/icons/icon.svg#location"></use>
                                        </svg> Hà Nội
                                    </div>
                                </div>
                                <a class="bookmark">
                                    <svg class="icon-svg me-1">
                                        <use xlink:href="images/icons/icon.svg#bookmark"></use>
                                    </svg>
                                </a>
                            </div>
                            <a class="job-name" href="#">Chuyên viên quản lý dữ liệu</a>
                            <div class="job-requirements">
                                <span>Full-time</span>
                                <span>On-site</span>
                                <span class="green">New</span>
                                <span class="orange">
                                    <svg class="icon-svg me-1">
                                        <use xlink:href="images/icons/icon.svg#paid"></use>
                                    </svg>
                                    Bonus Interview
                                </span>
                            </div>
                        </div>
                        <div class="job-bonus">Bonus: $ 800/ Interview</div>
                    </div>
                    <div class="swiper-slide item">
                        <div class="warp">
                            <div class="job-cty">
                                <span class="logo"><img src="images/graphics/fpt.png" alt=""></span>
                                <div class="info">
                                    <div class="name">FPT Software</div>
                                    <div class="address">
                                        <svg class="icon-svg me-1">
                                            <use xlink:href="images/icons/icon.svg#location"></use>
                                        </svg> Hà Nội
                                    </div>
                                </div>
                                <a class="bookmark">
                                    <svg class="icon-svg me-1">
                                        <use xlink:href="images/icons/icon.svg#bookmark"></use>
                                    </svg>
                                </a>
                            </div>
                            <a class="job-name" href="#">Chuyên viên quản lý dữ liệu</a>
                            <div class="job-requirements">
                                <span>Full-time</span>
                                <span>On-site</span>
                                <span class="green">New</span>
                                <span class="orange">
                                    <svg class="icon-svg me-1">
                                        <use xlink:href="images/icons/icon.svg#paid"></use>
                                    </svg>
                                    Bonus Interview
                                </span>
                            </div>
                        </div>
                        <div class="job-bonus">Bonus: $ 800/ Interview</div>
                    </div>
                    <div class="swiper-slide item">
                        <div class="warp">
                            <div class="job-cty">
                                <span class="logo"><img src="images/graphics/fpt.png" alt=""></span>
                                <div class="info">
                                    <div class="name">FPT Software</div>
                                    <div class="address">
                                        <svg class="icon-svg me-1">
                                            <use xlink:href="images/icons/icon.svg#location"></use>
                                        </svg> Hà Nội
                                    </div>
                                </div>
                                <a class="bookmark">
                                    <svg class="icon-svg me-1">
                                        <use xlink:href="images/icons/icon.svg#bookmark"></use>
                                    </svg>
                                </a>
                            </div>
                            <a class="job-name" href="#">Chuyên viên quản lý dữ liệu</a>
                            <div class="job-requirements">
                                <span>Full-time</span>
                                <span>On-site</span>
                                <span class="green">New</span>
                                <span class="orange">
                                    <svg class="icon-svg me-1">
                                        <use xlink:href="images/icons/icon.svg#paid"></use>
                                    </svg>
                                    Bonus Interview
                                </span>
                            </div>
                        </div>
                        <div class="job-bonus">Bonus: $ 800/ Interview</div>
                    </div>
                    <div class="swiper-slide item">
                        <div class="warp">
                            <div class="job-cty">
                                <span class="logo"><img src="images/graphics/fpt.png" alt=""></span>
                                <div class="info">
                                    <div class="name">FPT Software</div>
                                    <div class="address">
                                        <svg class="icon-svg me-1">
                                            <use xlink:href="images/icons/icon.svg#location"></use>
                                        </svg> Hà Nội
                                    </div>
                                </div>
                                <a class="bookmark">
                                    <svg class="icon-svg me-1">
                                        <use xlink:href="images/icons/icon.svg#bookmark"></use>
                                    </svg>
                                </a>
                            </div>
                            <a class="job-name" href="#">Chuyên viên quản lý dữ liệu</a>
                            <div class="job-requirements">
                                <span>Full-time</span>
                                <span>On-site</span>
                                <span class="green">New</span>
                                <span class="orange">
                                    <svg class="icon-svg me-1">
                                        <use xlink:href="images/icons/icon.svg#paid"></use>
                                    </svg>
                                    Bonus Interview
                                </span>
                            </div>
                        </div>
                        <div class="job-bonus">Bonus: $ 800/ Interview</div>
                    </div>
                    <div class="swiper-slide item">
                        <div class="warp">
                            <div class="job-cty">
                                <span class="logo"><img src="images/graphics/fpt.png" alt=""></span>
                                <div class="info">
                                    <div class="name">FPT Software</div>
                                    <div class="address">
                                        <svg class="icon-svg me-1">
                                            <use xlink:href="images/icons/icon.svg#location"></use>
                                        </svg> Hà Nội
                                    </div>
                                </div>
                                <a class="bookmark">
                                    <svg class="icon-svg me-1">
                                        <use xlink:href="images/icons/icon.svg#bookmark"></use>
                                    </svg>
                                </a>
                            </div>
                            <a class="job-name" href="#">Chuyên viên quản lý dữ liệu</a>
                            <div class="job-requirements">
                                <span>Full-time</span>
                                <span>On-site</span>
                                <span class="green">New</span>
                                <span class="orange">
                                    <svg class="icon-svg me-1">
                                        <use xlink:href="images/icons/icon.svg#paid"></use>
                                    </svg>
                                    Bonus Interview
                                </span>
                            </div>
                        </div>
                        <div class="job-bonus">Bonus: $ 800/ Interview</div>
                    </div>
                    <div class="swiper-slide item">
                        <div class="warp">
                            <div class="job-cty">
                                <span class="logo"><img src="images/graphics/fpt.png" alt=""></span>
                                <div class="info">
                                    <div class="name">FPT Software</div>
                                    <div class="address">
                                        <svg class="icon-svg me-1">
                                            <use xlink:href="images/icons/icon.svg#location"></use>
                                        </svg> Hà Nội
                                    </div>
                                </div>
                                <a class="bookmark">
                                    <svg class="icon-svg me-1">
                                        <use xlink:href="images/icons/icon.svg#bookmark"></use>
                                    </svg>
                                </a>
                            </div>
                            <a class="job-name" href="#">Chuyên viên quản lý dữ liệu</a>
                            <div class="job-requirements">
                                <span>Full-time</span>
                                <span>On-site</span>
                                <span class="green">New</span>
                                <span class="orange">
                                    <svg class="icon-svg me-1">
                                        <use xlink:href="images/icons/icon.svg#paid"></use>
                                    </svg>
                                    Bonus Interview
                                </span>
                            </div>
                        </div>
                        <div class="job-bonus">Bonus: $ 800/ Interview</div>
                    </div>
                    <div class="swiper-slide item">
                        <div class="warp">
                            <div class="job-cty">
                                <span class="logo"><img src="images/graphics/fpt.png" alt=""></span>
                                <div class="info">
                                    <div class="name">FPT Software</div>
                                    <div class="address">
                                        <svg class="icon-svg me-1">
                                            <use xlink:href="images/icons/icon.svg#location"></use>
                                        </svg> Hà Nội
                                    </div>
                                </div>
                                <a class="bookmark">
                                    <svg class="icon-svg me-1">
                                        <use xlink:href="images/icons/icon.svg#bookmark"></use>
                                    </svg>
                                </a>
                            </div>
                            <a class="job-name" href="#">Chuyên viên quản lý dữ liệu</a>
                            <div class="job-requirements">
                                <span>Full-time</span>
                                <span>On-site</span>
                                <span class="green">New</span>
                                <span class="orange">
                                    <svg class="icon-svg me-1">
                                        <use xlink:href="images/icons/icon.svg#paid"></use>
                                    </svg>
                                    Bonus Interview
                                </span>
                            </div>
                        </div>
                        <div class="job-bonus">Bonus: $ 800/ Interview</div>
                    </div>
                    <div class="swiper-slide item">
                        <div class="warp">
                            <div class="job-cty">
                                <span class="logo"><img src="images/graphics/fpt.png" alt=""></span>
                                <div class="info">
                                    <div class="name">FPT Software</div>
                                    <div class="address">
                                        <svg class="icon-svg me-1">
                                            <use xlink:href="images/icons/icon.svg#location"></use>
                                        </svg> Hà Nội
                                    </div>
                                </div>
                                <a class="bookmark">
                                    <svg class="icon-svg me-1">
                                        <use xlink:href="images/icons/icon.svg#bookmark"></use>
                                    </svg>
                                </a>
                            </div>
                            <a class="job-name" href="#">Chuyên viên quản lý dữ liệu</a>
                            <div class="job-requirements">
                                <span>Full-time</span>
                                <span>On-site</span>
                                <span class="green">New</span>
                                <span class="orange">
                                    <svg class="icon-svg me-1">
                                        <use xlink:href="images/icons/icon.svg#paid"></use>
                                    </svg>
                                    Bonus Interview
                                </span>
                            </div>
                        </div>
                        <div class="job-bonus">Bonus: $ 800/ Interview</div>
                    </div>
                    <div class="swiper-slide item">
                        <div class="warp">
                            <div class="job-cty">
                                <span class="logo"><img src="images/graphics/fpt.png" alt=""></span>
                                <div class="info">
                                    <div class="name">FPT Software</div>
                                    <div class="address">
                                        <svg class="icon-svg me-1">
                                            <use xlink:href="images/icons/icon.svg#location"></use>
                                        </svg> Hà Nội
                                    </div>
                                </div>
                                <a class="bookmark">
                                    <svg class="icon-svg me-1">
                                        <use xlink:href="images/icons/icon.svg#bookmark"></use>
                                    </svg>
                                </a>
                            </div>
                            <a class="job-name" href="#">Chuyên viên quản lý dữ liệu</a>
                            <div class="job-requirements">
                                <span>Full-time</span>
                                <span>On-site</span>
                                <span class="green">New</span>
                                <span class="orange">
                                    <svg class="icon-svg me-1">
                                        <use xlink:href="images/icons/icon.svg#paid"></use>
                                    </svg>
                                    Bonus Interview
                                </span>
                            </div>
                        </div>
                        <div class="job-bonus">Bonus: $ 800/ Interview</div>
                    </div>
                    <div class="swiper-slide item">
                        <div class="warp">
                            <div class="job-cty">
                                <span class="logo"><img src="images/graphics/fpt.png" alt=""></span>
                                <div class="info">
                                    <div class="name">FPT Software</div>
                                    <div class="address">
                                        <svg class="icon-svg me-1">
                                            <use xlink:href="images/icons/icon.svg#location"></use>
                                        </svg> Hà Nội
                                    </div>
                                </div>
                                <a class="bookmark">
                                    <svg class="icon-svg me-1">
                                        <use xlink:href="images/icons/icon.svg#bookmark"></use>
                                    </svg>
                                </a>
                            </div>
                            <a class="job-name" href="#">Chuyên viên quản lý dữ liệu</a>
                            <div class="job-requirements">
                                <span>Full-time</span>
                                <span>On-site</span>
                                <span class="green">New</span>
                                <span class="orange">
                                    <svg class="icon-svg me-1">
                                        <use xlink:href="images/icons/icon.svg#paid"></use>
                                    </svg>
                                    Bonus Interview
                                </span>
                            </div>
                        </div>
                        <div class="job-bonus">Bonus: $ 800/ Interview</div>
                    </div>

                </div>
            </div>
            <!-- Add Arrows -->
            <div class="swiper-button-next"></div>
            <div class="swiper-button-prev"></div>
            <div class="center">
                <a class="btn-default min none">Xem tất cả</a>
            </div>
        </div>
    </div>
</section>

<section class="section-home-4 clearfix mb80">
    <div class="container">
        <div class="row">
            <div class="col-md-7 mb-4 aos-init aos-animate" data-aos="fade-left" data-aos-duration="1000"
                data-aos-delay="500">
                <h2 class="title-cate2 mb30">Vì sao lại chọn Recland?</h2>
                <ul class="nav nav-tabs" id="myTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="hr-tab" data-bs-toggle="tab" data-bs-target="#hr"
                            type="button" role="tab" aria-controls="hr" aria-selected="true">Đối với HR</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="tuyendung-tab" data-bs-toggle="tab" data-bs-target="#tuyendung"
                            type="button" role="tab" aria-controls="tuyendung" aria-selected="false">Đối với Nhà tuyển
                            dụng</button>
                    </li>
                </ul>
                <div class="tab-content" id="myTabContent">
                    <div class="tab-pane fade show active" id="hr" role="tabpanel" aria-labelledby="hr-tab">
                        <ul>
                            <li>
                                <span class="icon">
                                    <svg class="icon-svg">
                                        <use xlink:href="images/icons/icon.svg#web"></use>
                                    </svg>
                                </span>
                                <div class="content">
                                    <h3>Đa dạng việc làm, thêm nhiều lựa chọn hấp dẫn</h3>
                                    <div>Hơn 200+ việc làm IT đa dạng, cung cấp việc làm đến những ứng viên phù hợp.
                                    </div>
                                </div>
                            </li>
                            <li>
                                <span class="icon">
                                    <svg class="icon-svg">
                                        <use xlink:href="images/icons/icon.svg#currency_exchange"></use>
                                    </svg>
                                </span>
                                <div class="content">
                                    <h3>Cơ chế hoa hồng hấp dẫn, rõ ràng và minh bạch</h3>
                                    <div>Recland cung cấp chính sách cộng tác viên rõ ràng, minh bạch với mức hoa hồng
                                        cực hấp dẫn.</div>
                                </div>
                            </li>
                            <li>
                                <span class="icon">
                                    <svg class="icon-svg">
                                        <use xlink:href="images/icons/icon.svg#signpost"></use>
                                    </svg>
                                </span>
                                <div class="content">
                                    <h3>Cách thức làm việc đơn giản, hiệu quả</h3>
                                    <div>Bạn có thể làm việc bất cứ đâu và bất cứ khi nào mà vẫn đảm bảo nguồn thu nhập.
                                    </div>
                                </div>
                            </li>
                            <li>
                                <span class="icon">
                                    <svg class="icon-svg">
                                        <use xlink:href="images/icons/icon.svg#gpp_good"></use>
                                    </svg>
                                </span>
                                <div class="content">
                                    <h3>Bảo mật thông tin</h3>
                                    <div>Recland cam kết bảo mật thông tin tuyệt đối cho cộng tác viên và ứng viên.
                                    </div>
                                </div>
                            </li>
                            <li>
                                <span class="icon">
                                    <svg class="icon-svg">
                                        <use xlink:href="images/icons/icon.svg#group_add"></use>
                                    </svg>
                                </span>
                                <div class="content">
                                    <h3>Mở rộng network từ cộng đồng</h3>
                                    <div>Cơ hội phát triển cùng Recland và mở rộng network trong lĩnh vực tuyển dụng.
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                    <div class="tab-pane fade" id="tuyendung" role="tabpanel" aria-labelledby="tuyendung-tab">
                        <ul>
                            <li>
                                <span class="icon">
                                    <svg class="icon-svg">
                                        <use xlink:href="images/icons/icon.svg#web"></use>
                                    </svg>
                                </span>
                                <div class="content">
                                    <h3>Đa dạng việc làm, thêm nhiều lựa chọn hấp dẫn</h3>
                                    <div>Hơn 200+ việc làm IT đa dạng, cung cấp việc làm đến những ứng viên phù hợp.
                                    </div>
                                </div>
                            </li>
                            <li>
                                <span class="icon">
                                    <svg class="icon-svg">
                                        <use xlink:href="images/icons/icon.svg#currency_exchange"></use>
                                    </svg>
                                </span>
                                <div class="content">
                                    <h3>Cơ chế hoa hồng hấp dẫn, rõ ràng và minh bạch</h3>
                                    <div>Recland cung cấp chính sách cộng tác viên rõ ràng, minh bạch với mức hoa hồng
                                        cực hấp dẫn.</div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-5 fade-in-right">
                <picture>
                    <source media="(min-width:767px)" srcset="images/graphics/recland.png">
                    <img width="100%" src="images/graphics/m_recland.png" alt="">
                </picture>
            </div>
        </div>
    </div>
</section>

<section class="section-home-5 clearfix mb80">
    <div class="container">
        <div class="box-tangtruong aos-init aos-animate" data-aos="fade-up" data-aos-duration="1000"
            data-aos-delay="300">
            <div class="grid grid__3">
                <div class="item">
                    <span class="icon">
                        <img src="images/icons/icon-briefcase.png" alt="">
                    </span>
                    <div class="content">
                        <div class="number">
                            <strong class="numscroller" data-min='1' data-max='22260' data-delay='1.5'
                                data-increment='20'></strong>
                        </div>
                        <div>Việc làm tại Recland</div>
                    </div>
                </div>
                <div class="item">
                    <span class="icon">
                        <img src="images/icons/icon-office.png" alt="">
                    </span>
                    <div class="content">
                        <div class="number">
                            <strong class="numscroller" data-min='1' data-max='1543' data-delay='1.5'
                                data-increment='10'></strong>
                        </div>
                        <div>Nhà tuyển dụng</div>
                    </div>
                </div>
                <div class="item">
                    <span class="icon">
                        <img src="images/icons/icon-loan.png" alt="">
                    </span>
                    <div class="content">
                        <div class="number">
                            <strong class="numscroller" data-min='1' data-max='22260' data-delay='1.5'
                                data-increment='20'></strong>
                        </div>
                        <div>CTV đã nhận thưởng</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>


<section class="section-home-6 clearfix mb80 aos-init aos-animate" data-aos="fade-down" data-aos-duration="1000"
    data-aos-delay="300">
    <div class="container">
        <h2 class="mb30 center title-cate2">Phản hồi từ Cộng Tác Viên tuyển dụng</h2>
        <div id="slide-ctv" class="relative">
            <div class="swiper-container">
                <div class="swiper-wrapper">
                    <div class="swiper-slide item">
                        <div class="info">
                            <a class="thumb_img thumb_5x5" href="#">
                                <img src="images/graphics/ava.jpg" alt="">
                            </a>
                            <div class="text">
                                <h4 class="name">Dang Thi Hoai Thu</h4>
                                <div class="position">Headhunter</div>
                            </div>
                        </div>
                        <div class="content">
                            “The work process and progress are very fast. Normally, my profile will be checked within
                            the same day. The job source is stable. I highly recommend HR professionals to join for
                            networking and to earn additional income.”
                        </div>
                    </div>
                    <div class="swiper-slide item">
                        <div class="info">
                            <a class="thumb_img thumb_5x5" href="#">
                                <img src="images/graphics/ava.jpg" alt="">
                            </a>
                            <div class="text">
                                <h4 class="name">Dang Thi Hoai Thu</h4>
                                <div class="position">Headhunter</div>
                            </div>
                        </div>
                        <div class="content">
                            “The work process and progress are very fast. Normally, my profile will be checked within
                            the same day. The job source is stable. I highly recommend HR professionals to join for
                            networking and to earn additional income.”
                        </div>
                    </div>
                    <div class="swiper-slide item">
                        <div class="info">
                            <a class="thumb_img thumb_5x5" href="#">
                                <img src="images/graphics/ava.jpg" alt="">
                            </a>
                            <div class="text">
                                <h4 class="name">Dang Thi Hoai Thu</h4>
                                <div class="position">Headhunter</div>
                            </div>
                        </div>
                        <div class="content">
                            “The work process and progress are very fast. Normally, my profile will be checked within
                            the same day. The job source is stable. I highly recommend HR professionals to join for
                            networking and to earn additional income.”
                        </div>
                    </div>
                    <div class="swiper-slide item">
                        <div class="info">
                            <a class="thumb_img thumb_5x5" href="#">
                                <img src="images/graphics/ava.jpg" alt="">
                            </a>
                            <div class="text">
                                <h4 class="name">Dang Thi Hoai Thu</h4>
                                <div class="position">Headhunter</div>
                            </div>
                        </div>
                        <div class="content">
                            “The work process and progress are very fast. Normally, my profile will be checked within
                            the same day. The job source is stable. I highly recommend HR professionals to join for
                            networking and to earn additional income.”
                        </div>
                    </div>
                    <div class="swiper-slide item">
                        <div class="info">
                            <a class="thumb_img thumb_5x5" href="#">
                                <img src="images/graphics/ava.jpg" alt="">
                            </a>
                            <div class="text">
                                <h4 class="name">Dang Thi Hoai Thu</h4>
                                <div class="position">Headhunter</div>
                            </div>
                        </div>
                        <div class="content">
                            “The work process and progress are very fast. Normally, my profile will be checked within
                            the same day. The job source is stable. I highly recommend HR professionals to join for
                            networking and to earn additional income.”
                        </div>
                    </div>
                    <div class="swiper-slide item">
                        <div class="info">
                            <a class="thumb_img thumb_5x5" href="#">
                                <img src="images/graphics/ava.jpg" alt="">
                            </a>
                            <div class="text">
                                <h4 class="name">Dang Thi Hoai Thu</h4>
                                <div class="position">Headhunter</div>
                            </div>
                        </div>
                        <div class="content">
                            “The work process and progress are very fast. Normally, my profile will be checked within
                            the same day. The job source is stable. I highly recommend HR professionals to join for
                            networking and to earn additional income.”
                        </div>
                    </div>
                </div>
            </div>
            <div class="control-slide mt30">
                <div class="swiper-button-prev"></div>
                <div class="page">
                    <div class="swiper-pagination"></div> page
                </div>
                <div class="swiper-button-next"></div>
            </div>
        </div>
    </div>
</section>


<!-- /build:content -->
<!-- build:script2 -->
<script>
    $('.topnav').addClass('home');
</script>
<!-- /build:script2 -->