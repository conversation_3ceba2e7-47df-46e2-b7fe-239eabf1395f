# Tài liệu Đặc tả Hệ thống Notification/Email - RecLand

**Phiên bản:** 2.0  
**Ngày:** 2025-01-09  
**T<PERSON><PERSON> gi<PERSON>:** AI Assistant

---

## 1. Tổ<PERSON> quan Hệ thống Notification

### 1.1. Kiến trúc Notification System

RecLand sử dụng Laravel Notification system với các channels:

-   **Mail Channel:** Email notifications qua SMTP/Mailgun
-   **Database Channel:** In-app notifications lưu trong bảng `notifications`
-   **Queue Channel:** Notifications được queue để xử lý bất đồng bộ

### 1.2. <PERSON><PERSON> loại Notifications

| Loại                     | Số lượng         | Mô tả                                  |
| ------------------------ | ---------------- | -------------------------------------- |
| **User Management**      | 8 notifications  | Đ<PERSON><PERSON> ký, x<PERSON><PERSON> thực, reset password      |
| **CV & Job Management**  | 12 notifications | Submit CV, mua CV, cập nhật trạng thái |
| **Payment & Commission** | 10 notifications | Thanh toán, hoa hồng, nạp tiền         |
| **Interview & Onboard**  | 15 notifications | Lịch phỏng vấn, thử việc, tuyển dụng   |
| **Complaint & Support**  | 8 notifications  | Khiếu nại, hỗ trợ, báo cáo             |
| **System & Admin**       | 6 notifications  | Thông báo hệ thống, admin alerts       |

---

## 2. User Management Notifications

### 2.1. RegisterSuccess - Đăng ký thành công

**File:** `app/Notifications/RegisterSuccess.php`

```php
class RegisterSuccess extends Notification implements ShouldQueue {
    use Queueable;

    protected $user;

    public function via($notifiable) {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail', 'database'];
    }

    public function toMail($notifiable) {
        return (new MailMessage)
            ->view('email.registerSuccess', ['name' => $this->user->name])
            ->subject('[Recland] [KÍCH HOẠT TÀI KHOẢN THÀNH CÔNG]');
    }

    public function toDatabase($notifiable) {
        return [
            'type' => 'register_success',
            'title' => 'Đăng ký thành công',
            'message' => 'Tài khoản của bạn đã được kích hoạt thành công.',
            'user_id' => $this->user->id
        ];
    }
}
```

**Trigger:** Sau khi user đăng ký và xác thực email thành công
**Recipients:** User vừa đăng ký
**Template:** `resources/views/email/registerSuccess.blade.php`

### 2.2. RegisterEmployer - Đăng ký nhà tuyển dụng

**File:** `app/Notifications/RegisterEmployer.php`

```php
class RegisterEmployer extends Notification implements ShouldQueue {
    public function toMail($notifiable) {
        return (new MailMessage)
            ->view('email.registerEmployer', ['name' => $this->user->name])
            ->subject('[Recland] [ĐĂNG KÝ TÀI KHOẢN THÀNH CÔNG]');
    }
}
```

**Trigger:** Khi employer đăng ký tài khoản
**Recipients:** Employer vừa đăng ký
**Template:** `resources/views/email/registerEmployer.blade.php`
**Content:** Thông báo chờ xác thực trong 24h

### 2.3. ResetPassword - Đặt lại mật khẩu

**File:** `app/Notifications/ResetPassword.php`

```php
class ResetPassword extends Notification {
    public $token;
    public $user;
    public $url;

    public function toMail($notifiable) {
        $url = $this->url ?
            $this->url . '?token=' . $this->token :
            config('constant.url') . '/forgot-password/?token=' . $this->token;

        return (new MailMessage)
            ->action('Reset Password', url($url))
            ->view('email.resetPassword', [
                'name' => $this->user->name,
                'url' => $url,
                'title' => 'BẠN ĐÃ QUÊN MẬT KHẨU CỦA MÌNH?',
                'titleEn' => 'VERIFY YOUR EMAIL ADDRESS'
            ])
            ->subject('[Recland] [ĐẶT LẠI MẬT KHẨU]');
    }
}
```

**Trigger:** Khi user yêu cầu reset password
**Recipients:** User yêu cầu reset
**Template:** `resources/views/email/resetPassword.blade.php`

---

## 3. CV & Job Management Notifications

### 3.1. BuyCvSuccess - Mua CV thành công

**File:** `app/Notifications/BuyCvSuccess.php`

```php
class BuyCvSuccess extends Notification implements ShouldQueue {
    protected $wareHouseCvSellingBuy;

    public function toMail($notifiable) {
        $candidateName = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;
        $employerName = $this->wareHouseCvSellingBuy->employer->name;
        $position = $this->wareHouseCvSellingBuy->job->name ?? 'N/A';
        $url = route('rec-cv-sold', ['cv_sold' => $this->wareHouseCvSellingBuy->id]);

        return (new MailMessage)
            ->view('email.buyCvSuccess', [
                'candidateName' => $candidateName,
                'employerName' => $employerName,
                'position' => $position,
                'url' => $url,
                'buyDate' => $this->wareHouseCvSellingBuy->created_at->format('d/m/Y')
            ])
            ->subject('[RECLAND] CV của bạn đã được mua bởi ' . $employerName);
    }
}
```

**Trigger:** Khi NTD mua CV từ marketplace
**Recipients:** CTV bán CV
**Template:** `resources/views/email/buyCvSuccess.blade.php`

### 3.2. RecNewCv - Thêm CV mới thành công

**File:** `app/Notifications/RecNewCv.php`

```php
class RecNewCv extends Notification implements ShouldQueue {
    public function via($notifiable) {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail', 'database'];
    }

    public function toMail($notifiable) {
        return (new MailMessage)
            ->view('email.recNewCv', ['name' => $this->user->name])
            ->subject('[Recland] [THÔNG BÁO THÊM MỚI CV THÀNH CÔNG]');
    }
}
```

**Trigger:** Khi CTV thêm CV mới vào warehouse
**Recipients:** CTV thêm CV
**Template:** `resources/views/email/recNewCv.blade.php`

### 3.3. EmployerIntroduceCandidate - Giới thiệu ứng viên

**File:** `app/Notifications/EmployerIntroduceCandidate.php`

```php
class EmployerIntroduceCandidate extends Notification implements ShouldQueue {
    protected $submitCv;

    public function toMail($notifiable) {
        $candidateName = $this->submitCv->warehouseCv->candidate_name;
        $recName = $this->submitCv->rec->name;
        $position = $this->submitCv->job->name;
        $url = route('employer-submitcv', ['submit_id' => $this->submitCv->id]);

        return (new MailMessage)
            ->view('email.employerIntroduceCandidate', [
                'candidateName' => $candidateName,
                'recName' => $recName,
                'position' => $position,
                'url' => $url,
                'submitDate' => $this->submitCv->created_at->format('d/m/Y H:i')
            ])
            ->subject('[RECLAND] Có ứng viên mới cho vị trí ' . $position);
    }
}
```

**Trigger:** Khi CTV submit CV vào job
**Recipients:** Employer của job
**Template:** `resources/views/email/employerIntroduceCandidate.blade.php`

---

## 4. Payment & Commission Notifications

### 4.1. PaymentOpenTurnCv - Thanh toán hoa hồng CV

**File:** `app/Notifications/PaymentOpenTurnCv.php`

```php
class PaymentOpenTurnCv extends Notification implements ShouldQueue {
    protected $wareHouseCvSellingBuy;
    protected $point;

    public function toMail($notifiable) {
        $candidateName = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;
        $employerName = $this->wareHouseCvSellingBuy->employer->name;
        $companyName = $this->wareHouseCvSellingBuy->employer->company_name;
        $url = route('rec-cv-sold', ['cv_sold' => $this->wareHouseCvSellingBuy->id]);
        $recTurnovers = route('rec-turnovers');

        return (new MailMessage)
            ->view('email.paymentOpenTurnCv', [
                'candidateName' => $candidateName,
                'employerName' => $employerName,
                'companyName' => $companyName,
                'url' => $url,
                'point' => $this->point,
                'recTurnovers' => $recTurnovers,
                'buyDate' => $this->wareHouseCvSellingBuy->created_at->format('d/m/Y')
            ])
            ->subject('[RECLAND] Thanh toán thành công CV ' . $candidateName);
    }
}
```

**Trigger:** Sau 7 ngày không khiếu nại, thanh toán hoa hồng cho CTV
**Recipients:** CTV bán CV
**Template:** `resources/views/email/paymentOpenTurnCv.blade.php`

### 4.2. PaymentInterviewToRec - Thanh toán hoa hồng Interview

**File:** `app/Notifications/PaymentInterviewToRec.php`

```php
class PaymentInterviewToRec extends Notification implements ShouldQueue {
    protected $wareHouseCvSellingBuy;
    protected $point;

    public function toMail($notifiable) {
        $candidateName = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;
        $position = $this->wareHouseCvSellingBuy->job->name;
        $employerName = $this->wareHouseCvSellingBuy->employer->name;

        return (new MailMessage)
            ->view('email.paymentInterviewToRec', [
                'candidateName' => $candidateName,
                'position' => $position,
                'employerName' => $employerName,
                'point' => $this->point,
                'interviewDate' => $this->wareHouseCvSellingBuy->date_change_status->format('d/m/Y')
            ])
            ->subject('[RECLAND] Thanh toán phỏng vấn thành công - ' . $candidateName);
    }
}
```

**Trigger:** Sau 24h khi ứng viên pass interview
**Recipients:** CTV
**Template:** `resources/views/email/paymentInterviewToRec.blade.php`

### 4.3. DepositToEmployer - Nạp tiền thành công

**File:** `app/Notifications/DepositToEmployer.php`

```php
class DepositToEmployer extends Notification implements ShouldQueue {
    protected $deposit;
    protected $employer;

    public function toMail($notifiable) {
        return (new MailMessage)
            ->view('email.depositToEmployer', [
                'id' => $this->deposit->id,
                'employerName' => $this->employer->name,
                'amount' => number_format($this->deposit->amount),
                'link' => route('employer-wallet')
            ])
            ->subject('[RECLAND] Thanh toán thành công đơn hàng số ' . $this->deposit->id);
    }
}
```

**Trigger:** Khi employer nạp tiền thành công
**Recipients:** Employer
**Template:** `resources/views/email/depositToEmployer.blade.php`

---

## 5. Interview & Onboard Notifications

### 5.1. EmailConfirmInterView - Xác nhận lịch phỏng vấn

**File:** `app/Notifications/EmailConfirmInterView.php`

```php
class EmailConfirmInterView extends Notification implements ShouldQueue {
    protected $wareHouseCvSellingBuyBook;

    public function toMail($notifiable) {
        $booking = $this->wareHouseCvSellingBuyBook;
        $candidateName = $booking->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;
        $employerName = $booking->wareHouseCvSellingBuy->employer->name;
        $companyName = $booking->wareHouseCvSellingBuy->employer->company_name;
        $position = $booking->wareHouseCvSellingBuy->job->name;
        $timeInterview = $booking->time_interview;
        $address = $booking->address;
        $link = route('employer-cv-bought', ['update-status' => $booking->wareHouseCvSellingBuy->id]);

        return (new MailMessage)
            ->view('email.emailConfirmInterView', [
                'candidateName' => $candidateName,
                'employerName' => $employerName,
                'companyName' => $companyName,
                'position' => $position,
                'type' => $booking->wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale,
                'timeInterview' => $timeInterview,
                'address' => $address,
                'link' => $link
            ])
            ->subject('[RECLAND] Xác nhận lịch phỏng vấn - ' . $candidateName);
    }
}
```

**Trigger:** Khi CTV xác nhận lịch phỏng vấn
**Recipients:** Employer
**Template:** `resources/views/email/emailConfirmInterView.blade.php`

### 5.2. ChangeStatusTrailWorkToRec - Thông báo thử việc

**File:** `app/Notifications/ChangeStatusTrailWorkToRec.php`

```php
class ChangeStatusTrailWorkToRec extends Notification implements ShouldQueue {
    protected $wareHouseCvSellingBuy;

    public function toMail($notifiable) {
        $candidateName = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;
        $companyName = $this->wareHouseCvSellingBuy->employer->name;
        $position = $this->wareHouseCvSellingBuy->job->name;
        $linkDetail = route('rec-cv-sold', ['cv_sold' => $this->wareHouseCvSellingBuy->id]);

        return (new MailMessage)
            ->view('email.changeStatusTrailWorkToRec', [
                'candidateName' => $candidateName,
                'companyName' => $companyName,
                'position' => $position,
                'recName' => $notifiable->name,
                'linkDetail' => $linkDetail,
                'startDate' => $this->wareHouseCvSellingBuy->date_book->format('d/m/Y')
            ])
            ->subject('[Recland] Ứng viên ' . $candidateName . ' bắt đầu ngày làm việc đầu tiên tại công ty ' . $companyName);
    }
}
```

**Trigger:** Khi ứng viên bắt đầu thử việc
**Recipients:** CTV
**Template:** `resources/views/email/changeStatusTrailWorkToRec.blade.php`

### 5.3. RemindExpireTrailWork - Nhắc nhở hết hạn thử việc

**File:** `app/Notifications/RemindExpireTrailWork.php`

```php
class RemindExpireTrailWork extends Notification implements ShouldQueue {
    protected $wareHouseCvSellingBuy;
    protected $remaining;

    public function toMail($notifiable) {
        $candidateName = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;
        $employerName = $this->wareHouseCvSellingBuy->employer->name;
        $position = $this->wareHouseCvSellingBuy->job->name;
        $link = route('employer-cv-bought', ['update-status' => $this->wareHouseCvSellingBuy->id]);

        return (new MailMessage)
            ->view('email.remindExpireTrailWork', [
                'candidateName' => $candidateName,
                'employerName' => $employerName,
                'position' => $position,
                'remaining' => 60 - $this->remaining, // Số ngày còn lại
                'link' => $link
            ])
            ->subject('[Recland] Thông báo cập nhật kết quả thử việc ứng viên ' . $candidateName . ' vị trí ' . $position);
    }
}
```

**Trigger:** Từ ngày 55-59 của thời gian thử việc (60 ngày)
**Recipients:** Employer
**Template:** `resources/views/email/remindExpireTrailWork.blade.php`

---

## 6. Payment Reminder Notifications

### 6.1. RemindPaymentDebit - Nhắc nhở thanh toán nợ (MarketCV)

**File:** `app/Notifications/RemindPaymentDebit.php`

```php
class RemindPaymentDebit extends Notification implements ShouldQueue {
    protected $wareHouseCvSellingBuy;

    public function toMail($notifiable) {
        $candidateName = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;
        $position = $this->wareHouseCvSellingBuy->job->name;
        $employerName = $this->wareHouseCvSellingBuy->employer->name;
        $point = $this->wareHouseCvSellingBuy->point;
        $point = $point - (0.1 * $point); // Trừ 10% đã thanh toán
        $employerWallet = route('employer-wallet', ['deposit' => 1]);

        return (new MailMessage)
            ->view('email.remindPaymentDebit', [
                'candidateName' => $candidateName,
                'position' => $position,
                'employerName' => $employerName,
                'point' => $point,
                'employerWallet' => $employerWallet,
                'dueDate' => now()->addDays(15)->format('d/m/Y')
            ])
            ->subject('[Recland] Thông báo thanh toán chi phí tuyển dụng ứng viên ' . $candidateName);
    }
}
```

**Trigger:** Khi chuyển sang trial work mà không đủ số dư
**Recipients:** Employer
**Template:** `resources/views/email/remindPaymentDebit.blade.php`

### 6.2. RemindPaymentDebitSubmit - Nhắc nhở thanh toán nợ (Submit CV)

**File:** `app/Notifications/RemindPaymentDebitSubmit.php`

Tương tự `RemindPaymentDebit` nhưng cho luồng Submit CV.

---

## 7. Complaint & Support Notifications

### 7.1. EmployerComplain - Khiếu nại từ Employer

**File:** `app/Notifications/EmployerComplain.php`

```php
class EmployerComplain extends Notification implements ShouldQueue {
    protected $wareHouseCvSellingBuy;

    public function toMail($notifiable) {
        $candidateName = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;
        $employerName = $this->wareHouseCvSellingBuy->employer->name;
        $position = $this->wareHouseCvSellingBuy->job->name;
        $complainReason = $this->wareHouseCvSellingBuy->txt_complain;
        $url = route('rec-cv-sold', ['cv_sold' => $this->wareHouseCvSellingBuy->id]);

        return (new MailMessage)
            ->view('email.employerComplain', [
                'candidateName' => $candidateName,
                'employerName' => $employerName,
                'position' => $position,
                'complainReason' => $complainReason,
                'url' => $url,
                'complainDate' => $this->wareHouseCvSellingBuy->date_complain->format('d/m/Y H:i')
            ])
            ->subject('[RECLAND] Khiếu nại từ ' . $employerName . ' về ứng viên ' . $candidateName);
    }
}
```

**Trigger:** Khi employer tạo khiếu nại
**Recipients:** CTV bán CV
**Template:** `resources/views/email/employerComplain.blade.php`

### 7.2. ComplaintsAcceptedToEmployer - Chấp nhận khiếu nại

**File:** `app/Notifications/ComplaintsAcceptedToEmployer.php`

```php
class ComplaintsAcceptedToEmployer extends Notification implements ShouldQueue {
    protected $wareHouseCvSellingBuy;

    public function toMail($notifiable) {
        $candidateName = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;
        $refundAmount = $this->calculateRefundAmount();
        $url = route('employer-cv-bought', ['update-status' => $this->wareHouseCvSellingBuy->id]);

        return (new MailMessage)
            ->view('email.complaintsAcceptedToEmployer', [
                'candidateName' => $candidateName,
                'refundAmount' => number_format($refundAmount),
                'url' => $url,
                'processDate' => now()->format('d/m/Y H:i')
            ])
            ->subject('[RECLAND] Khiếu nại được chấp nhận - Hoàn tiền ' . number_format($refundAmount) . ' USD');
    }
}
```

**Trigger:** Khi admin chấp nhận khiếu nại
**Recipients:** Employer khiếu nại
**Template:** `resources/views/email/complaintsAcceptedToEmployer.blade.php`

---

## 8. System & Admin Notifications

### 8.1. AdminReportSystem - Báo cáo hệ thống

**File:** `app/Notifications/AdminReportSystem.php`

```php
class AdminReportSystem extends Notification implements ShouldQueue {
    protected $report;

    public function toMail($notifiable) {
        return (new MailMessage)
            ->view('email.adminReportSystem', [
                'reportType' => $this->report->type,
                'reportContent' => $this->report->content,
                'reporterName' => $this->report->user->name,
                'reportDate' => $this->report->created_at->format('d/m/Y H:i'),
                'adminUrl' => config('app.admin_url')
            ])
            ->subject('[RECLAND ADMIN] Báo cáo hệ thống mới');
    }
}
```

**Trigger:** Khi user gửi báo cáo hệ thống
**Recipients:** Admin
**Template:** `resources/views/email/adminReportSystem.blade.php`

### 8.2. CheckCvExpired - Kiểm tra CV hết hạn

**File:** `app/Notifications/CheckCvExpired.php`

```php
class CheckCvExpired extends Notification implements ShouldQueue {
    protected $expiredCvs;

    public function toMail($notifiable) {
        return (new MailMessage)
            ->view('email.checkCvExpired', [
                'expiredCount' => count($this->expiredCvs),
                'expiredCvs' => $this->expiredCvs,
                'checkDate' => now()->format('d/m/Y H:i')
            ])
            ->subject('[RECLAND ADMIN] Báo cáo CV hết hạn - ' . count($this->expiredCvs) . ' CV');
    }
}
```

**Trigger:** Scheduled job hàng ngày
**Recipients:** Admin
**Template:** `resources/views/email/checkCvExpired.blade.php`

---

## 9. Email Templates Structure

### 9.1. Master Template

**File:** `resources/views/email/master.blade.php`

```html
<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>{{ $subject ?? 'RecLand Notification' }}</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 0;
                background-color: #f4f4f4;
            }
            .container {
                max-width: 600px;
                margin: 0 auto;
                background-color: #ffffff;
            }
            .header {
                background-color: #2c5aa0;
                padding: 20px;
                text-align: center;
            }
            .header img {
                max-height: 50px;
            }
            .content {
                padding: 20px;
            }
            .footer {
                background-color: #f8f9fa;
                padding: 15px;
                text-align: center;
                font-size: 12px;
                color: #666;
            }
            .button {
                display: inline-block;
                padding: 12px 24px;
                background-color: #2c5aa0;
                color: white;
                text-decoration: none;
                border-radius: 4px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <img
                    src="{{ asset('images/logo-recland.png') }}"
                    alt="RecLand Logo"
                />
            </div>

            <div class="content">@yield('content')</div>

            <div class="footer">
                <p>© {{ date('Y') }} RecLand. All rights reserved.</p>
                <p>
                    Fanpage:
                    <a href="https://www.facebook.com/Reclandco"
                        >https://www.facebook.com/Reclandco</a
                    ><br />
                    Zalo: {{ config('settings.global.group_zalo_link') }}<br />
                    Email: {{ config('settings.global.email_admin') }}
                </p>
            </div>
        </div>
    </body>
</html>
```

### 9.2. Common Template Variables

Tất cả email templates đều có access đến các biến sau:

```php
// User information
$name, $email, $userType

// Company information
$companyName, $employerName

// Candidate information
$candidateName, $candidateEmail, $candidateMobile

// Job information
$position, $jobName, $jobId

// Transaction information
$point, $amount, $transactionId, $paymentDate

// URLs
$url, $link, $adminUrl, $employerWallet, $recTurnovers

// Dates
$buyDate, $submitDate, $interviewDate, $startDate, $dueDate
```

---

## 10. Notification Configuration

### 10.1. Mail Configuration

**File:** `config/mail.php`

```php
return [
    'default' => env('MAIL_MAILER', 'smtp'),

    'mailers' => [
        'smtp' => [
            'transport' => 'smtp',
            'host' => env('MAIL_HOST', 'smtp.mailgun.org'),
            'port' => env('MAIL_PORT', 587),
            'encryption' => env('MAIL_ENCRYPTION', 'tls'),
            'username' => env('MAIL_USERNAME'),
            'password' => env('MAIL_PASSWORD'),
            'timeout' => null,
        ],
    ],

    'from' => [
        'address' => env('MAIL_FROM_ADDRESS', '<EMAIL>'),
        'name' => env('MAIL_FROM_NAME', 'RecLand'),
    ],
];
```

### 10.2. Queue Configuration cho Notifications

```php
// config/queue.php
'connections' => [
    'database' => [
        'driver' => 'database',
        'table' => 'jobs',
        'queue' => 'default',
        'retry_after' => 90,
    ],
],

// Notification-specific queues
'notifications' => [
    'high_priority' => ['payment', 'complaint'],
    'normal_priority' => ['status_change', 'reminder'],
    'low_priority' => ['system', 'report']
]
```

---

## 11. In-App Notification System

### 11.1. Database Notifications

**Table:** `notifications`

```sql
CREATE TABLE notifications (
    id CHAR(36) PRIMARY KEY,
    type VARCHAR(255) NOT NULL,
    notifiable_type VARCHAR(255) NOT NULL,
    notifiable_id BIGINT UNSIGNED NOT NULL,
    data TEXT NOT NULL,
    read_at TIMESTAMP NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,

    INDEX idx_notifiable (notifiable_type, notifiable_id),
    INDEX idx_read_at (read_at),
    INDEX idx_created_at (created_at)
);
```

### 11.2. Notification Data Structure

```php
// Database notification data format
{
    "type": "cv_purchased",
    "title": "CV đã được mua",
    "message": "CV của ứng viên John Doe đã được mua bởi ABC Company",
    "action_url": "/rec/cv-sold/123",
    "action_text": "Xem chi tiết",
    "priority": "high",
    "metadata": {
        "cv_id": 123,
        "buyer_id": 456,
        "amount": 50
    }
}
```

### 11.3. Frontend Notification Display

```javascript
// Vue.js component for notifications
export default {
    data() {
        return {
            notifications: [],
            unreadCount: 0,
        };
    },

    mounted() {
        this.fetchNotifications();
        this.setupRealTimeUpdates();
    },

    methods: {
        fetchNotifications() {
            axios.get("/api/notifications").then((response) => {
                this.notifications = response.data.data;
                this.unreadCount = response.data.unread_count;
            });
        },

        markAsRead(notificationId) {
            axios.post(`/api/notifications/${notificationId}/read`).then(() => {
                this.updateNotificationStatus(notificationId);
            });
        },
    },
};
```

---

## 12. Email Logging & Tracking

### 12.1. Email Log Table

**Table:** `email_logs`

```sql
CREATE TABLE email_logs (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    from_email VARCHAR(255) NOT NULL,
    to_email VARCHAR(255) NOT NULL,
    cc_email VARCHAR(255) NULL,
    subject VARCHAR(255) NOT NULL,
    html_content LONGTEXT NOT NULL,
    hash VARCHAR(255) NOT NULL,
    status TINYINT DEFAULT 0 COMMENT '0: pending, 1: sent, 2: failed',
    sent_at TIMESTAMP NULL,
    error_message TEXT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,

    INDEX idx_hash (hash),
    INDEX idx_to_email (to_email),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);
```

### 12.2. Email Logging Service

```php
class EmailLogService {
    public function logEmail($from, $to, $subject, $content, $cc = null) {
        $hash = md5($to . $subject . time());

        return EmailLog::create([
            'from_email' => $from,
            'to_email' => $to,
            'cc_email' => $cc,
            'subject' => $subject,
            'html_content' => $content,
            'hash' => $hash,
            'status' => 0
        ]);
    }

    public function markAsSent($hash) {
        EmailLog::where('hash', $hash)->update([
            'status' => 1,
            'sent_at' => now()
        ]);
    }

    public function markAsFailed($hash, $error) {
        EmailLog::where('hash', $hash)->update([
            'status' => 2,
            'error_message' => $error
        ]);
    }
}
```

---

## 13. Notification Performance & Optimization

### 13.1. Queue Optimization

```php
// High priority notifications
class PaymentNotification extends Notification implements ShouldQueue {
    public $queue = 'high';
    public $delay = 0;
    public $tries = 3;
    public $timeout = 60;
}

// Normal priority notifications
class StatusChangeNotification extends Notification implements ShouldQueue {
    public $queue = 'default';
    public $delay = 30; // 30 seconds delay
    public $tries = 2;
}

// Low priority notifications
class SystemReportNotification extends Notification implements ShouldQueue {
    public $queue = 'low';
    public $delay = 300; // 5 minutes delay
    public $tries = 1;
}
```

### 13.2. Batch Notifications

```php
class BatchNotificationService {
    public function sendBulkNotifications($users, $notificationClass, $data) {
        $chunks = $users->chunk(100);

        foreach ($chunks as $chunk) {
            dispatch(new SendBulkNotificationJob($chunk, $notificationClass, $data))
                ->onQueue('bulk-notifications');
        }
    }
}
```

### 13.3. Notification Throttling

```php
class NotificationThrottleService {
    public function canSendNotification($userId, $type, $timeWindow = 3600) {
        $key = "notification_throttle:{$userId}:{$type}";
        $count = Cache::get($key, 0);

        if ($count >= $this->getThrottleLimit($type)) {
            return false;
        }

        Cache::put($key, $count + 1, $timeWindow);
        return true;
    }

    private function getThrottleLimit($type) {
        return [
            'payment' => 10,
            'status_change' => 20,
            'reminder' => 5,
            'system' => 3
        ][$type] ?? 5;
    }
}
```

---

_Tài liệu hệ thống Notification/Email hoàn tất với đầy đủ chi tiết về tất cả notifications, email templates, configuration và optimization._
