<?php

namespace App\Http\Controllers\Frontend;

use App\Helpers\Common;
use App\Http\Controllers\Controller;
use App\Services\Frontend\BannerService;
use App\Services\Frontend\JobService;
use App\Services\Frontend\SeoService;
use App\Services\Frontend\SettingService;
use App\Services\Frontend\WareHouseSubmitCvService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;

class JobController extends Controller
{

    protected $jobService;
    protected $settingService;
    protected $bannerService;
    protected $wareHouseSubmitCvService;
    protected $seoService;

    protected $routeName;

    public function __construct(
        JobService $jobService,
        SettingService $settingService,
        BannerService $bannerService,
        WareHouseSubmitCvService $wareHouseSubmitCvService,
        SeoService $seoService,
    ) {
        $this->jobService = $jobService;
        $this->settingService = $settingService;
        $this->bannerService = $bannerService;
        $this->wareHouseSubmitCvService = $wareHouseSubmitCvService;
        $this->seoService = $seoService;
        $this->routeName =  Route::currentRouteName();
    }

    public function index()
    {
        if (Auth::guard('client')->user() && Auth::guard('client')->user()->type == config('constant.role.employer')) {
            return back();
        }
        $lang = app()->getLocale();
        //get config seo
        $this->seoService->getConfig($this->routeName, $lang);

        $this->generateParams();

        return view('frontend.pages.job.list');
    }

    public function searchJob(Request $request)
    {
        $arrJob = $this->jobService->getJobSearch($request->all());
        $str = 'home_job';
        $cities = Common::getCities();
        $lang = app()->getLocale();
        $arrLang = $this->settingService->getAllByKey($str, $lang);
        $view = view('frontend.pages.job.list-mobile', compact('arrJob', 'arrLang', 'cities'));
        $view = $view->render();
        return $view;
    }

    public function countSearchJob(Request $request)
    {
        return $this->jobService->countJobSearch($request->toArray());
    }



    public function show($slug)
    {

        $job = $this->jobService->findBySlug($slug);
        $is_employer = false;
        if (\Auth::guard('client')->user() && \Auth::guard('client')->user()->type == config('constant.role.employer')) {
            if ($job->company->id != \Auth::guard('client')->user()->company_id) {
                abort(403, 'Bạn không có quyền xem công việc này');
            }
            $is_employer = true;
        }
        if (intval($job->level) > 0) {
            $level = \Auth::guard('client')->user() ? \Auth::guard('client')->user()->level : 0;
            if ($job->level > $level) {
                abort(403, 'Bạn không có quyền xem công việc này');
            }
        }
        $job->url_detail = route('job-detail', $job->slug);;
        $company = $job->company;
        $jobMeta = $job->jobMeta;
        $jobSeo = $job->jobSeo;
        $cities = Common::getCities();
        $relateJobs = $this->jobService->getRelateBySkill($job->skills, 4);

        $lang = app()->getLocale();
        $currency = config('constant.currency');

        //get config seo
        if ($jobSeo) {
            $this->seoService->getConfig($jobSeo, $lang, true);
        }
        if ($company->path_logo) {
            view()->share([
                'page_image_url' => $company->path_logo
            ]);
        }

        $this->generateParams();

        return view('frontend.pages.job.show', compact('job', 'company', 'cities', 'relateJobs', 'jobMeta', 'currency', 'is_employer'));
    }

    private function generateParams()
    {
        $lang = app()->getLocale();
        $cities = Common::getCities();
        $arrSettingGlobal = $this->settingService->getListSettingGlobal();
        $career = config('job.career.' . $lang);
        $rank = config('job.rank.' . $lang);
        $type = config('job.type.' . $lang);
        $salary = config('job.salary');
        $bonus = config('job.bonus');
        $currency = config('constant.currency');
        $yearExperience = config('constant.sonamkinhnghiem');

        //lấy slide banner: detail-job-search-top
        $bannerSearchJobTop = $this->bannerService->getListByType(
            '',
            strtolower(config('constant.position_banner.detail-job-search-top')),
            10
        );

        $arrBannerSearchJobTop = [];
        foreach ($bannerSearchJobTop as $item) {
            if ($lang == config('constant.language.vi')) {
                array_push($arrBannerSearchJobTop, $item->image_url_vn);
            } else {
                array_push($arrBannerSearchJobTop, $item->image_url_en);
            }
        }

        view()->share([
            'yearExperience'        => $yearExperience,
            'cities'                => $cities,
            'arrLangGlobal'         => $arrSettingGlobal,
            'career'                => $career,
            'rank'                  => $rank,
            'type'                  => $type,
            'salary'                => $salary,
            'bonus'                 => $bonus,
            'arrBannerSearchJobTop' => $arrBannerSearchJobTop,
            'currency'              => $currency,
        ]);
    }

    //click vào job dùng ajax load ra detail job
    public function ajaxDetailJob($slug)
    {
        $job = $this->jobService->findBySlug($slug);
        $totalJobInCompany = $this->jobService->getTotalJob($job);

        $this->generateParams();
        $jobMeta = $job->jobMeta;
        $view = view('frontend.pages.job.detail', compact('job', 'totalJobInCompany', 'jobMeta'));

        return $view->render();
    }

    public function ajaxListJob(Request $request)
    {
        $data = $this->jobService->getJobAjax($request->all());

        $response = [];
        if (count($data)) {
            foreach ($data as $key => $value) {
                $response[$key]['id'] = $value->id;
                $response[$key]['text'] = $value->name . ' - ' . $value->company->name;
            }
        }

        return json_encode($response);
    }
}
