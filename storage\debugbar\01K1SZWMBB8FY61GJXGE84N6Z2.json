{"__meta": {"id": "01K1SZWMBB8FY61GJXGE84N6Z2", "datetime": "2025-08-04 14:31:43", "utime": **********.596579, "method": "POST", "uri": "/rec/update-profile", "ip": "127.0.0.1"}, "php": {"version": "8.1.6", "interface": "apache2handler"}, "messages": {"count": 24, "messages": [{"message": "[14:31:42] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `seos` where `key` = 'rec-update-profile' limit 1\\n-- \",\n    \"Time:\": 2.92\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.023847, "xdebug_link": null, "collector": "log"}, {"message": "[14:31:42] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\\\"_token\\\":\\\"67FrIiAKRzqdDDYE9lpf1v63S50ahQba2svcMgmp\\\",\\\"avatar\\\":null,\\\"cccd_front_image\\\":null,\\\"cccd_back_image\\\":null,\\\"name\\\":\\\"HAU Nguyen Thi\\\",\\\"birthday\\\":\\\"04\\\\\\/02\\\\\\/2000\\\",\\\"mobile\\\":\\\"097845634555\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"address\\\":\\\"60 D\\\\u01b0\\\\u01a1ng Khu\\\\u00ea M\\\\u1ef9 \\\\u0110\\\\u00ecnh2\\\",\\\"cccd_number\\\":\\\"1111111111\\\"}', 'http:\\/\\/recland.local\\/rec\\/update-profile', 'http:\\/\\/recland.local\\/rec\\/profile', '[\\\"en-us\\\",\\\"en\\\",\\\"vi\\\",\\\"nl\\\"]', 'Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********', '{\\\"host\\\":[\\\"recland.local\\\"],\\\"connection\\\":[\\\"keep-alive\\\"],\\\"content-length\\\":[\\\"1137\\\"],\\\"cache-control\\\":[\\\"max-age=0\\\"],\\\"origin\\\":[\\\"http:\\\\\\/\\\\\\/recland.local\\\"],\\\"content-type\\\":[\\\"multipart\\\\\\/form-data; boundary=----WebKitFormBoundary4z7lNZmnzSSuVka7\\\"],\\\"upgrade-insecure-requests\\\":[\\\"1\\\"],\\\"user-agent\\\":[\\\"Mozilla\\\\\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\\\\\/537.36 (KHTML, like Gecko) Chrome\\\\\\/********* Safari\\\\\\/537.36 Edg\\\\\\/*********\\\"],\\\"accept\\\":[\\\"text\\\\\\/html,application\\\\\\/xhtml+xml,application\\\\\\/xml;q=0.9,image\\\\\\/avif,image\\\\\\/webp,image\\\\\\/apng,*\\\\\\/*;q=0.8,application\\\\\\/signed-exchange;v=b3;q=0.7\\\"],\\\"referer\\\":[\\\"http:\\\\\\/\\\\\\/recland.local\\\\\\/rec\\\\\\/profile\\\"],\\\"accept-encoding\\\":[\\\"gzip, deflate\\\"],\\\"accept-language\\\":[\\\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\\\"],\\\"cookie\\\":[\\\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImwwQXVqYlB5Y2M2YjZoR1Jja2UyZEE9PSIsInZhbHVlIjoiV2RDaEFzN3o1Q2FBK1U0dnZ0U0FaSFphcys2d1FNb2gvMnB0OVFoNWp4UDFxK1JMdmpNS05WTmhCVjFicURnRzMybUg1dXNOQm5NT0l6dmJTeklSNVdVOHFmODVXdzBnNTNzUkFZNnJHNE8xajlyeFdhUVozdW4xUGtqL3k3ODMiLCJtYWMiOiIwMWI2MWIzYWM1MWY2YmM2ZDM4YTczOTNhNTRjMjg5NWM5OWM3OWMxM2Y5MGVjMjEwODljMDUzMzlmMDcxN2MxIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6Ik1MUkVFSzMvS1dJQ3R5bnlYUTZQNWc9PSIsInZhbHVlIjoibWZ3b1VTTUVWclZ1anRUSDFGbWxFUkYvd01jaFEyWUZySjkwSzVCMGFVMzZib3RiUXJobWpmb0FZVlloa1RyRm9EajRQa09DWk1xTmJGVTZXMFBIVnZMMUp2djhzeGZwMCtqbkFsYTJjaGs2VEJCTVFIU0RieDk0QW9rejZaVi8iLCJtYWMiOiI4NTFiMmVlMDk1NGIwNzJjNmM0NTk4ZjcwYmNlMDA0MTkyOTViYjVkNjUyOWMyNjJlMWNiZGRmYWEwMmQwY2Q3IiwidGFnIjoiIn0%3D\\\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-04 14:31:42', '2025-08-04 14:31:42')\\n-- \",\n    \"Time:\": 0.54\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.040882, "xdebug_link": null, "collector": "log"}, {"message": "[14:31:42] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `id` = '512' limit 1\\n-- \",\n    \"Time:\": 0.57\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.047307, "xdebug_link": null, "collector": "log"}, {"message": "[14:31:42] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `id` != '512' and `email` = '<EMAIL>' and `type` = 'rec' limit 1\\n-- \",\n    \"Time:\": 0.44\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.063723, "xdebug_link": null, "collector": "log"}, {"message": "[14:31:42] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `users`.`id` = '512' limit 1\\n-- \",\n    \"Time:\": 0.4\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.069134, "xdebug_link": null, "collector": "log"}, {"message": "[14:31:42] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nupdate `users` set `birthday` = '2000-02-04 14:31:42', `users`.`updated_at` = '2025-08-04 14:31:42' where `id` = '512'\\n-- \",\n    \"Time:\": 0.78\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.095205, "xdebug_link": null, "collector": "log"}, {"message": "[14:31:42] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `id` = '1' limit 1\\n-- \",\n    \"Time:\": 0.44\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.098385, "xdebug_link": null, "collector": "log"}, {"message": "[14:31:42] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `audits` (`old_values`, `new_values`, `event`, `auditable_id`, `auditable_type`, `user_id`, `user_type`, `tags`, `ip_address`, `user_agent`, `url`, `updated_at`, `created_at`) values ('{\\\"birthday\\\":\\\"2000-02-04\\\"}', '{\\\"birthday\\\":\\\"2000-02-04T07:31:42.000000Z\\\"}', 'updated', '512', 'App\\\\Models\\\\User', '1', 'App\\\\Models\\\\User', '', '127.0.0.1', 'Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********', 'http:\\/\\/recland.local\\/rec\\/update-profile', '2025-08-04 14:31:42', '2025-08-04 14:31:42')\\n-- \",\n    \"Time:\": 0.71\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.104366, "xdebug_link": null, "collector": "log"}, {"message": "[14:31:42] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `user_infos` where `user_id` = '512' limit 1\\n-- \",\n    \"Time:\": 0.52\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.106415, "xdebug_link": null, "collector": "log"}, {"message": "[14:31:42] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `user_infos` where `user_infos`.`id` = '139' limit 1\\n-- \",\n    \"Time:\": 0.36\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.108121, "xdebug_link": null, "collector": "log"}, {"message": "[14:31:42] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nupdate `user_infos` set `cccd_number` = '1111111111', `user_infos`.`updated_at` = '2025-08-04 14:31:42' where `id` = '139'\\n-- \",\n    \"Time:\": 0.72\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.114736, "xdebug_link": null, "collector": "log"}, {"message": "[14:31:42] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `users`.`id` = '512' limit 1\\n-- \",\n    \"Time:\": 0.44\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.116541, "xdebug_link": null, "collector": "log"}, {"message": "[14:31:42] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `job_logs` (`job_id`, `name`, `queue`, `payload`, `updated_at`, `created_at`) values ('', 'App\\\\Notifications\\\\UpdateProfileEmployer', 'sync', '{\\\"uuid\\\":\\\"417ee99a-d0d7-4962-b6ae-6be82bcfd957\\\",\\\"displayName\\\":\\\"App\\\\\\\\Notifications\\\\\\\\UpdateProfileEmployer\\\",\\\"job\\\":\\\"Illuminate\\\\\\\\Queue\\\\\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"failOnTimeout\\\":false,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"Illuminate\\\\\\\\Notifications\\\\\\\\SendQueuedNotifications\\\",\\\"command\\\":\\\"O:48:\\\\\\\"Illuminate\\\\\\\\Notifications\\\\\\\\SendQueuedNotifications\\\\\\\":3:{s:11:\\\\\\\"notifiables\\\\\\\";O:45:\\\\\\\"Illuminate\\\\\\\\Contracts\\\\\\\\Database\\\\\\\\ModelIdentifier\\\\\\\":5:{s:5:\\\\\\\"class\\\\\\\";s:15:\\\\\\\"App\\\\\\\\Models\\\\\\\\User\\\\\\\";s:2:\\\\\\\"id\\\\\\\";a:1:{i:0;i:512;}s:9:\\\\\\\"relations\\\\\\\";a:0:{}s:10:\\\\\\\"connection\\\\\\\";s:5:\\\\\\\"mysql\\\\\\\";s:15:\\\\\\\"collectionClass\\\\\\\";N;}s:12:\\\\\\\"notification\\\\\\\";O:39:\\\\\\\"App\\\\\\\\Notifications\\\\\\\\UpdateProfileEmployer\\\\\\\":2:{s:7:\\\\\\\"\\\\u0000*\\\\u0000user\\\\\\\";O:45:\\\\\\\"Illuminate\\\\\\\\Contracts\\\\\\\\Database\\\\\\\\ModelIdentifier\\\\\\\":5:{s:5:\\\\\\\"class\\\\\\\";s:15:\\\\\\\"App\\\\\\\\Models\\\\\\\\User\\\\\\\";s:2:\\\\\\\"id\\\\\\\";i:512;s:9:\\\\\\\"relations\\\\\\\";a:0:{}s:10:\\\\\\\"connection\\\\\\\";s:5:\\\\\\\"mysql\\\\\\\";s:15:\\\\\\\"collectionClass\\\\\\\";N;}s:2:\\\\\\\"id\\\\\\\";s:36:\\\\\\\"9eae7f68-80c6-48e5-8519-3fe2e14dd2c2\\\\\\\";}s:8:\\\\\\\"channels\\\\\\\";a:1:{i:0;s:4:\\\\\\\"mail\\\\\\\";}}\\\"}}', '2025-08-04 14:31:42', '2025-08-04 14:31:42')\\n-- \",\n    \"Time:\": 0.69\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.145112, "xdebug_link": null, "collector": "log"}, {"message": "[14:31:42] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `users`.`id` = '512' limit 1\\n-- \",\n    \"Time:\": 0.53\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.147769, "xdebug_link": null, "collector": "log"}, {"message": "[14:31:42] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `users`.`id` in (512)\\n-- \",\n    \"Time:\": 0.42\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.149681, "xdebug_link": null, "collector": "log"}, {"message": "[14:31:42] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `email_logs` (`from`, `to`, `cc`, `subject`, `html_content`, `hash`, `status`, `updated_at`, `created_at`) values ('[\\\"<EMAIL>\\\"]', '[\\\"<EMAIL>\\\"]', '[]', '[Recland] [C\\u1eacP NH\\u1eacT H\\u1ed2 S\\u01a0 C\\u00c1 NH\\u00c2N TH\\u00c0NH C\\u00d4NG]', '<!DOCTYPE html>\\r\\n<html>\\r\\n\\r\\n<body width=\\\"100%\\\"\\r\\n    style=\\\"margin: 0; padding: 0 !important; mso-line-height-rule: exactly; background-color: #f1f1f1;font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif\\\">\\r\\n    <center style=\\\"width: 100%; background-color: #f1f1f1;\\\">\\r\\n        <div style=\\\"max-width: 600px; margin: 0 auto;\\\">\\r\\n            <table align=\\\"center\\\" role=\\\"presentation\\\" cellspacing=\\\"0\\\" cellpadding=\\\"0\\\" border=\\\"0\\\" width=\\\"100%\\\"\\r\\n                style=\\\"margin: auto;\\\">\\r\\n                <tr>\\r\\n                    <td>\\r\\n                        <div style=\\\"text-align: center;padding: 25px 0;background: #FCFCFE;\\\">\\r\\n                            <img style=\\\"max-width: 100%\\\"\\r\\n                                src=\\\"http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/logo.png?v=689061de29bb4\\\">\\r\\n                        <\\/div>\\r\\n                    <\\/td>\\r\\n                <\\/tr>\\r\\n            <\\/table>\\r\\n            <div style=\\\"background-image: linear-gradient(192.45deg, #155969 18.03%, #DAE6E9 93.73%);\\\">\\r\\n                <div\\r\\n                    style=\\\"padding-top: 0;padding-left: 40px;padding-right: 40px;padding-bottom: 32px;\\r\\n                background-image: url(http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/background.png?v=689061de29bc5);\\r\\n                background-repeat: no-repeat;background-size: 100%;\\\">\\r\\n                    <table>\\r\\n                        <tr>\\r\\n                            <td>\\r\\n                                <div\\r\\n                                    style=\\\"padding-top: 80px;height: 194px;text-align: center;font-style: normal;font-weight: 600;font-size: 26px;line-height: 31px;color: #FCFCFE;\\\">\\r\\n                                    <div style=\\\"margin-bottom: 14px\\\">\\r\\n                                                                            <\\/div>\\r\\n                                    <div>\\r\\n                                                                            <\\/div>\\r\\n\\r\\n                                <\\/div>\\r\\n                            <\\/td>\\r\\n                        <\\/tr>\\r\\n                    <\\/table>\\r\\n                    <table style=\\\"width: 100%\\\" role=\\\"presentation\\\" cellspacing=\\\"0\\\" cellpadding=\\\"0\\\" border=\\\"0\\\"\\r\\n                        width=\\\"100%\\\">\\r\\n                        <tr>\\r\\n                            <td>\\r\\n                                    <div\\r\\n        style=\\\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\\\">\\r\\n        <p>Xin ch\\u00e0o, HAU Nguyen Thi<\\/p>\\r\\n        Recland th\\u00f4ng b\\u00e1o h\\u1ed3 s\\u01a1 c\\u1ee7a b\\u1ea1n \\u0111\\u00e3 \\u0111\\u01b0\\u1ee3c c\\u1eadp nh\\u1eadt th\\u00e0nh c\\u00f4ng.\\r\\n        <p><b>Tr\\u00e2n tr\\u1ecdng,<\\/b><\\/p>\\r\\n        <p><i>\\u0110\\u1ed9i ng\\u0169 Recland.<\\/i><\\/p>\\r\\n    <\\/div>\\r\\n    <div style=\\\"border: 5px solid #F7F7F7;\\\"><\\/div>\\r\\n    <div\\r\\n        style=\\\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\\\">\\r\\n        <p>Hello, HAU Nguyen Thi<\\/p>\\r\\n        Recland informs you that your profile has been successfully updated.\\r\\n        <p><b>Best regards,<\\/b><\\/p>\\r\\n        <p><i>Recland team.<\\/i><\\/p>\\r\\n    <\\/div>\\r\\n                            <\\/td>\\r\\n                        <\\/tr>\\r\\n                        <tr>\\r\\n                            <td>\\r\\n                                <div style=\\\"padding:12px 0\\\">\\r\\n                                    <div\\r\\n                                        style=\\\"background-image: url(http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/background-footer.png?v=689061de29be4);background-position: bottom left;background-repeat: no-repeat;text-align: center;background-color: #FCFCFE;padding-top: 22px;padding-bottom: 12px\\\">\\r\\n                                        <div style=\\\"margin-bottom: 12px;text-align: center\\\">\\r\\n                                            <a style=\\\"padding-left: 16px;padding-right: 16px\\\" href=\\\"#\\\"><img\\r\\n                                                    src=\\\"http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/icon-twitter.png?v=689061de29bef\\\"><\\/a>\\r\\n                                            <a style=\\\"padding-left: 16px;padding-right: 16px\\\" href=\\\"#\\\"><img\\r\\n                                                    src=\\\"http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/icon-facebook.png?v=689061de29bf9\\\"><\\/a>\\r\\n                                            <a style=\\\"padding-left: 16px;padding-right: 16px\\\" href=\\\"#\\\"><img\\r\\n                                                    src=\\\"http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/icon-instagram.png?v=689061de29c04\\\"><\\/a>\\r\\n                                        <\\/div>\\r\\n                                        <p\\r\\n                                            style=\\\"margin-top: 0;margin-bottom: 8px;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #17677B;\\\">\\r\\n                                            N\\u1ec1n t\\u1ea3ng t\\u1ea1o ra c\\u01a1 h\\u1ed9i ki\\u1ebfm ti\\u1ec1n d\\u00e0nh cho HR Freelancer<\\/p>\\r\\n                                        <p\\r\\n                                            style=\\\"margin-top: 0;margin-bottom: 0;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #3C4455;\\\">\\r\\n                                            \\u00a9 2022 Recland.co<\\/p>\\r\\n                                    <\\/div>\\r\\n                                <\\/div>\\r\\n\\r\\n                            <\\/td>\\r\\n                        <\\/tr>\\r\\n                    <\\/table>\\r\\n                <\\/div>\\r\\n            <\\/div>\\r\\n        <\\/div>\\r\\n    <\\/center>\\r\\n<\\/body>\\r\\n\\r\\n<\\/html>\\r\\n', '3390c972591f9d6e2538ff349cb9f628', '0', '2025-08-04 14:31:42', '2025-08-04 14:31:42')\\n-- \",\n    \"Time:\": 1.25\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.174727, "xdebug_link": null, "collector": "log"}, {"message": "[14:31:43] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `job_logs` (`job_id`, `name`, `queue`, `payload`, `updated_at`, `created_at`) values ('', 'App\\\\Jobs\\\\UpdateEmailLogStatus', 'sync', '{\\\"uuid\\\":\\\"8ff1cd8d-e7f8-47c1-b7db-a24d3a89a229\\\",\\\"displayName\\\":\\\"App\\\\\\\\Jobs\\\\\\\\UpdateEmailLogStatus\\\",\\\"job\\\":\\\"Illuminate\\\\\\\\Queue\\\\\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"failOnTimeout\\\":false,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"App\\\\\\\\Jobs\\\\\\\\UpdateEmailLogStatus\\\",\\\"command\\\":\\\"O:29:\\\\\\\"App\\\\\\\\Jobs\\\\\\\\UpdateEmailLogStatus\\\\\\\":1:{s:5:\\\\\\\"email\\\\\\\";O:28:\\\\\\\"Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Email\\\\\\\":6:{i:0;N;i:1;N;i:2;s:5639:\\\\\\\"<!DOCTYPE html>\\\\r\\\\n<html>\\\\r\\\\n\\\\r\\\\n<body width=\\\\\\\"100%\\\\\\\"\\\\r\\\\n    style=\\\\\\\"margin: 0; padding: 0 !important; mso-line-height-rule: exactly; background-color: #f1f1f1;font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif\\\\\\\">\\\\r\\\\n    <center style=\\\\\\\"width: 100%; background-color: #f1f1f1;\\\\\\\">\\\\r\\\\n        <div style=\\\\\\\"max-width: 600px; margin: 0 auto;\\\\\\\">\\\\r\\\\n            <table align=\\\\\\\"center\\\\\\\" role=\\\\\\\"presentation\\\\\\\" cellspacing=\\\\\\\"0\\\\\\\" cellpadding=\\\\\\\"0\\\\\\\" border=\\\\\\\"0\\\\\\\" width=\\\\\\\"100%\\\\\\\"\\\\r\\\\n                style=\\\\\\\"margin: auto;\\\\\\\">\\\\r\\\\n                <tr>\\\\r\\\\n                    <td>\\\\r\\\\n                        <div style=\\\\\\\"text-align: center;padding: 25px 0;background: #FCFCFE;\\\\\\\">\\\\r\\\\n                            <img style=\\\\\\\"max-width: 100%\\\\\\\"\\\\r\\\\n                                src=\\\\\\\"http:\\\\\\/\\\\\\/recland.local\\\\\\/frontend\\\\\\/asset\\\\\\/images\\\\\\/template-email\\\\\\/logo.png?v=689061de29bb4\\\\\\\">\\\\r\\\\n                        <\\\\\\/div>\\\\r\\\\n                    <\\\\\\/td>\\\\r\\\\n                <\\\\\\/tr>\\\\r\\\\n            <\\\\\\/table>\\\\r\\\\n            <div style=\\\\\\\"background-image: linear-gradient(192.45deg, #155969 18.03%, #DAE6E9 93.73%);\\\\\\\">\\\\r\\\\n                <div\\\\r\\\\n                    style=\\\\\\\"padding-top: 0;padding-left: 40px;padding-right: 40px;padding-bottom: 32px;\\\\r\\\\n                background-image: url(http:\\\\\\/\\\\\\/recland.local\\\\\\/frontend\\\\\\/asset\\\\\\/images\\\\\\/template-email\\\\\\/background.png?v=689061de29bc5);\\\\r\\\\n                background-repeat: no-repeat;background-size: 100%;\\\\\\\">\\\\r\\\\n                    <table>\\\\r\\\\n                        <tr>\\\\r\\\\n                            <td>\\\\r\\\\n                                <div\\\\r\\\\n                                    style=\\\\\\\"padding-top: 80px;height: 194px;text-align: center;font-style: normal;font-weight: 600;font-size: 26px;line-height: 31px;color: #FCFCFE;\\\\\\\">\\\\r\\\\n                                    <div style=\\\\\\\"margin-bottom: 14px\\\\\\\">\\\\r\\\\n                                                                            <\\\\\\/div>\\\\r\\\\n                                    <div>\\\\r\\\\n                                                                            <\\\\\\/div>\\\\r\\\\n\\\\r\\\\n                                <\\\\\\/div>\\\\r\\\\n                            <\\\\\\/td>\\\\r\\\\n                        <\\\\\\/tr>\\\\r\\\\n                    <\\\\\\/table>\\\\r\\\\n                    <table style=\\\\\\\"width: 100%\\\\\\\" role=\\\\\\\"presentation\\\\\\\" cellspacing=\\\\\\\"0\\\\\\\" cellpadding=\\\\\\\"0\\\\\\\" border=\\\\\\\"0\\\\\\\"\\\\r\\\\n                        width=\\\\\\\"100%\\\\\\\">\\\\r\\\\n                        <tr>\\\\r\\\\n                            <td>\\\\r\\\\n                                    <div\\\\r\\\\n        style=\\\\\\\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\\\\\\\">\\\\r\\\\n        <p>Xin ch\\\\u00e0o, HAU Nguyen Thi<\\\\\\/p>\\\\r\\\\n        Recland th\\\\u00f4ng b\\\\u00e1o h\\\\u1ed3 s\\\\u01a1 c\\\\u1ee7a b\\\\u1ea1n \\\\u0111\\\\u00e3 \\\\u0111\\\\u01b0\\\\u1ee3c c\\\\u1eadp nh\\\\u1eadt th\\\\u00e0nh c\\\\u00f4ng.\\\\r\\\\n        <p><b>Tr\\\\u00e2n tr\\\\u1ecdng,<\\\\\\/b><\\\\\\/p>\\\\r\\\\n        <p><i>\\\\u0110\\\\u1ed9i ng\\\\u0169 Recland.<\\\\\\/i><\\\\\\/p>\\\\r\\\\n    <\\\\\\/div>\\\\r\\\\n    <div style=\\\\\\\"border: 5px solid #F7F7F7;\\\\\\\"><\\\\\\/div>\\\\r\\\\n    <div\\\\r\\\\n        style=\\\\\\\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\\\\\\\">\\\\r\\\\n        <p>Hello, HAU Nguyen Thi<\\\\\\/p>\\\\r\\\\n        Recland informs you that your profile has been successfully updated.\\\\r\\\\n        <p><b>Best regards,<\\\\\\/b><\\\\\\/p>\\\\r\\\\n        <p><i>Recland team.<\\\\\\/i><\\\\\\/p>\\\\r\\\\n    <\\\\\\/div>\\\\r\\\\n                            <\\\\\\/td>\\\\r\\\\n                        <\\\\\\/tr>\\\\r\\\\n                        <tr>\\\\r\\\\n                            <td>\\\\r\\\\n                                <div style=\\\\\\\"padding:12px 0\\\\\\\">\\\\r\\\\n                                    <div\\\\r\\\\n                                        style=\\\\\\\"background-image: url(http:\\\\\\/\\\\\\/recland.local\\\\\\/frontend\\\\\\/asset\\\\\\/images\\\\\\/template-email\\\\\\/background-footer.png?v=689061de29be4);background-position: bottom left;background-repeat: no-repeat;text-align: center;background-color: #FCFCFE;padding-top: 22px;padding-bottom: 12px\\\\\\\">\\\\r\\\\n                                        <div style=\\\\\\\"margin-bottom: 12px;text-align: center\\\\\\\">\\\\r\\\\n                                            <a style=\\\\\\\"padding-left: 16px;padding-right: 16px\\\\\\\" href=\\\\\\\"#\\\\\\\"><img\\\\r\\\\n                                                    src=\\\\\\\"http:\\\\\\/\\\\\\/recland.local\\\\\\/frontend\\\\\\/asset\\\\\\/images\\\\\\/template-email\\\\\\/icon-twitter.png?v=689061de29bef\\\\\\\"><\\\\\\/a>\\\\r\\\\n                                            <a style=\\\\\\\"padding-left: 16px;padding-right: 16px\\\\\\\" href=\\\\\\\"#\\\\\\\"><img\\\\r\\\\n                                                    src=\\\\\\\"http:\\\\\\/\\\\\\/recland.local\\\\\\/frontend\\\\\\/asset\\\\\\/images\\\\\\/template-email\\\\\\/icon-facebook.png?v=689061de29bf9\\\\\\\"><\\\\\\/a>\\\\r\\\\n                                            <a style=\\\\\\\"padding-left: 16px;padding-right: 16px\\\\\\\" href=\\\\\\\"#\\\\\\\"><img\\\\r\\\\n                                                    src=\\\\\\\"http:\\\\\\/\\\\\\/recland.local\\\\\\/frontend\\\\\\/asset\\\\\\/images\\\\\\/template-email\\\\\\/icon-instagram.png?v=689061de29c04\\\\\\\"><\\\\\\/a>\\\\r\\\\n                                        <\\\\\\/div>\\\\r\\\\n                                        <p\\\\r\\\\n                                            style=\\\\\\\"margin-top: 0;margin-bottom: 8px;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #17677B;\\\\\\\">\\\\r\\\\n                                            N\\\\u1ec1n t\\\\u1ea3ng t\\\\u1ea1o ra c\\\\u01a1 h\\\\u1ed9i ki\\\\u1ebfm ti\\\\u1ec1n d\\\\u00e0nh cho HR Freelancer<\\\\\\/p>\\\\r\\\\n                                        <p\\\\r\\\\n                                            style=\\\\\\\"margin-top: 0;margin-bottom: 0;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #3C4455;\\\\\\\">\\\\r\\\\n                                            \\\\u00a9 2022 Recland.co<\\\\\\/p>\\\\r\\\\n                                    <\\\\\\/div>\\\\r\\\\n                                <\\\\\\/div>\\\\r\\\\n\\\\r\\\\n                            <\\\\\\/td>\\\\r\\\\n                        <\\\\\\/tr>\\\\r\\\\n                    <\\\\\\/table>\\\\r\\\\n                <\\\\\\/div>\\\\r\\\\n            <\\\\\\/div>\\\\r\\\\n        <\\\\\\/div>\\\\r\\\\n    <\\\\\\/center>\\\\r\\\\n<\\\\\\/body>\\\\r\\\\n\\\\r\\\\n<\\\\\\/html>\\\\r\\\\n\\\\\\\";i:3;s:5:\\\\\\\"utf-8\\\\\\\";i:4;a:0:{}i:5;a:2:{i:0;O:37:\\\\\\\"Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\Headers\\\\\\\":2:{s:46:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\Headers\\\\u0000headers\\\\\\\";a:4:{s:4:\\\\\\\"from\\\\\\\";a:1:{i:0;O:47:\\\\\\\"Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\MailboxListHeader\\\\\\\":5:{s:50:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\AbstractHeader\\\\u0000name\\\\\\\";s:4:\\\\\\\"From\\\\\\\";s:56:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\AbstractHeader\\\\u0000lineLength\\\\\\\";i:76;s:50:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\AbstractHeader\\\\u0000lang\\\\\\\";N;s:53:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\AbstractHeader\\\\u0000charset\\\\\\\";s:5:\\\\\\\"utf-8\\\\\\\";s:58:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\MailboxListHeader\\\\u0000addresses\\\\\\\";a:1:{i:0;O:30:\\\\\\\"Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Address\\\\\\\":2:{s:39:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Address\\\\u0000address\\\\\\\";s:18:\\\\\\\"<EMAIL>\\\\\\\";s:36:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Address\\\\u0000name\\\\\\\";s:7:\\\\\\\"RECLAND\\\\\\\";}}}}s:2:\\\\\\\"to\\\\\\\";a:1:{i:0;O:47:\\\\\\\"Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\MailboxListHeader\\\\\\\":5:{s:50:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\AbstractHeader\\\\u0000name\\\\\\\";s:2:\\\\\\\"To\\\\\\\";s:56:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\AbstractHeader\\\\u0000lineLength\\\\\\\";i:76;s:50:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\AbstractHeader\\\\u0000lang\\\\\\\";N;s:53:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\AbstractHeader\\\\u0000charset\\\\\\\";s:5:\\\\\\\"utf-8\\\\\\\";s:58:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\MailboxListHeader\\\\u0000addresses\\\\\\\";a:1:{i:0;O:30:\\\\\\\"Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Address\\\\\\\":2:{s:39:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Address\\\\u0000address\\\\\\\";s:21:\\\\\\\"<EMAIL>\\\\\\\";s:36:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Address\\\\u0000name\\\\\\\";s:0:\\\\\\\"\\\\\\\";}}}}s:7:\\\\\\\"subject\\\\\\\";a:1:{i:0;O:48:\\\\\\\"Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\UnstructuredHeader\\\\\\\":5:{s:50:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\AbstractHeader\\\\u0000name\\\\\\\";s:7:\\\\\\\"Subject\\\\\\\";s:56:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\AbstractHeader\\\\u0000lineLength\\\\\\\";i:76;s:50:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\AbstractHeader\\\\u0000lang\\\\\\\";N;s:53:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\AbstractHeader\\\\u0000charset\\\\\\\";s:5:\\\\\\\"utf-8\\\\\\\";s:55:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\UnstructuredHeader\\\\u0000value\\\\\\\";s:56:\\\\\\\"[Recland] [C\\\\u1eacP NH\\\\u1eacT H\\\\u1ed2 S\\\\u01a0 C\\\\u00c1 NH\\\\u00c2N TH\\\\u00c0NH C\\\\u00d4NG]\\\\\\\";}}s:17:\\\\\\\"x-original-emails\\\\\\\";a:1:{i:0;O:48:\\\\\\\"Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\UnstructuredHeader\\\\\\\":5:{s:50:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\AbstractHeader\\\\u0000name\\\\\\\";s:17:\\\\\\\"X-Original-Emails\\\\\\\";s:56:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\AbstractHeader\\\\u0000lineLength\\\\\\\";i:76;s:50:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\AbstractHeader\\\\u0000lang\\\\\\\";N;s:53:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\AbstractHeader\\\\u0000charset\\\\\\\";s:5:\\\\\\\"utf-8\\\\\\\";s:55:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\UnstructuredHeader\\\\u0000value\\\\\\\";s:2:\\\\\\\"[]\\\\\\\";}}}s:49:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\Headers\\\\u0000lineLength\\\\\\\";i:76;}i:1;N;}}}\\\"}}', '2025-08-04 14:31:43', '2025-08-04 14:31:43')\\n-- \",\n    \"Time:\": 2.17\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.331993, "xdebug_link": null, "collector": "log"}, {"message": "[14:31:43] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `email_logs` where `hash` = '92082d4bc6af578d785908c3aeb3ced6' and `created_at` >= '2025-08-04 14:26:43'\\n-- \",\n    \"Time:\": 228.73\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.563761, "xdebug_link": null, "collector": "log"}, {"message": "[14:31:43] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `job_logs` (`job_id`, `name`, `queue`, `payload`, `updated_at`, `created_at`) values ('', 'App\\\\Notifications\\\\UpdateProfileEmployer', 'sync', '{\\\"uuid\\\":\\\"93cf4ed8-1abd-4f75-b7a6-6e8016126ec3\\\",\\\"displayName\\\":\\\"App\\\\\\\\Notifications\\\\\\\\UpdateProfileEmployer\\\",\\\"job\\\":\\\"Illuminate\\\\\\\\Queue\\\\\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"failOnTimeout\\\":false,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"Illuminate\\\\\\\\Notifications\\\\\\\\SendQueuedNotifications\\\",\\\"command\\\":\\\"O:48:\\\\\\\"Illuminate\\\\\\\\Notifications\\\\\\\\SendQueuedNotifications\\\\\\\":3:{s:11:\\\\\\\"notifiables\\\\\\\";O:45:\\\\\\\"Illuminate\\\\\\\\Contracts\\\\\\\\Database\\\\\\\\ModelIdentifier\\\\\\\":5:{s:5:\\\\\\\"class\\\\\\\";s:15:\\\\\\\"App\\\\\\\\Models\\\\\\\\User\\\\\\\";s:2:\\\\\\\"id\\\\\\\";a:1:{i:0;i:512;}s:9:\\\\\\\"relations\\\\\\\";a:0:{}s:10:\\\\\\\"connection\\\\\\\";s:5:\\\\\\\"mysql\\\\\\\";s:15:\\\\\\\"collectionClass\\\\\\\";N;}s:12:\\\\\\\"notification\\\\\\\";O:39:\\\\\\\"App\\\\\\\\Notifications\\\\\\\\UpdateProfileEmployer\\\\\\\":2:{s:7:\\\\\\\"\\\\u0000*\\\\u0000user\\\\\\\";O:45:\\\\\\\"Illuminate\\\\\\\\Contracts\\\\\\\\Database\\\\\\\\ModelIdentifier\\\\\\\":5:{s:5:\\\\\\\"class\\\\\\\";s:15:\\\\\\\"App\\\\\\\\Models\\\\\\\\User\\\\\\\";s:2:\\\\\\\"id\\\\\\\";i:512;s:9:\\\\\\\"relations\\\\\\\";a:0:{}s:10:\\\\\\\"connection\\\\\\\";s:5:\\\\\\\"mysql\\\\\\\";s:15:\\\\\\\"collectionClass\\\\\\\";N;}s:2:\\\\\\\"id\\\\\\\";s:36:\\\\\\\"9eae7f68-80c6-48e5-8519-3fe2e14dd2c2\\\\\\\";}s:8:\\\\\\\"channels\\\\\\\";a:1:{i:0;s:8:\\\\\\\"database\\\\\\\";}}\\\"}}', '2025-08-04 14:31:43', '2025-08-04 14:31:43')\\n-- \",\n    \"Time:\": 0.69\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.567731, "xdebug_link": null, "collector": "log"}, {"message": "[14:31:43] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `users`.`id` = '512' limit 1\\n-- \",\n    \"Time:\": 0.6\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.570141, "xdebug_link": null, "collector": "log"}, {"message": "[14:31:43] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `users`.`id` in (512)\\n-- \",\n    \"Time:\": 0.43\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.573514, "xdebug_link": null, "collector": "log"}, {"message": "[14:31:43] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect `value`, `key` from `settings` where `key` like '%settings.vi.notification%'\\n-- \",\n    \"Time:\": 5.09\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.581731, "xdebug_link": null, "collector": "log"}, {"message": "[14:31:43] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect `value`, `key` from `settings` where `key` like '%settings.en.notification%'\\n-- \",\n    \"Time:\": 3.47\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.58691, "xdebug_link": null, "collector": "log"}, {"message": "[14:31:43] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `notifications` (`id`, `type`, `data`, `read_at`, `notifiable_id`, `notifiable_type`, `updated_at`, `created_at`) values ('9eae7f68-80c6-48e5-8519-3fe2e14dd2c2', 'App\\\\Notifications\\\\UpdateProfileEmployer', '{\\\"content_vi\\\":\\\"C\\\\u1ed9ng t\\\\u00e1c vi\\\\u00ean \\\\u0111\\\\u00e3 c\\\\u1eadp nh\\\\u1eadt h\\\\u1ed3 s\\\\u01a1 th\\\\u00e0nh c\\\\u00f4ng.\\\",\\\"content_en\\\":\\\"The collaborator has successfully updated the profile.\\\"}', '', '512', 'App\\\\Models\\\\User', '2025-08-04 14:31:43', '2025-08-04 14:31:43')\\n-- \",\n    \"Time:\": 0.57\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.58966, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 3, "start": **********.629055, "end": **********.596624, "duration": 1.9675688743591309, "duration_str": "1.97s", "measures": [{"label": "Booting", "start": **********.629055, "relative_start": 0, "end": **********.974486, "relative_end": **********.974486, "duration": 0.****************, "duration_str": "345ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.974497, "relative_start": 0.****************, "end": **********.596626, "relative_end": 2.1457672119140625e-06, "duration": 1.***************, "duration_str": "1.62s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.985546, "relative_start": 0.****************, "end": **********.988721, "relative_end": **********.988721, "duration": 0.0031747817993164062, "duration_str": "3.17ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 2, "nb_templates": 2, "templates": [{"name": "1x email.updateProfileRec", "param_count": null, "params": [], "start": **********.170046, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/email/updateProfileRec.blade.phpemail.updateProfileRec", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Femail%2FupdateProfileRec.blade.php&line=1", "ajax": false, "filename": "updateProfileRec.blade.php", "line": "?"}, "render_count": 1, "name_original": "email.updateProfileRec"}, {"name": "1x email.master", "param_count": null, "params": [], "start": **********.170566, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/email/master.blade.phpemail.master", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Femail%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}, "render_count": 1, "name_original": "email.master"}]}, "route": {"uri": "POST rec/update-profile", "middleware": "web, localization, visit-website, check-rec", "controller": "App\\Http\\Controllers\\Frontend\\RecController@profilePost<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FRecController.php&line=169\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Frontend", "prefix": "/rec", "as": "rec-update-profile", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FRecController.php&line=169\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/RecController.php:169-182</a>"}, "queries": {"count": 26, "nb_statements": 26, "nb_visible_statements": 26, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.27725999999999995, "accumulated_duration_str": "277ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `seos` where `key` = 'rec-update-profile' limit 1", "type": "query", "params": [], "bindings": ["rec-update-profile"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/SeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SeoRepository.php", "line": 14}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/SeoService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\SeoService.php", "line": 22}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/RecController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\RecController.php", "line": 104}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 929}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 770}], "start": **********.0211742, "duration": 0.00292, "duration_str": "2.92ms", "memory": 0, "memory_str": null, "filename": "SeoRepository.php:14", "source": {"index": 15, "namespace": null, "name": "app/Repositories/SeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SeoRepository.php", "line": 14}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSeoRepository.php&line=14", "ajax": false, "filename": "SeoRepository.php", "line": "14"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `seos` where `key` = ? limit 1", "hash": "68402713c9a7f9ba4e150acd101ff175ff9c302f4b906d356fb660b7bf2712cd"}, "start_percent": 0, "width_percent": 1.053}, {"sql": "insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\\\"_token\\\":\\\"67FrIiAKRzqdDDYE9lpf1v63S50ahQba2svcMgmp\\\",\\\"avatar\\\":null,\\\"cccd_front_image\\\":null,\\\"cccd_back_image\\\":null,\\\"name\\\":\\\"HAU Nguyen <PERSON>hi\\\",\\\"birthday\\\":\\\"04\\/02\\/2000\\\",\\\"mobile\\\":\\\"097845634555\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"address\\\":\\\"60 D\\u01b0\\u01a1ng Khu\\u00ea M\\u1ef9 \\u0110\\u00ecnh2\\\",\\\"cccd_number\\\":\\\"1111111111\\\"}', 'http://recland.local/rec/update-profile', 'http://recland.local/rec/profile', '[\\\"en-us\\\",\\\"en\\\",\\\"vi\\\",\\\"nl\\\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\\\"host\\\":[\\\"recland.local\\\"],\\\"connection\\\":[\\\"keep-alive\\\"],\\\"content-length\\\":[\\\"1137\\\"],\\\"cache-control\\\":[\\\"max-age=0\\\"],\\\"origin\\\":[\\\"http:\\/\\/recland.local\\\"],\\\"content-type\\\":[\\\"multipart\\/form-data; boundary=----WebKitFormBoundary4z7lNZmnzSSuVka7\\\"],\\\"upgrade-insecure-requests\\\":[\\\"1\\\"],\\\"user-agent\\\":[\\\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\\\"],\\\"accept\\\":[\\\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\\\"],\\\"referer\\\":[\\\"http:\\/\\/recland.local\\/rec\\/profile\\\"],\\\"accept-encoding\\\":[\\\"gzip, deflate\\\"],\\\"accept-language\\\":[\\\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\\\"],\\\"cookie\\\":[\\\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImwwQXVqYlB5Y2M2YjZoR1Jja2UyZEE9PSIsInZhbHVlIjoiV2RDaEFzN3o1Q2FBK1U0dnZ0U0FaSFphcys2d1FNb2gvMnB0OVFoNWp4UDFxK1JMdmpNS05WTmhCVjFicURnRzMybUg1dXNOQm5NT0l6dmJTeklSNVdVOHFmODVXdzBnNTNzUkFZNnJHNE8xajlyeFdhUVozdW4xUGtqL3k3ODMiLCJtYWMiOiIwMWI2MWIzYWM1MWY2YmM2ZDM4YTczOTNhNTRjMjg5NWM5OWM3OWMxM2Y5MGVjMjEwODljMDUzMzlmMDcxN2MxIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6Ik1MUkVFSzMvS1dJQ3R5bnlYUTZQNWc9PSIsInZhbHVlIjoibWZ3b1VTTUVWclZ1anRUSDFGbWxFUkYvd01jaFEyWUZySjkwSzVCMGFVMzZib3RiUXJobWpmb0FZVlloa1RyRm9EajRQa09DWk1xTmJGVTZXMFBIVnZMMUp2djhzeGZwMCtqbkFsYTJjaGs2VEJCTVFIU0RieDk0QW9rejZaVi8iLCJtYWMiOiI4NTFiMmVlMDk1NGIwNzJjNmM0NTk4ZjcwYmNlMDA0MTkyOTViYjVkNjUyOWMyNjJlMWNiZGRmYWEwMmQwY2Q3IiwidGFnIjoiIn0%3D\\\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-04 14:31:42', '2025-08-04 14:31:42')", "type": "query", "params": [], "bindings": ["POST", "{\"_token\":\"67FrIiAKRzqdDDYE9lpf1v63S50ahQba2svcMgmp\",\"avatar\":null,\"cccd_front_image\":null,\"cccd_back_image\":null,\"name\":\"<PERSON><PERSON><PERSON>\",\"birthday\":\"04\\/02\\/2000\",\"mobile\":\"097845634555\",\"email\":\"<EMAIL>\",\"address\":\"60 D\\u01b0\\u01a1ng Khu\\u00ea M\\u1ef9 \\u0110\\u00ecnh2\",\"cccd_number\":\"1111111111\"}", "http://recland.local/rec/update-profile", "http://recland.local/rec/profile", "[\"en-us\",\"en\",\"vi\",\"nl\"]", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"1137\"],\"cache-control\":[\"max-age=0\"],\"origin\":[\"http:\\/\\/recland.local\"],\"content-type\":[\"multipart\\/form-data; boundary=----WebKitFormBoundary4z7lNZmnzSSuVka7\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/rec\\/profile\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImwwQXVqYlB5Y2M2YjZoR1Jja2UyZEE9PSIsInZhbHVlIjoiV2RDaEFzN3o1Q2FBK1U0dnZ0U0FaSFphcys2d1FNb2gvMnB0OVFoNWp4UDFxK1JMdmpNS05WTmhCVjFicURnRzMybUg1dXNOQm5NT0l6dmJTeklSNVdVOHFmODVXdzBnNTNzUkFZNnJHNE8xajlyeFdhUVozdW4xUGtqL3k3ODMiLCJtYWMiOiIwMWI2MWIzYWM1MWY2YmM2ZDM4YTczOTNhNTRjMjg5NWM5OWM3OWMxM2Y5MGVjMjEwODljMDUzMzlmMDcxN2MxIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6Ik1MUkVFSzMvS1dJQ3R5bnlYUTZQNWc9PSIsInZhbHVlIjoibWZ3b1VTTUVWclZ1anRUSDFGbWxFUkYvd01jaFEyWUZySjkwSzVCMGFVMzZib3RiUXJobWpmb0FZVlloa1RyRm9EajRQa09DWk1xTmJGVTZXMFBIVnZMMUp2djhzeGZwMCtqbkFsYTJjaGs2VEJCTVFIU0RieDk0QW9rejZaVi8iLCJtYWMiOiI4NTFiMmVlMDk1NGIwNzJjNmM0NTk4ZjcwYmNlMDA0MTkyOTViYjVkNjUyOWMyNjJlMWNiZGRmYWEwMmQwY2Q3IiwidGFnIjoiIn0%3D\"]}", "WebKit", "Windows", "Edge", "127.0.0.1", null, null, "2025-08-04 14:31:42", "2025-08-04 14:31:42"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/shetabit/visitor/src/Visitor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\shetabit\\visitor\\src\\Visitor.php", "line": 245}, {"index": 22, "namespace": "middleware", "name": "visit-website", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\VisitWebsiteMiddleware.php", "line": 20}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 24, "namespace": "middleware", "name": "localization", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\Localization.php", "line": 24}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.040414, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Visitor.php:245", "source": {"index": 21, "namespace": null, "name": "vendor/shetabit/visitor/src/Visitor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\shetabit\\visitor\\src\\Visitor.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fshetabit%2Fvisitor%2Fsrc%2FVisitor.php&line=245", "ajax": false, "filename": "Visitor.php", "line": "245"}, "connection": "hri_recland_product", "explain": null, "start_percent": 1.053, "width_percent": 0.195}, {"sql": "select * from `users` where `id` = 512 limit 1", "type": "query", "params": [], "bindings": [512], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "check-rec", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckRec.php", "line": 21}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.046808, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `id` = ? limit 1", "hash": "f57aeccc1e0beb29028e5e2a4241e4eed6d22790637d6f4352f9bde1e64d2f30"}, "start_percent": 1.248, "width_percent": 0.206}, {"sql": "select * from `users` where `id` != 512 and `email` = '<EMAIL>' and `type` = 'rec' limit 1", "type": "query", "params": [], "bindings": [512, "<EMAIL>", "rec"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserRepository.php", "line": 81}, {"index": 16, "namespace": null, "name": "app/Rules/Admin/CheckEmailRule.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Rules\\Admin\\CheckEmailRule.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 819}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 610}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 422}], "start": **********.0633552, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "UserRepository.php:81", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserRepository.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserRepository.php&line=81", "ajax": false, "filename": "UserRepository.php", "line": "81"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `id` != ? and `email` = ? and `type` = ? limit 1", "hash": "91fceb3dbd981d8bcb47e0b21a06b13d665823cd8e02c8a075ba990e0620911a"}, "start_percent": 1.454, "width_percent": 0.159}, {"sql": "select * from `users` where `users`.`id` = 512 limit 1", "type": "query", "params": [], "bindings": [512], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 118}, {"index": 17, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 139}, {"index": 18, "namespace": null, "name": "app/Services/Frontend/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserService.php", "line": 358}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Frontend/RecController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\RecController.php", "line": 176}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.0688028, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:118", "source": {"index": 16, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 118}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FBaseRepository.php&line=118", "ajax": false, "filename": "BaseRepository.php", "line": "118"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `users`.`id` = ? limit 1", "hash": "62b695c7a5937b15a7884c504ebc83827f2a9393d8daceb5a22dceb704d4dceb"}, "start_percent": 1.612, "width_percent": 0.144}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'hri_recland_product' and table_name = 'users'", "type": "query", "params": [], "bindings": ["hri_recland_product", "users"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 143}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserService.php", "line": 358}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/RecController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\RecController.php", "line": 176}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.0725791, "duration": 0.02028, "duration_str": "20.28ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:143", "source": {"index": 15, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 143}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FBaseRepository.php&line=143", "ajax": false, "filename": "BaseRepository.php", "line": "143"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select column_name as `column_name` from information_schema.columns where table_schema = ? and table_name = ?", "hash": "2f932a8ab5559dc2e718e2169d292b7c251235f387a3834f8b6fb4b996f87912"}, "start_percent": 1.756, "width_percent": 7.314}, {"sql": "update `users` set `birthday` = '2000-02-04 14:31:42', `users`.`updated_at` = '2025-08-04 14:31:42' where `id` = 512", "type": "query", "params": [], "bindings": ["2000-02-04 14:31:42", "2025-08-04 14:31:42", 512], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 143}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserService.php", "line": 358}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/RecController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\RecController.php", "line": 176}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.0944948, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:143", "source": {"index": 14, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 143}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FBaseRepository.php&line=143", "ajax": false, "filename": "BaseRepository.php", "line": "143"}, "connection": "hri_recland_product", "explain": null, "start_percent": 9.071, "width_percent": 0.281}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": null, "name": "vendor/owen-it/laravel-auditing/src/Resolvers/UserResolver.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\owen-it\\laravel-auditing\\src\\Resolvers\\UserResolver.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/owen-it/laravel-auditing/src/Auditable.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\owen-it\\laravel-auditing\\src\\Auditable.php", "line": 409}], "start": **********.0980132, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `id` = ? limit 1", "hash": "ac0ef66d2bf40f3993c8e40de021b0d99da887a13adec01b9fbb0601a2bf69c5"}, "start_percent": 9.352, "width_percent": 0.159}, {"sql": "insert into `audits` (`old_values`, `new_values`, `event`, `auditable_id`, `auditable_type`, `user_id`, `user_type`, `tags`, `ip_address`, `user_agent`, `url`, `updated_at`, `created_at`) values ('{\\\"birthday\\\":\\\"2000-02-04\\\"}', '{\\\"birthday\\\":\\\"2000-02-04T07:31:42.000000Z\\\"}', 'updated', 512, 'App\\Models\\User', 1, 'App\\Models\\User', '', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'http://recland.local/rec/update-profile', '2025-08-04 14:31:42', '2025-08-04 14:31:42')", "type": "query", "params": [], "bindings": ["{\"birthday\":\"2000-02-04\"}", "{\"birthday\":\"2000-02-04T07:31:42.000000Z\"}", "updated", 512, "App\\Models\\User", 1, "App\\Models\\User", null, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "http://recland.local/rec/update-profile", "2025-08-04 14:31:42", "2025-08-04 14:31:42"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/owen-it/laravel-auditing/src/Auditor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\owen-it\\laravel-auditing\\src\\Auditor.php", "line": 83}, {"index": 25, "namespace": null, "name": "vendor/owen-it/laravel-auditing/src/AuditableObserver.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\owen-it\\laravel-auditing\\src\\AuditableObserver.php", "line": 99}, {"index": 26, "namespace": null, "name": "vendor/owen-it/laravel-auditing/src/AuditableObserver.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\owen-it\\laravel-auditing\\src\\AuditableObserver.php", "line": 49}, {"index": 33, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 143}, {"index": 34, "namespace": null, "name": "app/Services/Frontend/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserService.php", "line": 358}], "start": **********.103727, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "Auditor.php:83", "source": {"index": 23, "namespace": null, "name": "vendor/owen-it/laravel-auditing/src/Auditor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\owen-it\\laravel-auditing\\src\\Auditor.php", "line": 83}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fowen-it%2Flaravel-auditing%2Fsrc%2FAuditor.php&line=83", "ajax": false, "filename": "Auditor.php", "line": "83"}, "connection": "hri_recland_product", "explain": null, "start_percent": 9.511, "width_percent": 0.256}, {"sql": "select * from `user_infos` where `user_id` = 512 limit 1", "type": "query", "params": [], "bindings": [512], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserInfoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserInfoRepository.php", "line": 20}, {"index": 16, "namespace": null, "name": "app/Repositories/UserInfoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserInfoRepository.php", "line": 12}, {"index": 17, "namespace": null, "name": "app/Services/Frontend/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserService.php", "line": 375}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Frontend/RecController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\RecController.php", "line": 176}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.105965, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "UserInfoRepository.php:20", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserInfoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserInfoRepository.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserInfoRepository.php&line=20", "ajax": false, "filename": "UserInfoRepository.php", "line": "20"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `user_infos` where `user_id` = ? limit 1", "hash": "20d3197d303f67ee1a069fe9317298fdb0c3662c05805b0f37dc2ac5535fca23"}, "start_percent": 9.767, "width_percent": 0.188}, {"sql": "select * from `user_infos` where `user_infos`.`id` = 139 limit 1", "type": "query", "params": [], "bindings": [139], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 118}, {"index": 17, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 139}, {"index": 18, "namespace": null, "name": "app/Repositories/UserInfoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserInfoRepository.php", "line": 16}, {"index": 19, "namespace": null, "name": "app/Services/Frontend/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserService.php", "line": 375}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/RecController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\RecController.php", "line": 176}], "start": **********.107827, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:118", "source": {"index": 16, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 118}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FBaseRepository.php&line=118", "ajax": false, "filename": "BaseRepository.php", "line": "118"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `user_infos` where `user_infos`.`id` = ? limit 1", "hash": "cc1fdec549f7d68aa7902f234a9df919413700e4096df9eb7296fae222fff566"}, "start_percent": 9.955, "width_percent": 0.13}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'hri_recland_product' and table_name = 'user_infos'", "type": "query", "params": [], "bindings": ["hri_recland_product", "user_infos"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 143}, {"index": 16, "namespace": null, "name": "app/Repositories/UserInfoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserInfoRepository.php", "line": 16}, {"index": 17, "namespace": null, "name": "app/Services/Frontend/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserService.php", "line": 375}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Frontend/RecController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\RecController.php", "line": 176}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.1092, "duration": 0.0035, "duration_str": "3.5ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:143", "source": {"index": 15, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 143}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FBaseRepository.php&line=143", "ajax": false, "filename": "BaseRepository.php", "line": "143"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select column_name as `column_name` from information_schema.columns where table_schema = ? and table_name = ?", "hash": "874c70f820607cb9b3665962570159279e91c2f9c7788ca27d25ab52edd9a4f9"}, "start_percent": 10.084, "width_percent": 1.262}, {"sql": "update `user_infos` set `cccd_number` = '1111111111', `user_infos`.`updated_at` = '2025-08-04 14:31:42' where `id` = 139", "type": "query", "params": [], "bindings": ["1111111111", "2025-08-04 14:31:42", 139], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 143}, {"index": 15, "namespace": null, "name": "app/Repositories/UserInfoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserInfoRepository.php", "line": 16}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserService.php", "line": 375}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/RecController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\RecController.php", "line": 176}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.1140969, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:143", "source": {"index": 14, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 143}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FBaseRepository.php&line=143", "ajax": false, "filename": "BaseRepository.php", "line": "143"}, "connection": "hri_recland_product", "explain": null, "start_percent": 11.347, "width_percent": 0.26}, {"sql": "select * from `users` where `users`.`id` = 512 limit 1", "type": "query", "params": [], "bindings": [512], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 118}, {"index": 17, "namespace": null, "name": "app/Services/Frontend/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserService.php", "line": 379}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Frontend/RecController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\RecController.php", "line": 176}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.1161702, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:118", "source": {"index": 16, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 118}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FBaseRepository.php&line=118", "ajax": false, "filename": "BaseRepository.php", "line": "118"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `users`.`id` = ? limit 1", "hash": "62b695c7a5937b15a7884c504ebc83827f2a9393d8daceb5a22dceb704d4dceb"}, "start_percent": 11.606, "width_percent": 0.159}, {"sql": "insert into `job_logs` (`job_id`, `name`, `queue`, `payload`, `updated_at`, `created_at`) values ('', 'App\\Notifications\\UpdateProfileEmployer', 'sync', '{\\\"uuid\\\":\\\"417ee99a-d0d7-4962-b6ae-6be82bcfd957\\\",\\\"displayName\\\":\\\"App\\\\Notifications\\\\UpdateProfileEmployer\\\",\\\"job\\\":\\\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"failOnTimeout\\\":false,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\",\\\"command\\\":\\\"O:48:\\\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\\\":3:{s:11:\\\\\"notifiables\\\\\";O:45:\\\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\\\":5:{s:5:\\\\\"class\\\\\";s:15:\\\\\"App\\\\Models\\\\User\\\\\";s:2:\\\\\"id\\\\\";a:1:{i:0;i:512;}s:9:\\\\\"relations\\\\\";a:0:{}s:10:\\\\\"connection\\\\\";s:5:\\\\\"mysql\\\\\";s:15:\\\\\"collectionClass\\\\\";N;}s:12:\\\\\"notification\\\\\";O:39:\\\\\"App\\\\Notifications\\\\UpdateProfileEmployer\\\\\":2:{s:7:\\\\\"\\u0000*\\u0000user\\\\\";O:45:\\\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\\\":5:{s:5:\\\\\"class\\\\\";s:15:\\\\\"App\\\\Models\\\\User\\\\\";s:2:\\\\\"id\\\\\";i:512;s:9:\\\\\"relations\\\\\";a:0:{}s:10:\\\\\"connection\\\\\";s:5:\\\\\"mysql\\\\\";s:15:\\\\\"collectionClass\\\\\";N;}s:2:\\\\\"id\\\\\";s:36:\\\\\"9eae7f68-80c6-48e5-8519-3fe2e14dd2c2\\\\\";}s:8:\\\\\"channels\\\\\";a:1:{i:0;s:4:\\\\\"mail\\\\\";}}\\\"}}', '2025-08-04 14:31:42', '2025-08-04 14:31:42')", "type": "query", "params": [], "bindings": ["", "App\\Notifications\\UpdateProfileEmployer", "sync", "{\"uuid\":\"417ee99a-d0d7-4962-b6ae-6be82bcfd957\",\"displayName\":\"App\\\\Notifications\\\\UpdateProfileEmployer\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:512;}s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:39:\\\"App\\\\Notifications\\\\UpdateProfileEmployer\\\":2:{s:7:\\\"\\u0000*\\u0000user\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";i:512;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"9eae7f68-80c6-48e5-8519-3fe2e14dd2c2\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:4:\\\"mail\\\";}}\"}}", "2025-08-04 14:31:42", "2025-08-04 14:31:42"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Providers\\AppServiceProvider.php", "line": 75}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SyncQueue.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SyncQueue.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SyncQueue.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SyncQueue.php", "line": 41}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php", "line": 253}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php", "line": 229}], "start": **********.144494, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:75", "source": {"index": 21, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Providers\\AppServiceProvider.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FProviders%2FAppServiceProvider.php&line=75", "ajax": false, "filename": "AppServiceProvider.php", "line": "75"}, "connection": "hri_recland_product", "explain": null, "start_percent": 11.765, "width_percent": 0.249}, {"sql": "select * from `users` where `users`.`id` = 512 limit 1", "type": "query", "params": [], "bindings": [512], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesModels.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesModels.php", "line": 88}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php", "line": 60}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php", "line": 98}], "start": **********.14733, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "SerializesAndRestoresModelIdentifiers.php:108", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FSerializesAndRestoresModelIdentifiers.php&line=108", "ajax": false, "filename": "SerializesAndRestoresModelIdentifiers.php", "line": "108"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `users`.`id` = ? limit 1", "hash": "62b695c7a5937b15a7884c504ebc83827f2a9393d8daceb5a22dceb704d4dceb"}, "start_percent": 12.014, "width_percent": 0.191}, {"sql": "select * from `users` where `users`.`id` in (512)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 60}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesModels.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesModels.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php", "line": 60}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php", "line": 98}], "start": **********.149329, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "SerializesAndRestoresModelIdentifiers.php:80", "source": {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FSerializesAndRestoresModelIdentifiers.php&line=80", "ajax": false, "filename": "SerializesAndRestoresModelIdentifiers.php", "line": "80"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `users`.`id` in (512)", "hash": "1216ff3bf8b8e0ab1053a91cfaebee74f0f91ec9ac571cfdf9656de6355aa46f"}, "start_percent": 12.205, "width_percent": 0.151}, {"sql": "insert into `email_logs` (`from`, `to`, `cc`, `subject`, `html_content`, `hash`, `status`, `updated_at`, `created_at`) values ('[\\\"<EMAIL>\\\"]', '[\\\"<EMAIL>\\\"]', '[]', '[Recland] [CẬP NHẬT HỒ SƠ CÁ NHÂN THÀNH CÔNG]', '<!DOCTYPE html>\\r\\n<html>\\r\\n\\r\\n<body width=\\\"100%\\\"\\r\\n    style=\\\"margin: 0; padding: 0 !important; mso-line-height-rule: exactly; background-color: #f1f1f1;font-family: Arial, \\'Helvetica Neue\\', Helvetica, sans-serif\\\">\\r\\n    <center style=\\\"width: 100%; background-color: #f1f1f1;\\\">\\r\\n        <div style=\\\"max-width: 600px; margin: 0 auto;\\\">\\r\\n            <table align=\\\"center\\\" role=\\\"presentation\\\" cellspacing=\\\"0\\\" cellpadding=\\\"0\\\" border=\\\"0\\\" width=\\\"100%\\\"\\r\\n                style=\\\"margin: auto;\\\">\\r\\n                <tr>\\r\\n                    <td>\\r\\n                        <div style=\\\"text-align: center;padding: 25px 0;background: #FCFCFE;\\\">\\r\\n                            <img style=\\\"max-width: 100%\\\"\\r\\n                                src=\\\"http://recland.local/frontend/asset/images/template-email/logo.png?v=689061de29bb4\\\">\\r\\n                        </div>\\r\\n                    </td>\\r\\n                </tr>\\r\\n            </table>\\r\\n            <div style=\\\"background-image: linear-gradient(192.45deg, #155969 18.03%, #DAE6E9 93.73%);\\\">\\r\\n                <div\\r\\n                    style=\\\"padding-top: 0;padding-left: 40px;padding-right: 40px;padding-bottom: 32px;\\r\\n                background-image: url(http://recland.local/frontend/asset/images/template-email/background.png?v=689061de29bc5);\\r\\n                background-repeat: no-repeat;background-size: 100%;\\\">\\r\\n                    <table>\\r\\n                        <tr>\\r\\n                            <td>\\r\\n                                <div\\r\\n                                    style=\\\"padding-top: 80px;height: 194px;text-align: center;font-style: normal;font-weight: 600;font-size: 26px;line-height: 31px;color: #FCFCFE;\\\">\\r\\n                                    <div style=\\\"margin-bottom: 14px\\\">\\r\\n                                                                            </div>\\r\\n                                    <div>\\r\\n                                                                            </div>\\r\\n\\r\\n                                </div>\\r\\n                            </td>\\r\\n                        </tr>\\r\\n                    </table>\\r\\n                    <table style=\\\"width: 100%\\\" role=\\\"presentation\\\" cellspacing=\\\"0\\\" cellpadding=\\\"0\\\" border=\\\"0\\\"\\r\\n                        width=\\\"100%\\\">\\r\\n                        <tr>\\r\\n                            <td>\\r\\n                                    <div\\r\\n        style=\\\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\\\">\\r\\n        <p>Xin chào, HAU Nguyen Thi</p>\\r\\n        Recland thông báo hồ sơ của bạn đã được cập nhật thành công.\\r\\n        <p><b>Trân trọng,</b></p>\\r\\n        <p><i>Đội ngũ Recland.</i></p>\\r\\n    </div>\\r\\n    <div style=\\\"border: 5px solid #F7F7F7;\\\"></div>\\r\\n    <div\\r\\n        style=\\\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\\\">\\r\\n        <p>Hello, HAU Nguyen Thi</p>\\r\\n        Recland informs you that your profile has been successfully updated.\\r\\n        <p><b>Best regards,</b></p>\\r\\n        <p><i>Recland team.</i></p>\\r\\n    </div>\\r\\n                            </td>\\r\\n                        </tr>\\r\\n                        <tr>\\r\\n                            <td>\\r\\n                                <div style=\\\"padding:12px 0\\\">\\r\\n                                    <div\\r\\n                                        style=\\\"background-image: url(http://recland.local/frontend/asset/images/template-email/background-footer.png?v=689061de29be4);background-position: bottom left;background-repeat: no-repeat;text-align: center;background-color: #FCFCFE;padding-top: 22px;padding-bottom: 12px\\\">\\r\\n                                        <div style=\\\"margin-bottom: 12px;text-align: center\\\">\\r\\n                                            <a style=\\\"padding-left: 16px;padding-right: 16px\\\" href=\\\"#\\\"><img\\r\\n                                                    src=\\\"http://recland.local/frontend/asset/images/template-email/icon-twitter.png?v=689061de29bef\\\"></a>\\r\\n                                            <a style=\\\"padding-left: 16px;padding-right: 16px\\\" href=\\\"#\\\"><img\\r\\n                                                    src=\\\"http://recland.local/frontend/asset/images/template-email/icon-facebook.png?v=689061de29bf9\\\"></a>\\r\\n                                            <a style=\\\"padding-left: 16px;padding-right: 16px\\\" href=\\\"#\\\"><img\\r\\n                                                    src=\\\"http://recland.local/frontend/asset/images/template-email/icon-instagram.png?v=689061de29c04\\\"></a>\\r\\n                                        </div>\\r\\n                                        <p\\r\\n                                            style=\\\"margin-top: 0;margin-bottom: 8px;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #17677B;\\\">\\r\\n                                            Nền tảng tạo ra cơ hội kiếm tiền dành cho HR Freelancer</p>\\r\\n                                        <p\\r\\n                                            style=\\\"margin-top: 0;margin-bottom: 0;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #3C4455;\\\">\\r\\n                                            © 2022 Recland.co</p>\\r\\n                                    </div>\\r\\n                                </div>\\r\\n\\r\\n                            </td>\\r\\n                        </tr>\\r\\n                    </table>\\r\\n                </div>\\r\\n            </div>\\r\\n        </div>\\r\\n    </center>\\r\\n</body>\\r\\n\\r\\n</html>\\r\\n', '3390c972591f9d6e2538ff349cb9f628', 0, '2025-08-04 14:31:42', '2025-08-04 14:31:42')", "type": "query", "params": [], "bindings": ["[\"<EMAIL>\"]", "[\"<EMAIL>\"]", "[]", "[Recland] [CẬP NHẬT HỒ SƠ CÁ NHÂN THÀNH CÔNG]", "<!DOCTYPE html>\r\n<html>\r\n\r\n<body width=\"100%\"\r\n    style=\"margin: 0; padding: 0 !important; mso-line-height-rule: exactly; background-color: #f1f1f1;font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif\">\r\n    <center style=\"width: 100%; background-color: #f1f1f1;\">\r\n        <div style=\"max-width: 600px; margin: 0 auto;\">\r\n            <table align=\"center\" role=\"presentation\" cellspacing=\"0\" cellpadding=\"0\" border=\"0\" width=\"100%\"\r\n                style=\"margin: auto;\">\r\n                <tr>\r\n                    <td>\r\n                        <div style=\"text-align: center;padding: 25px 0;background: #FCFCFE;\">\r\n                            <img style=\"max-width: 100%\"\r\n                                src=\"http://recland.local/frontend/asset/images/template-email/logo.png?v=689061de29bb4\">\r\n                        </div>\r\n                    </td>\r\n                </tr>\r\n            </table>\r\n            <div style=\"background-image: linear-gradient(192.45deg, #155969 18.03%, #DAE6E9 93.73%);\">\r\n                <div\r\n                    style=\"padding-top: 0;padding-left: 40px;padding-right: 40px;padding-bottom: 32px;\r\n                background-image: url(http://recland.local/frontend/asset/images/template-email/background.png?v=689061de29bc5);\r\n                background-repeat: no-repeat;background-size: 100%;\">\r\n                    <table>\r\n                        <tr>\r\n                            <td>\r\n                                <div\r\n                                    style=\"padding-top: 80px;height: 194px;text-align: center;font-style: normal;font-weight: 600;font-size: 26px;line-height: 31px;color: #FCFCFE;\">\r\n                                    <div style=\"margin-bottom: 14px\">\r\n                                                                            </div>\r\n                                    <div>\r\n                                                                            </div>\r\n\r\n                                </div>\r\n                            </td>\r\n                        </tr>\r\n                    </table>\r\n                    <table style=\"width: 100%\" role=\"presentation\" cellspacing=\"0\" cellpadding=\"0\" border=\"0\"\r\n                        width=\"100%\">\r\n                        <tr>\r\n                            <td>\r\n                                    <div\r\n        style=\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\">\r\n        <p>Xin chào, HAU Nguyen Thi</p>\r\n        Recland thông báo hồ sơ của bạn đã được cập nhật thành công.\r\n        <p><b>Trân trọng,</b></p>\r\n        <p><i>Đội ngũ Recland.</i></p>\r\n    </div>\r\n    <div style=\"border: 5px solid #F7F7F7;\"></div>\r\n    <div\r\n        style=\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\">\r\n        <p>Hello, HAU Nguyen Thi</p>\r\n        Recland informs you that your profile has been successfully updated.\r\n        <p><b>Best regards,</b></p>\r\n        <p><i>Recland team.</i></p>\r\n    </div>\r\n                            </td>\r\n                        </tr>\r\n                        <tr>\r\n                            <td>\r\n                                <div style=\"padding:12px 0\">\r\n                                    <div\r\n                                        style=\"background-image: url(http://recland.local/frontend/asset/images/template-email/background-footer.png?v=689061de29be4);background-position: bottom left;background-repeat: no-repeat;text-align: center;background-color: #FCFCFE;padding-top: 22px;padding-bottom: 12px\">\r\n                                        <div style=\"margin-bottom: 12px;text-align: center\">\r\n                                            <a style=\"padding-left: 16px;padding-right: 16px\" href=\"#\"><img\r\n                                                    src=\"http://recland.local/frontend/asset/images/template-email/icon-twitter.png?v=689061de29bef\"></a>\r\n                                            <a style=\"padding-left: 16px;padding-right: 16px\" href=\"#\"><img\r\n                                                    src=\"http://recland.local/frontend/asset/images/template-email/icon-facebook.png?v=689061de29bf9\"></a>\r\n                                            <a style=\"padding-left: 16px;padding-right: 16px\" href=\"#\"><img\r\n                                                    src=\"http://recland.local/frontend/asset/images/template-email/icon-instagram.png?v=689061de29c04\"></a>\r\n                                        </div>\r\n                                        <p\r\n                                            style=\"margin-top: 0;margin-bottom: 8px;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #17677B;\">\r\n                                            Nền tảng tạo ra cơ hội kiếm tiền dành cho HR Freelancer</p>\r\n                                        <p\r\n                                            style=\"margin-top: 0;margin-bottom: 0;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #3C4455;\">\r\n                                            © 2022 Recland.co</p>\r\n                                    </div>\r\n                                </div>\r\n\r\n                            </td>\r\n                        </tr>\r\n                    </table>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </center>\r\n</body>\r\n\r\n</html>\r\n", "3390c972591f9d6e2538ff349cb9f628", 0, "2025-08-04 14:31:42", "2025-08-04 14:31:42"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Jobs/CreateEmailLog.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Jobs\\CreateEmailLog.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 37}], "start": **********.173553, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "CreateEmailLog.php:68", "source": {"index": 21, "namespace": null, "name": "app/Jobs/CreateEmailLog.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Jobs\\CreateEmailLog.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FJobs%2FCreateEmailLog.php&line=68", "ajax": false, "filename": "CreateEmailLog.php", "line": "68"}, "connection": "hri_recland_product", "explain": null, "start_percent": 12.357, "width_percent": 0.451}, {"sql": "insert into `job_logs` (`job_id`, `name`, `queue`, `payload`, `updated_at`, `created_at`) values ('', 'App\\Jobs\\UpdateEmailLogStatus', 'sync', '{\\\"uuid\\\":\\\"8ff1cd8d-e7f8-47c1-b7db-a24d3a89a229\\\",\\\"displayName\\\":\\\"App\\\\Jobs\\\\UpdateEmailLogStatus\\\",\\\"job\\\":\\\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"failOnTimeout\\\":false,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"App\\\\Jobs\\\\UpdateEmailLogStatus\\\",\\\"command\\\":\\\"O:29:\\\\\"App\\\\Jobs\\\\UpdateEmailLogStatus\\\\\":1:{s:5:\\\\\"email\\\\\";O:28:\\\\\"Symfony\\\\Component\\\\Mime\\\\Email\\\\\":6:{i:0;N;i:1;N;i:2;s:5639:\\\\\"<!DOCTYPE html>\\r\\n<html>\\r\\n\\r\\n<body width=\\\\\"100%\\\\\"\\r\\n    style=\\\\\"margin: 0; padding: 0 !important; mso-line-height-rule: exactly; background-color: #f1f1f1;font-family: Arial, \\'Helvetica Neue\\', Helvetica, sans-serif\\\\\">\\r\\n    <center style=\\\\\"width: 100%; background-color: #f1f1f1;\\\\\">\\r\\n        <div style=\\\\\"max-width: 600px; margin: 0 auto;\\\\\">\\r\\n            <table align=\\\\\"center\\\\\" role=\\\\\"presentation\\\\\" cellspacing=\\\\\"0\\\\\" cellpadding=\\\\\"0\\\\\" border=\\\\\"0\\\\\" width=\\\\\"100%\\\\\"\\r\\n                style=\\\\\"margin: auto;\\\\\">\\r\\n                <tr>\\r\\n                    <td>\\r\\n                        <div style=\\\\\"text-align: center;padding: 25px 0;background: #FCFCFE;\\\\\">\\r\\n                            <img style=\\\\\"max-width: 100%\\\\\"\\r\\n                                src=\\\\\"http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/logo.png?v=689061de29bb4\\\\\">\\r\\n                        <\\/div>\\r\\n                    <\\/td>\\r\\n                <\\/tr>\\r\\n            <\\/table>\\r\\n            <div style=\\\\\"background-image: linear-gradient(192.45deg, #155969 18.03%, #DAE6E9 93.73%);\\\\\">\\r\\n                <div\\r\\n                    style=\\\\\"padding-top: 0;padding-left: 40px;padding-right: 40px;padding-bottom: 32px;\\r\\n                background-image: url(http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/background.png?v=689061de29bc5);\\r\\n                background-repeat: no-repeat;background-size: 100%;\\\\\">\\r\\n                    <table>\\r\\n                        <tr>\\r\\n                            <td>\\r\\n                                <div\\r\\n                                    style=\\\\\"padding-top: 80px;height: 194px;text-align: center;font-style: normal;font-weight: 600;font-size: 26px;line-height: 31px;color: #FCFCFE;\\\\\">\\r\\n                                    <div style=\\\\\"margin-bottom: 14px\\\\\">\\r\\n                                                                            <\\/div>\\r\\n                                    <div>\\r\\n                                                                            <\\/div>\\r\\n\\r\\n                                <\\/div>\\r\\n                            <\\/td>\\r\\n                        <\\/tr>\\r\\n                    <\\/table>\\r\\n                    <table style=\\\\\"width: 100%\\\\\" role=\\\\\"presentation\\\\\" cellspacing=\\\\\"0\\\\\" cellpadding=\\\\\"0\\\\\" border=\\\\\"0\\\\\"\\r\\n                        width=\\\\\"100%\\\\\">\\r\\n                        <tr>\\r\\n                            <td>\\r\\n                                    <div\\r\\n        style=\\\\\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\\\\\">\\r\\n        <p>Xin ch\\u00e0o, HAU Nguyen Thi<\\/p>\\r\\n        Recland th\\u00f4ng b\\u00e1o h\\u1ed3 s\\u01a1 c\\u1ee7a b\\u1ea1n \\u0111\\u00e3 \\u0111\\u01b0\\u1ee3c c\\u1eadp nh\\u1eadt th\\u00e0nh c\\u00f4ng.\\r\\n        <p><b>Tr\\u00e2n tr\\u1ecdng,<\\/b><\\/p>\\r\\n        <p><i>\\u0110\\u1ed9i ng\\u0169 Recland.<\\/i><\\/p>\\r\\n    <\\/div>\\r\\n    <div style=\\\\\"border: 5px solid #F7F7F7;\\\\\"><\\/div>\\r\\n    <div\\r\\n        style=\\\\\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\\\\\">\\r\\n        <p>Hello, HAU Nguyen Thi<\\/p>\\r\\n        Recland informs you that your profile has been successfully updated.\\r\\n        <p><b>Best regards,<\\/b><\\/p>\\r\\n        <p><i>Recland team.<\\/i><\\/p>\\r\\n    <\\/div>\\r\\n                            <\\/td>\\r\\n                        <\\/tr>\\r\\n                        <tr>\\r\\n                            <td>\\r\\n                                <div style=\\\\\"padding:12px 0\\\\\">\\r\\n                                    <div\\r\\n                                        style=\\\\\"background-image: url(http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/background-footer.png?v=689061de29be4);background-position: bottom left;background-repeat: no-repeat;text-align: center;background-color: #FCFCFE;padding-top: 22px;padding-bottom: 12px\\\\\">\\r\\n                                        <div style=\\\\\"margin-bottom: 12px;text-align: center\\\\\">\\r\\n                                            <a style=\\\\\"padding-left: 16px;padding-right: 16px\\\\\" href=\\\\\"#\\\\\"><img\\r\\n                                                    src=\\\\\"http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/icon-twitter.png?v=689061de29bef\\\\\"><\\/a>\\r\\n                                            <a style=\\\\\"padding-left: 16px;padding-right: 16px\\\\\" href=\\\\\"#\\\\\"><img\\r\\n                                                    src=\\\\\"http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/icon-facebook.png?v=689061de29bf9\\\\\"><\\/a>\\r\\n                                            <a style=\\\\\"padding-left: 16px;padding-right: 16px\\\\\" href=\\\\\"#\\\\\"><img\\r\\n                                                    src=\\\\\"http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/icon-instagram.png?v=689061de29c04\\\\\"><\\/a>\\r\\n                                        <\\/div>\\r\\n                                        <p\\r\\n                                            style=\\\\\"margin-top: 0;margin-bottom: 8px;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #17677B;\\\\\">\\r\\n                                            N\\u1ec1n t\\u1ea3ng t\\u1ea1o ra c\\u01a1 h\\u1ed9i ki\\u1ebfm ti\\u1ec1n d\\u00e0nh cho HR Freelancer<\\/p>\\r\\n                                        <p\\r\\n                                            style=\\\\\"margin-top: 0;margin-bottom: 0;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #3C4455;\\\\\">\\r\\n                                            \\u00a9 2022 Recland.co<\\/p>\\r\\n                                    <\\/div>\\r\\n                                <\\/div>\\r\\n\\r\\n                            <\\/td>\\r\\n                        <\\/tr>\\r\\n                    <\\/table>\\r\\n                <\\/div>\\r\\n            <\\/div>\\r\\n        <\\/div>\\r\\n    <\\/center>\\r\\n<\\/body>\\r\\n\\r\\n<\\/html>\\r\\n\\\\\";i:3;s:5:\\\\\"utf-8\\\\\";i:4;a:0:{}i:5;a:2:{i:0;O:37:\\\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\Headers\\\\\":2:{s:46:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\Headers\\u0000headers\\\\\";a:4:{s:4:\\\\\"from\\\\\";a:1:{i:0;O:47:\\\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\\\\":5:{s:50:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\\\";s:4:\\\\\"From\\\\\";s:56:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\\\";i:76;s:50:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\\\";N;s:53:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\\\";s:5:\\\\\"utf-8\\\\\";s:58:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\u0000addresses\\\\\";a:1:{i:0;O:30:\\\\\"Symfony\\\\Component\\\\Mime\\\\Address\\\\\":2:{s:39:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000address\\\\\";s:18:\\\\\"<EMAIL>\\\\\";s:36:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000name\\\\\";s:7:\\\\\"RECLAND\\\\\";}}}}s:2:\\\\\"to\\\\\";a:1:{i:0;O:47:\\\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\\\\":5:{s:50:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\\\";s:2:\\\\\"To\\\\\";s:56:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\\\";i:76;s:50:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\\\";N;s:53:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\\\";s:5:\\\\\"utf-8\\\\\";s:58:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\u0000addresses\\\\\";a:1:{i:0;O:30:\\\\\"Symfony\\\\Component\\\\Mime\\\\Address\\\\\":2:{s:39:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000address\\\\\";s:21:\\\\\"<EMAIL>\\\\\";s:36:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000name\\\\\";s:0:\\\\\"\\\\\";}}}}s:7:\\\\\"subject\\\\\";a:1:{i:0;O:48:\\\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\\\\":5:{s:50:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\\\";s:7:\\\\\"Subject\\\\\";s:56:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\\\";i:76;s:50:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\\\";N;s:53:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\\\";s:5:\\\\\"utf-8\\\\\";s:55:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\u0000value\\\\\";s:56:\\\\\"[Recland] [C\\u1eacP NH\\u1eacT H\\u1ed2 S\\u01a0 C\\u00c1 NH\\u00c2N TH\\u00c0NH C\\u00d4NG]\\\\\";}}s:17:\\\\\"x-original-emails\\\\\";a:1:{i:0;O:48:\\\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\\\\":5:{s:50:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\\\";s:17:\\\\\"X-Original-Emails\\\\\";s:56:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\\\";i:76;s:50:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\\\";N;s:53:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\\\";s:5:\\\\\"utf-8\\\\\";s:55:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\u0000value\\\\\";s:2:\\\\\"[]\\\\\";}}}s:49:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\Headers\\u0000lineLength\\\\\";i:76;}i:1;N;}}}\\\"}}', '2025-08-04 14:31:43', '2025-08-04 14:31:43')", "type": "query", "params": [], "bindings": ["", "App\\Jobs\\UpdateEmailLogStatus", "sync", "{\"uuid\":\"8ff1cd8d-e7f8-47c1-b7db-a24d3a89a229\",\"displayName\":\"App\\\\Jobs\\\\UpdateEmailLogStatus\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\UpdateEmailLogStatus\",\"command\":\"O:29:\\\"App\\\\Jobs\\\\UpdateEmailLogStatus\\\":1:{s:5:\\\"email\\\";O:28:\\\"Symfony\\\\Component\\\\Mime\\\\Email\\\":6:{i:0;N;i:1;N;i:2;s:5639:\\\"<!DOCTYPE html>\\r\\n<html>\\r\\n\\r\\n<body width=\\\"100%\\\"\\r\\n    style=\\\"margin: 0; padding: 0 !important; mso-line-height-rule: exactly; background-color: #f1f1f1;font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif\\\">\\r\\n    <center style=\\\"width: 100%; background-color: #f1f1f1;\\\">\\r\\n        <div style=\\\"max-width: 600px; margin: 0 auto;\\\">\\r\\n            <table align=\\\"center\\\" role=\\\"presentation\\\" cellspacing=\\\"0\\\" cellpadding=\\\"0\\\" border=\\\"0\\\" width=\\\"100%\\\"\\r\\n                style=\\\"margin: auto;\\\">\\r\\n                <tr>\\r\\n                    <td>\\r\\n                        <div style=\\\"text-align: center;padding: 25px 0;background: #FCFCFE;\\\">\\r\\n                            <img style=\\\"max-width: 100%\\\"\\r\\n                                src=\\\"http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/logo.png?v=689061de29bb4\\\">\\r\\n                        <\\/div>\\r\\n                    <\\/td>\\r\\n                <\\/tr>\\r\\n            <\\/table>\\r\\n            <div style=\\\"background-image: linear-gradient(192.45deg, #155969 18.03%, #DAE6E9 93.73%);\\\">\\r\\n                <div\\r\\n                    style=\\\"padding-top: 0;padding-left: 40px;padding-right: 40px;padding-bottom: 32px;\\r\\n                background-image: url(http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/background.png?v=689061de29bc5);\\r\\n                background-repeat: no-repeat;background-size: 100%;\\\">\\r\\n                    <table>\\r\\n                        <tr>\\r\\n                            <td>\\r\\n                                <div\\r\\n                                    style=\\\"padding-top: 80px;height: 194px;text-align: center;font-style: normal;font-weight: 600;font-size: 26px;line-height: 31px;color: #FCFCFE;\\\">\\r\\n                                    <div style=\\\"margin-bottom: 14px\\\">\\r\\n                                                                            <\\/div>\\r\\n                                    <div>\\r\\n                                                                            <\\/div>\\r\\n\\r\\n                                <\\/div>\\r\\n                            <\\/td>\\r\\n                        <\\/tr>\\r\\n                    <\\/table>\\r\\n                    <table style=\\\"width: 100%\\\" role=\\\"presentation\\\" cellspacing=\\\"0\\\" cellpadding=\\\"0\\\" border=\\\"0\\\"\\r\\n                        width=\\\"100%\\\">\\r\\n                        <tr>\\r\\n                            <td>\\r\\n                                    <div\\r\\n        style=\\\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\\\">\\r\\n        <p>Xin ch\\u00e0o, HAU Nguyen Thi<\\/p>\\r\\n        Recland th\\u00f4ng b\\u00e1o h\\u1ed3 s\\u01a1 c\\u1ee7a b\\u1ea1n \\u0111\\u00e3 \\u0111\\u01b0\\u1ee3c c\\u1eadp nh\\u1eadt th\\u00e0nh c\\u00f4ng.\\r\\n        <p><b>Tr\\u00e2n tr\\u1ecdng,<\\/b><\\/p>\\r\\n        <p><i>\\u0110\\u1ed9i ng\\u0169 Recland.<\\/i><\\/p>\\r\\n    <\\/div>\\r\\n    <div style=\\\"border: 5px solid #F7F7F7;\\\"><\\/div>\\r\\n    <div\\r\\n        style=\\\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\\\">\\r\\n        <p>Hello, HAU Nguyen Thi<\\/p>\\r\\n        Recland informs you that your profile has been successfully updated.\\r\\n        <p><b>Best regards,<\\/b><\\/p>\\r\\n        <p><i>Recland team.<\\/i><\\/p>\\r\\n    <\\/div>\\r\\n                            <\\/td>\\r\\n                        <\\/tr>\\r\\n                        <tr>\\r\\n                            <td>\\r\\n                                <div style=\\\"padding:12px 0\\\">\\r\\n                                    <div\\r\\n                                        style=\\\"background-image: url(http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/background-footer.png?v=689061de29be4);background-position: bottom left;background-repeat: no-repeat;text-align: center;background-color: #FCFCFE;padding-top: 22px;padding-bottom: 12px\\\">\\r\\n                                        <div style=\\\"margin-bottom: 12px;text-align: center\\\">\\r\\n                                            <a style=\\\"padding-left: 16px;padding-right: 16px\\\" href=\\\"#\\\"><img\\r\\n                                                    src=\\\"http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/icon-twitter.png?v=689061de29bef\\\"><\\/a>\\r\\n                                            <a style=\\\"padding-left: 16px;padding-right: 16px\\\" href=\\\"#\\\"><img\\r\\n                                                    src=\\\"http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/icon-facebook.png?v=689061de29bf9\\\"><\\/a>\\r\\n                                            <a style=\\\"padding-left: 16px;padding-right: 16px\\\" href=\\\"#\\\"><img\\r\\n                                                    src=\\\"http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/icon-instagram.png?v=689061de29c04\\\"><\\/a>\\r\\n                                        <\\/div>\\r\\n                                        <p\\r\\n                                            style=\\\"margin-top: 0;margin-bottom: 8px;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #17677B;\\\">\\r\\n                                            N\\u1ec1n t\\u1ea3ng t\\u1ea1o ra c\\u01a1 h\\u1ed9i ki\\u1ebfm ti\\u1ec1n d\\u00e0nh cho HR Freelancer<\\/p>\\r\\n                                        <p\\r\\n                                            style=\\\"margin-top: 0;margin-bottom: 0;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #3C4455;\\\">\\r\\n                                            \\u00a9 2022 Recland.co<\\/p>\\r\\n                                    <\\/div>\\r\\n                                <\\/div>\\r\\n\\r\\n                            <\\/td>\\r\\n                        <\\/tr>\\r\\n                    <\\/table>\\r\\n                <\\/div>\\r\\n            <\\/div>\\r\\n        <\\/div>\\r\\n    <\\/center>\\r\\n<\\/body>\\r\\n\\r\\n<\\/html>\\r\\n\\\";i:3;s:5:\\\"utf-8\\\";i:4;a:0:{}i:5;a:2:{i:0;O:37:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\Headers\\\":2:{s:46:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\Headers\\u0000headers\\\";a:4:{s:4:\\\"from\\\";a:1:{i:0;O:47:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\\":5:{s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\";s:4:\\\"From\\\";s:56:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\";i:76;s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\";N;s:53:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\";s:5:\\\"utf-8\\\";s:58:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\u0000addresses\\\";a:1:{i:0;O:30:\\\"Symfony\\\\Component\\\\Mime\\\\Address\\\":2:{s:39:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000address\\\";s:18:\\\"<EMAIL>\\\";s:36:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000name\\\";s:7:\\\"RECLAND\\\";}}}}s:2:\\\"to\\\";a:1:{i:0;O:47:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\\":5:{s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\";s:2:\\\"To\\\";s:56:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\";i:76;s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\";N;s:53:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\";s:5:\\\"utf-8\\\";s:58:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\u0000addresses\\\";a:1:{i:0;O:30:\\\"Symfony\\\\Component\\\\Mime\\\\Address\\\":2:{s:39:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000address\\\";s:21:\\\"<EMAIL>\\\";s:36:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000name\\\";s:0:\\\"\\\";}}}}s:7:\\\"subject\\\";a:1:{i:0;O:48:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\\":5:{s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\";s:7:\\\"Subject\\\";s:56:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\";i:76;s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\";N;s:53:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\";s:5:\\\"utf-8\\\";s:55:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\u0000value\\\";s:56:\\\"[Recland] [C\\u1eacP NH\\u1eacT H\\u1ed2 S\\u01a0 C\\u00c1 NH\\u00c2N TH\\u00c0NH C\\u00d4NG]\\\";}}s:17:\\\"x-original-emails\\\";a:1:{i:0;O:48:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\\":5:{s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\";s:17:\\\"X-Original-Emails\\\";s:56:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\";i:76;s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\";N;s:53:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\";s:5:\\\"utf-8\\\";s:55:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\u0000value\\\";s:2:\\\"[]\\\";}}}s:49:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\Headers\\u0000lineLength\\\";i:76;}i:1;N;}}}\"}}", "2025-08-04 14:31:43", "2025-08-04 14:31:43"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Providers\\AppServiceProvider.php", "line": 75}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SyncQueue.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SyncQueue.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SyncQueue.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SyncQueue.php", "line": 41}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php", "line": 253}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php", "line": 229}], "start": **********.3300018, "duration": 0.00217, "duration_str": "2.17ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:75", "source": {"index": 21, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Providers\\AppServiceProvider.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FProviders%2FAppServiceProvider.php&line=75", "ajax": false, "filename": "AppServiceProvider.php", "line": "75"}, "connection": "hri_recland_product", "explain": null, "start_percent": 12.807, "width_percent": 0.783}, {"sql": "select * from `email_logs` where `hash` = '92082d4bc6af578d785908c3aeb3ced6' and `created_at` >= '2025-08-04 14:26:43'", "type": "query", "params": [], "bindings": ["92082d4bc6af578d785908c3aeb3ced6", "2025-08-04 14:26:43"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Jobs/UpdateEmailLogStatus.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Jobs\\UpdateEmailLogStatus.php", "line": 59}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 37}], "start": **********.335169, "duration": 0.22873, "duration_str": "229ms", "memory": 0, "memory_str": null, "filename": "UpdateEmailLogStatus.php:59", "source": {"index": 14, "namespace": null, "name": "app/Jobs/UpdateEmailLogStatus.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Jobs\\UpdateEmailLogStatus.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FJobs%2FUpdateEmailLogStatus.php&line=59", "ajax": false, "filename": "UpdateEmailLogStatus.php", "line": "59"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `email_logs` where `hash` = ? and `created_at` >= ?", "hash": "31c62cfad3f2f102bb6636988443f06ea351984dc7fa16ad19a679fe2dc9aed0"}, "start_percent": 13.59, "width_percent": 82.497}, {"sql": "insert into `job_logs` (`job_id`, `name`, `queue`, `payload`, `updated_at`, `created_at`) values ('', 'App\\Notifications\\UpdateProfileEmployer', 'sync', '{\\\"uuid\\\":\\\"93cf4ed8-1abd-4f75-b7a6-6e8016126ec3\\\",\\\"displayName\\\":\\\"App\\\\Notifications\\\\UpdateProfileEmployer\\\",\\\"job\\\":\\\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"failOnTimeout\\\":false,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\",\\\"command\\\":\\\"O:48:\\\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\\\":3:{s:11:\\\\\"notifiables\\\\\";O:45:\\\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\\\":5:{s:5:\\\\\"class\\\\\";s:15:\\\\\"App\\\\Models\\\\User\\\\\";s:2:\\\\\"id\\\\\";a:1:{i:0;i:512;}s:9:\\\\\"relations\\\\\";a:0:{}s:10:\\\\\"connection\\\\\";s:5:\\\\\"mysql\\\\\";s:15:\\\\\"collectionClass\\\\\";N;}s:12:\\\\\"notification\\\\\";O:39:\\\\\"App\\\\Notifications\\\\UpdateProfileEmployer\\\\\":2:{s:7:\\\\\"\\u0000*\\u0000user\\\\\";O:45:\\\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\\\":5:{s:5:\\\\\"class\\\\\";s:15:\\\\\"App\\\\Models\\\\User\\\\\";s:2:\\\\\"id\\\\\";i:512;s:9:\\\\\"relations\\\\\";a:0:{}s:10:\\\\\"connection\\\\\";s:5:\\\\\"mysql\\\\\";s:15:\\\\\"collectionClass\\\\\";N;}s:2:\\\\\"id\\\\\";s:36:\\\\\"9eae7f68-80c6-48e5-8519-3fe2e14dd2c2\\\\\";}s:8:\\\\\"channels\\\\\";a:1:{i:0;s:8:\\\\\"database\\\\\";}}\\\"}}', '2025-08-04 14:31:43', '2025-08-04 14:31:43')", "type": "query", "params": [], "bindings": ["", "App\\Notifications\\UpdateProfileEmployer", "sync", "{\"uuid\":\"93cf4ed8-1abd-4f75-b7a6-6e8016126ec3\",\"displayName\":\"App\\\\Notifications\\\\UpdateProfileEmployer\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:512;}s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:39:\\\"App\\\\Notifications\\\\UpdateProfileEmployer\\\":2:{s:7:\\\"\\u0000*\\u0000user\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";i:512;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"9eae7f68-80c6-48e5-8519-3fe2e14dd2c2\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:8:\\\"database\\\";}}\"}}", "2025-08-04 14:31:43", "2025-08-04 14:31:43"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Providers\\AppServiceProvider.php", "line": 75}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SyncQueue.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SyncQueue.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SyncQueue.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SyncQueue.php", "line": 41}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php", "line": 253}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php", "line": 229}], "start": **********.567148, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:75", "source": {"index": 21, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Providers\\AppServiceProvider.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FProviders%2FAppServiceProvider.php&line=75", "ajax": false, "filename": "AppServiceProvider.php", "line": "75"}, "connection": "hri_recland_product", "explain": null, "start_percent": 96.087, "width_percent": 0.249}, {"sql": "select * from `users` where `users`.`id` = 512 limit 1", "type": "query", "params": [], "bindings": [512], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesModels.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesModels.php", "line": 88}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php", "line": 60}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php", "line": 98}], "start": **********.5697148, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "SerializesAndRestoresModelIdentifiers.php:108", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FSerializesAndRestoresModelIdentifiers.php&line=108", "ajax": false, "filename": "SerializesAndRestoresModelIdentifiers.php", "line": "108"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `users`.`id` = ? limit 1", "hash": "62b695c7a5937b15a7884c504ebc83827f2a9393d8daceb5a22dceb704d4dceb"}, "start_percent": 96.336, "width_percent": 0.216}, {"sql": "select * from `users` where `users`.`id` in (512)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 60}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesModels.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesModels.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php", "line": 60}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php", "line": 98}], "start": **********.573186, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "SerializesAndRestoresModelIdentifiers.php:80", "source": {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FSerializesAndRestoresModelIdentifiers.php&line=80", "ajax": false, "filename": "SerializesAndRestoresModelIdentifiers.php", "line": "80"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `users`.`id` in (512)", "hash": "1216ff3bf8b8e0ab1053a91cfaebee74f0f91ec9ac571cfdf9656de6355aa46f"}, "start_percent": 96.552, "width_percent": 0.155}, {"sql": "select `value`, `key` from `settings` where `key` like '%settings.vi.notification%'", "type": "query", "params": [], "bindings": ["%settings.vi.notification%"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Repositories/SettingRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SettingRepository.php", "line": 23}, {"index": 14, "namespace": null, "name": "app/Services/Frontend/SettingService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\SettingService.php", "line": 49}, {"index": 15, "namespace": null, "name": "app/Notifications/UpdateProfileEmployer.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Notifications\\UpdateProfileEmployer.php", "line": 74}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/Channels/DatabaseChannel.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Channels\\DatabaseChannel.php", "line": 60}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/Channels/DatabaseChannel.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Channels\\DatabaseChannel.php", "line": 38}], "start": **********.576717, "duration": 0.00509, "duration_str": "5.09ms", "memory": 0, "memory_str": null, "filename": "SettingRepository.php:23", "source": {"index": 13, "namespace": null, "name": "app/Repositories/SettingRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SettingRepository.php", "line": 23}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSettingRepository.php&line=23", "ajax": false, "filename": "SettingRepository.php", "line": "23"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select `value`, `key` from `settings` where `key` like ?", "hash": "03db4599941fbfcc5d5ce90af08233c929fd5fe673d9c15cc07ef3d9b7c55995"}, "start_percent": 96.707, "width_percent": 1.836}, {"sql": "select `value`, `key` from `settings` where `key` like '%settings.en.notification%'", "type": "query", "params": [], "bindings": ["%settings.en.notification%"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Repositories/SettingRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SettingRepository.php", "line": 23}, {"index": 14, "namespace": null, "name": "app/Services/Frontend/SettingService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\SettingService.php", "line": 49}, {"index": 15, "namespace": null, "name": "app/Notifications/UpdateProfileEmployer.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Notifications\\UpdateProfileEmployer.php", "line": 75}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/Channels/DatabaseChannel.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Channels\\DatabaseChannel.php", "line": 60}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/Channels/DatabaseChannel.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Channels\\DatabaseChannel.php", "line": 38}], "start": **********.5835102, "duration": 0.00347, "duration_str": "3.47ms", "memory": 0, "memory_str": null, "filename": "SettingRepository.php:23", "source": {"index": 13, "namespace": null, "name": "app/Repositories/SettingRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SettingRepository.php", "line": 23}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSettingRepository.php&line=23", "ajax": false, "filename": "SettingRepository.php", "line": "23"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select `value`, `key` from `settings` where `key` like ?", "hash": "0013672069abff7e5ead156adf6ed0e3cdad8f11cd08bce35f44b435e246d43a"}, "start_percent": 98.543, "width_percent": 1.252}, {"sql": "insert into `notifications` (`id`, `type`, `data`, `read_at`, `notifiable_id`, `notifiable_type`, `updated_at`, `created_at`) values ('9eae7f68-80c6-48e5-8519-3fe2e14dd2c2', 'App\\Notifications\\UpdateProfileEmployer', '{\\\"content_vi\\\":\\\"C\\u1ed9ng t\\u00e1c vi\\u00ean \\u0111\\u00e3 c\\u1eadp nh\\u1eadt h\\u1ed3 s\\u01a1 th\\u00e0nh c\\u00f4ng.\\\",\\\"content_en\\\":\\\"The collaborator has successfully updated the profile.\\\"}', '', 512, 'App\\Models\\User', '2025-08-04 14:31:43', '2025-08-04 14:31:43')", "type": "query", "params": [], "bindings": ["9eae7f68-80c6-48e5-8519-3fe2e14dd2c2", "App\\Notifications\\UpdateProfileEmployer", "{\"content_vi\":\"C\\u1ed9ng t\\u00e1c vi\\u00ean \\u0111\\u00e3 c\\u1eadp nh\\u1eadt h\\u1ed3 s\\u01a1 th\\u00e0nh c\\u00f4ng.\",\"content_en\":\"The collaborator has successfully updated the profile.\"}", null, 512, "App\\Models\\User", "2025-08-04 14:31:43", "2025-08-04 14:31:43"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/Channels/DatabaseChannel.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Channels\\DatabaseChannel.php", "line": 20}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/NotificationSender.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php", "line": 148}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/NotificationSender.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php", "line": 106}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/NotificationSender.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php", "line": 109}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/ChannelManager.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\ChannelManager.php", "line": 54}], "start": **********.589166, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "DatabaseChannel.php:20", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/Channels/DatabaseChannel.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Channels\\DatabaseChannel.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FNotifications%2FChannels%2FDatabaseChannel.php&line=20", "ajax": false, "filename": "DatabaseChannel.php", "line": "20"}, "connection": "hri_recland_product", "explain": null, "start_percent": 99.794, "width_percent": 0.206}]}, "models": {"data": {"App\\Models\\User": {"retrieved": 8, "updated": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\UserInfo": {"retrieved": 2, "updated": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FUserInfo.php&line=1", "ajax": false, "filename": "UserInfo.php", "line": "?"}}, "App\\Models\\JobLog": {"created": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FJobLog.php&line=1", "ajax": false, "filename": "JobLog.php", "line": "?"}}, "Shetabit\\Visitor\\Models\\Visit": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fshetabit%2Fvisitor%2Fsrc%2FModels%2FVisit.php&line=1", "ajax": false, "filename": "Visit.php", "line": "?"}}, "OwenIt\\Auditing\\Models\\Audit": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fowen-it%2Flaravel-auditing%2Fsrc%2FModels%2FAudit.php&line=1", "ajax": false, "filename": "Audit.php", "line": "?"}}, "App\\Models\\EmailLog": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FEmailLog.php&line=1", "ajax": false, "filename": "EmailLog.php", "line": "?"}}, "Illuminate\\Notifications\\DatabaseNotification": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FNotifications%2FDatabaseNotification.php&line=1", "ajax": false, "filename": "DatabaseNotification.php", "line": "?"}}}, "count": 19, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"created": 7, "retrieved": 10, "updated": 2}}, "symfonymailer_mails": {"count": 1, "mails": [{"to": ["<EMAIL>"], "subject": "[Recland] [CẬP NHẬT HỒ SƠ CÁ NHÂN THÀNH CÔNG]", "headers": "From: RECLAND <<EMAIL>>\r\nTo: <EMAIL>\r\nSubject: [Recland] =?utf-8?Q?=5BC=E1=BA=ACP_NH=E1=BA=ACT_H=E1=BB=92?=\r\n =?utf-8?Q?_S=C6=A0_C=C3=81_NH=C3=82N_TH=C3=80NH_C=C3=94NG=5D?=\r\nX-Original-Emails: []\r\n", "body": null, "html": null}]}, "gate": {"count": 0, "messages": []}, "session": {"_token": "67FrIiAKRzqdDDYE9lpf1v63S50ahQba2svcMgmp", "_previous": "array:1 [\n  \"url\" => \"http://recland.local/rec/profile\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => array:1 [\n    0 => \"toastr::messages\"\n  ]\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "login_client_59ba36addc2b2f9401580f014c7f58ea4e30989d": "512", "toastr::messages": "array:1 [\n  0 => array:4 [\n    \"type\" => \"success\"\n    \"title\" => \"Success\"\n    \"message\" => \"<PERSON><PERSON> lưu thành công bản ghi\"\n    \"options\" => []\n  ]\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "302 Found", "full_url": "http://recland.local/rec/update-profile", "action_name": "rec-update-profile", "controller_action": "App\\Http\\Controllers\\Frontend\\RecController@profilePost", "uri": "POST rec/update-profile", "controller": "App\\Http\\Controllers\\Frontend\\RecController@profilePost<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FRecController.php&line=169\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Frontend", "prefix": "/rec", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FRecController.php&line=169\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/RecController.php:169-182</a>", "middleware": "web, localization, visit-website, check-rec", "duration": "1.97s", "peak_memory": "60MB", "response": "Redirect to http://recland.local/rec/profile", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1387335923 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1387335923\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1707529571 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">67FrIiAKRzqdDDYE9lpf1v63S50ahQba2svcMgmp</span>\"\n  \"<span class=sf-dump-key>avatar</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cccd_front_image</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cccd_back_image</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">HAU Nguyen Thi</span>\"\n  \"<span class=sf-dump-key>birthday</span>\" => \"<span class=sf-dump-str title=\"10 characters\">04/02/2000</span>\"\n  \"<span class=sf-dump-key>mobile</span>\" => \"<span class=sf-dump-str title=\"12 characters\">097845634555</span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"24 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>address</span>\" => \"<span class=sf-dump-str title=\"22 characters\">60 D&#432;&#417;ng Khu&#234; M&#7929; &#272;&#236;nh2</span>\"\n  \"<span class=sf-dump-key>cccd_number</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1111111111</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1707529571\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1819589047 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1137</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundary4z7lNZmnzSSuVka7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">http://recland.local/rec/profile</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,vi;q=0.8,nl;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1257 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImwwQXVqYlB5Y2M2YjZoR1Jja2UyZEE9PSIsInZhbHVlIjoiV2RDaEFzN3o1Q2FBK1U0dnZ0U0FaSFphcys2d1FNb2gvMnB0OVFoNWp4UDFxK1JMdmpNS05WTmhCVjFicURnRzMybUg1dXNOQm5NT0l6dmJTeklSNVdVOHFmODVXdzBnNTNzUkFZNnJHNE8xajlyeFdhUVozdW4xUGtqL3k3ODMiLCJtYWMiOiIwMWI2MWIzYWM1MWY2YmM2ZDM4YTczOTNhNTRjMjg5NWM5OWM3OWMxM2Y5MGVjMjEwODljMDUzMzlmMDcxN2MxIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6Ik1MUkVFSzMvS1dJQ3R5bnlYUTZQNWc9PSIsInZhbHVlIjoibWZ3b1VTTUVWclZ1anRUSDFGbWxFUkYvd01jaFEyWUZySjkwSzVCMGFVMzZib3RiUXJobWpmb0FZVlloa1RyRm9EajRQa09DWk1xTmJGVTZXMFBIVnZMMUp2djhzeGZwMCtqbkFsYTJjaGs2VEJCTVFIU0RieDk0QW9rejZaVi8iLCJtYWMiOiI4NTFiMmVlMDk1NGIwNzJjNmM0NTk4ZjcwYmNlMDA0MTkyOTViYjVkNjUyOWMyNjJlMWNiZGRmYWEwMmQwY2Q3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1819589047\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-799606922 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|cm8f4SgFu8wrAd7wPR9b0gsQwQ8YYGlGSWS8Y9hgKQXIsj7qT8VG3KwC3dMx|$2y$10$iTSniY9ReMSnjm6GaX0bRuGWPNsyk9pQat6r8dKHdyoQSgwMkVojS</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">67FrIiAKRzqdDDYE9lpf1v63S50ahQba2svcMgmp</span>\"\n  \"<span class=sf-dump-key>recland_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1VyD9s5I8kaKasZMxJDemZkFPxfq8PRabbTbE7MT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-799606922\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1985959434 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 04 Aug 2025 07:31:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">http://recland.local/rec/profile</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1985959434\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-184147515 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">67FrIiAKRzqdDDYE9lpf1v63S50ahQba2svcMgmp</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">http://recland.local/rec/profile</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">toastr::messages</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>login_client_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>512</span>\n  \"<span class=sf-dump-key>toastr::messages</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n      \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Success</span>\"\n      \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"25 characters\">&#272;&#227; l&#432;u th&#224;nh c&#244;ng b&#7843;n ghi</span>\"\n      \"<span class=sf-dump-key>options</span>\" => []\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-184147515\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "http://recland.local/rec/update-profile", "action_name": "rec-update-profile", "controller_action": "App\\Http\\Controllers\\Frontend\\RecController@profilePost"}, "badge": "302 Found"}}