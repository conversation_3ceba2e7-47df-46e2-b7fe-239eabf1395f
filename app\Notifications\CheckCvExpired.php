<?php

namespace App\Notifications;

use App\Helpers\Common;
use App\Services\Frontend\SettingService;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class CheckCvExpired extends Notification implements ShouldQueue
{
    use Queueable;

    protected $submitCv;
    protected $user;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($submitCv, $user)
    {
        $this->submitCv = $submitCv;
        $this->user = $user;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $name = $this->submitCv->submitCvMeta->candidate_name;
        $userName = $this->user->name;

        return (new MailMessage)
            ->view('email.checkCvExpired', [
                'name' => $name,
                'userName' => $userName,
            ])
            ->subject('[Recland] [THÔNG BÁO CV ĐÃ HẾT HẠN UPDATE TRẠNG THÁI]');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        $settings = resolve(SettingService::class);

        $str = 'notification';

        $name = $this->submitCv->submitCvMeta->candidate_name;

        $arrLangVi = $settings->getAllKeyWithQuery($str, config('constant.language.vi'));
        $arrLangEn = $settings->getAllKeyWithQuery($str, config('constant.language.en'));

        $contentVi = Common::transLang($arrLangVi['ntd_cvcuaungvienquahan'], ['name' => $name]);
        $contentEn = Common::transLang($arrLangEn['ntd_cvcuaungvienquahan'], ['name' => $name]);

        return [
            'event'           => 'updateCv',
            'content_vi'      => $contentVi,
            'content_en'      => $contentEn,
            'submitcvid'      => $this->submitCv->id,
            'routename'       => 'employer-submitcv'
        ];
    }
}
