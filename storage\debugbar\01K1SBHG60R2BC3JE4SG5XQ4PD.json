{"__meta": {"id": "01K1SBHG60R2BC3JE4SG5XQ4PD", "datetime": "2025-08-04 08:36:07", "utime": **********.361738, "method": "POST", "uri": "/job/api/list", "ip": "127.0.0.1"}, "php": {"version": "8.1.6", "interface": "apache2handler"}, "messages": {"count": 35, "messages": [{"message": "[08:36:06] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\\\"skill\\\":null,\\\"lang\\\":\\\"vi\\\"}', 'http:\\/\\/recland.local\\/job\\/api\\/list', 'http:\\/\\/recland.local\\/', '[\\\"en-us\\\",\\\"en\\\",\\\"vi\\\",\\\"nl\\\"]', 'Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********', '{\\\"host\\\":[\\\"recland.local\\\"],\\\"connection\\\":[\\\"keep-alive\\\"],\\\"content-length\\\":[\\\"24\\\"],\\\"x-xsrf-token\\\":[\\\"eyJpdiI6ImRPUWpqTWhPV1hRK0R4d2NyZWNOd1E9PSIsInZhbHVlIjoiY3pKcFVNbEthSlZxVmdsTktJaFdIYkV1bExJMHNINyttaExzQ0tNczE2dkcyVzBsWksyMWZoYWNjZkJpa3FiZFlyWFpBVTdQZmdwb0hFMVd1M092cm9pejYvb1FRV0VJc0U3Rjlra2RsUmJmTEFqSmpqdnRjRitITjdHeDdEMTYiLCJtYWMiOiIzNWUyYjA3YzZhNTNjZTQ1MDE5ZTZlZmNlZDlmMmJkYzUyNjBlZWQ5NGM2NTFiMzg1ODk4MGE4OGI4ZTNkMjczIiwidGFnIjoiIn0=\\\"],\\\"x-requested-with\\\":[\\\"XMLHttpRequest\\\"],\\\"user-agent\\\":[\\\"Mozilla\\\\\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\\\\\/537.36 (KHTML, like Gecko) Chrome\\\\\\/********* Safari\\\\\\/537.36 Edg\\\\\\/*********\\\"],\\\"accept\\\":[\\\"application\\\\\\/json, text\\\\\\/plain, *\\\\\\/*\\\"],\\\"content-type\\\":[\\\"application\\\\\\/json\\\"],\\\"origin\\\":[\\\"http:\\\\\\/\\\\\\/recland.local\\\"],\\\"referer\\\":[\\\"http:\\\\\\/\\\\\\/recland.local\\\\\\/\\\"],\\\"accept-encoding\\\":[\\\"gzip, deflate\\\"],\\\"accept-language\\\":[\\\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\\\"],\\\"cookie\\\":[\\\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImRPUWpqTWhPV1hRK0R4d2NyZWNOd1E9PSIsInZhbHVlIjoiY3pKcFVNbEthSlZxVmdsTktJaFdIYkV1bExJMHNINyttaExzQ0tNczE2dkcyVzBsWksyMWZoYWNjZkJpa3FiZFlyWFpBVTdQZmdwb0hFMVd1M092cm9pejYvb1FRV0VJc0U3Rjlra2RsUmJmTEFqSmpqdnRjRitITjdHeDdEMTYiLCJtYWMiOiIzNWUyYjA3YzZhNTNjZTQ1MDE5ZTZlZmNlZDlmMmJkYzUyNjBlZWQ5NGM2NTFiMzg1ODk4MGE4OGI4ZTNkMjczIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IlROWDZYWmNteUFndTRCdDJueUEzamc9PSIsInZhbHVlIjoiSVMxdU1ha1QybXYzaGNvaHd4QnNvQXZmek1VQWx3S1hVcWk3eCttWFltN0RlQmQrUzE4eUxZaVhTRzlJM044Q1hxbUhJSC9lTlIxMmJFWW44QnpoY2FtOTlJSk1aTXNuYWcxUHpHTS9DN1pONHltUkR4NVZ5eWpRK0pIT1MvQUwiLCJtYWMiOiJlZDI3OWI3NzQ2YjA1MGVhNjczZDdlMGZiYjc4ZDA2ZTE3ZmQ1OTk0NTNiNGQ1YzNlNTY4ODk0NjM1ZjliYWE2IiwidGFnIjoiIn0%3D\\\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-04 08:36:06', '2025-08-04 08:36:06')\\n-- \",\n    \"Time:\": 24.37\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.590892, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:06] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect `job`.*, `job_meta`.`priority` from `job` inner join `job_meta` on `job`.`id` = `job_meta`.`job_id` where `job`.`is_active` = '1' and `job`.`status` = '1' and `job`.`expire_at` >= '2025-08-04' order by FIELD(job_meta.priority, 'Premium', 'Hot', 'New'), `job`.`publish_at` desc limit 30\\n-- \",\n    \"Time:\": 33.54\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.633049, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:06] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `companies` where `companies`.`id` in (3, 187, 217, 274, 277, 278, 286, 487, 491, 694, 695, 696)\\n-- \",\n    \"Time:\": 0.67\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.641301, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:06] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `job_meta` where `job_meta`.`job_id` in (631, 700, 821, 825, 826, 827, 828, 838, 861, 865, 866, 1625, 1626, 1628, 1629, 1630, 1631, 1632, 1633, 1634, 1635, 1636, 1637, 1638, 1639, 1640, 1641, 1642, 1643, 1644)\\n-- \",\n    \"Time:\": 3.13\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.660009, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:06] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect skills from `job` where `skills` is not null and `skills` != '' and `is_active` = '1' and `status` = '1' and `expire_at` >= '2025-08-04' group by `skills` order by COUNT(id) DESC limit 8\\n-- \",\n    \"Time:\": 17.89\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.679718, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:07] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1644' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.72\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.234411, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:07] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1642' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.55\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.248564, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:07] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1643' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.34\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.253629, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:07] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '631' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.38\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.258205, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:07] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1641' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.41\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.262265, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:07] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1640' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.36\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.265935, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:07] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1628' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.27\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.269494, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:07] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1629' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.24\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.273082, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:07] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1630' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.36\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.276615, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:07] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1625' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.33\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.280054, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:07] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1626' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.24\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.283219, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:07] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1631' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.23\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.286545, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:07] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1632' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.22\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.289667, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:07] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1633' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.22\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.292813, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:07] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1634' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.22\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.29594, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:07] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1635' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.22\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.299061, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:07] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1636' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.22\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.302209, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:07] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1637' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.22\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.305538, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:07] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1638' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.26\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.316388, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:07] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1639' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.22\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.319892, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:07] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '700' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.22\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.323225, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:07] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '821' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.22\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.32653, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:07] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '865' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.3\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.3299, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:07] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '866' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.25\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.336875, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:07] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '861' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.22\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.340223, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:07] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '838' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.22\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.343661, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:07] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '825' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.27\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.346833, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:07] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '826' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.25\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.350139, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:07] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '827' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.26\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.353531, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:07] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '828' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.25\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.357184, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 3, "start": **********.059257, "end": **********.361795, "duration": 1.3025379180908203, "duration_str": "1.3s", "measures": [{"label": "Booting", "start": **********.059257, "relative_start": 0, "end": **********.459494, "relative_end": **********.459494, "duration": 0.****************, "duration_str": "400ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.459504, "relative_start": 0.*****************, "end": **********.361797, "relative_end": 2.1457672119140625e-06, "duration": 0.****************, "duration_str": "902ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.472759, "relative_start": 0.****************, "end": **********.479489, "relative_end": **********.479489, "duration": 0.006730079650878906, "duration_str": "6.73ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "61MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "POST job/api/list", "middleware": "web, localization, visit-website", "controller": "App\\Http\\Controllers\\Api\\Frontend\\JobController@list<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FApi%2FFrontend%2FJobController.php&line=23\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Frontend", "as": "job.api.list", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FApi%2FFrontend%2FJobController.php&line=23\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/Frontend/JobController.php:23-43</a>"}, "queries": {"count": 35, "nb_statements": 35, "nb_visible_statements": 35, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.08828999999999998, "accumulated_duration_str": "88.29ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\\\"skill\\\":null,\\\"lang\\\":\\\"vi\\\"}', 'http://recland.local/job/api/list', 'http://recland.local/', '[\\\"en-us\\\",\\\"en\\\",\\\"vi\\\",\\\"nl\\\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\\\"host\\\":[\\\"recland.local\\\"],\\\"connection\\\":[\\\"keep-alive\\\"],\\\"content-length\\\":[\\\"24\\\"],\\\"x-xsrf-token\\\":[\\\"eyJpdiI6ImRPUWpqTWhPV1hRK0R4d2NyZWNOd1E9PSIsInZhbHVlIjoiY3pKcFVNbEthSlZxVmdsTktJaFdIYkV1bExJMHNINyttaExzQ0tNczE2dkcyVzBsWksyMWZoYWNjZkJpa3FiZFlyWFpBVTdQZmdwb0hFMVd1M092cm9pejYvb1FRV0VJc0U3Rjlra2RsUmJmTEFqSmpqdnRjRitITjdHeDdEMTYiLCJtYWMiOiIzNWUyYjA3YzZhNTNjZTQ1MDE5ZTZlZmNlZDlmMmJkYzUyNjBlZWQ5NGM2NTFiMzg1ODk4MGE4OGI4ZTNkMjczIiwidGFnIjoiIn0=\\\"],\\\"x-requested-with\\\":[\\\"XMLHttpRequest\\\"],\\\"user-agent\\\":[\\\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\\\"],\\\"accept\\\":[\\\"application\\/json, text\\/plain, *\\/*\\\"],\\\"content-type\\\":[\\\"application\\/json\\\"],\\\"origin\\\":[\\\"http:\\/\\/recland.local\\\"],\\\"referer\\\":[\\\"http:\\/\\/recland.local\\/\\\"],\\\"accept-encoding\\\":[\\\"gzip, deflate\\\"],\\\"accept-language\\\":[\\\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\\\"],\\\"cookie\\\":[\\\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImRPUWpqTWhPV1hRK0R4d2NyZWNOd1E9PSIsInZhbHVlIjoiY3pKcFVNbEthSlZxVmdsTktJaFdIYkV1bExJMHNINyttaExzQ0tNczE2dkcyVzBsWksyMWZoYWNjZkJpa3FiZFlyWFpBVTdQZmdwb0hFMVd1M092cm9pejYvb1FRV0VJc0U3Rjlra2RsUmJmTEFqSmpqdnRjRitITjdHeDdEMTYiLCJtYWMiOiIzNWUyYjA3YzZhNTNjZTQ1MDE5ZTZlZmNlZDlmMmJkYzUyNjBlZWQ5NGM2NTFiMzg1ODk4MGE4OGI4ZTNkMjczIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IlROWDZYWmNteUFndTRCdDJueUEzamc9PSIsInZhbHVlIjoiSVMxdU1ha1QybXYzaGNvaHd4QnNvQXZmek1VQWx3S1hVcWk3eCttWFltN0RlQmQrUzE4eUxZaVhTRzlJM044Q1hxbUhJSC9lTlIxMmJFWW44QnpoY2FtOTlJSk1aTXNuYWcxUHpHTS9DN1pONHltUkR4NVZ5eWpRK0pIT1MvQUwiLCJtYWMiOiJlZDI3OWI3NzQ2YjA1MGVhNjczZDdlMGZiYjc4ZDA2ZTE3ZmQ1OTk0NTNiNGQ1YzNlNTY4ODk0NjM1ZjliYWE2IiwidGFnIjoiIn0%3D\\\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-04 08:36:06', '2025-08-04 08:36:06')", "type": "query", "params": [], "bindings": ["POST", "{\"skill\":null,\"lang\":\"vi\"}", "http://recland.local/job/api/list", "http://recland.local/", "[\"en-us\",\"en\",\"vi\",\"nl\"]", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"24\"],\"x-xsrf-token\":[\"eyJpdiI6ImRPUWpqTWhPV1hRK0R4d2NyZWNOd1E9PSIsInZhbHVlIjoiY3pKcFVNbEthSlZxVmdsTktJaFdIYkV1bExJMHNINyttaExzQ0tNczE2dkcyVzBsWksyMWZoYWNjZkJpa3FiZFlyWFpBVTdQZmdwb0hFMVd1M092cm9pejYvb1FRV0VJc0U3Rjlra2RsUmJmTEFqSmpqdnRjRitITjdHeDdEMTYiLCJtYWMiOiIzNWUyYjA3YzZhNTNjZTQ1MDE5ZTZlZmNlZDlmMmJkYzUyNjBlZWQ5NGM2NTFiMzg1ODk4MGE4OGI4ZTNkMjczIiwidGFnIjoiIn0=\"],\"x-requested-with\":[\"XMLHttpRequest\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"application\\/json, text\\/plain, *\\/*\"],\"content-type\":[\"application\\/json\"],\"origin\":[\"http:\\/\\/recland.local\"],\"referer\":[\"http:\\/\\/recland.local\\/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImRPUWpqTWhPV1hRK0R4d2NyZWNOd1E9PSIsInZhbHVlIjoiY3pKcFVNbEthSlZxVmdsTktJaFdIYkV1bExJMHNINyttaExzQ0tNczE2dkcyVzBsWksyMWZoYWNjZkJpa3FiZFlyWFpBVTdQZmdwb0hFMVd1M092cm9pejYvb1FRV0VJc0U3Rjlra2RsUmJmTEFqSmpqdnRjRitITjdHeDdEMTYiLCJtYWMiOiIzNWUyYjA3YzZhNTNjZTQ1MDE5ZTZlZmNlZDlmMmJkYzUyNjBlZWQ5NGM2NTFiMzg1ODk4MGE4OGI4ZTNkMjczIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IlROWDZYWmNteUFndTRCdDJueUEzamc9PSIsInZhbHVlIjoiSVMxdU1ha1QybXYzaGNvaHd4QnNvQXZmek1VQWx3S1hVcWk3eCttWFltN0RlQmQrUzE4eUxZaVhTRzlJM044Q1hxbUhJSC9lTlIxMmJFWW44QnpoY2FtOTlJSk1aTXNuYWcxUHpHTS9DN1pONHltUkR4NVZ5eWpRK0pIT1MvQUwiLCJtYWMiOiJlZDI3OWI3NzQ2YjA1MGVhNjczZDdlMGZiYjc4ZDA2ZTE3ZmQ1OTk0NTNiNGQ1YzNlNTY4ODk0NjM1ZjliYWE2IiwidGFnIjoiIn0%3D\"]}", "WebKit", "Windows", "Edge", "127.0.0.1", null, null, "2025-08-04 08:36:06", "2025-08-04 08:36:06"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/shetabit/visitor/src/Visitor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\shetabit\\visitor\\src\\Visitor.php", "line": 245}, {"index": 22, "namespace": "middleware", "name": "visit-website", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\VisitWebsiteMiddleware.php", "line": 20}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 24, "namespace": "middleware", "name": "localization", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\Localization.php", "line": 24}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.5668561, "duration": 0.02437, "duration_str": "24.37ms", "memory": 0, "memory_str": null, "filename": "Visitor.php:245", "source": {"index": 21, "namespace": null, "name": "vendor/shetabit/visitor/src/Visitor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\shetabit\\visitor\\src\\Visitor.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fshetabit%2Fvisitor%2Fsrc%2FVisitor.php&line=245", "ajax": false, "filename": "Visitor.php", "line": "245"}, "connection": "hri_recland_product", "explain": null, "start_percent": 0, "width_percent": 27.602}, {"sql": "select `job`.*, `job_meta`.`priority` from `job` inner join `job_meta` on `job`.`id` = `job_meta`.`job_id` where `job`.`is_active` = 1 and `job`.`status` = 1 and `job`.`expire_at` >= '2025-08-04' order by FIELD(job_meta.priority, 'Premium', 'Hot', 'New'), `job`.`publish_at` desc limit 30", "type": "query", "params": [], "bindings": [1, 1, "2025-08-04"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/JobRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobRepository.php", "line": 162}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/JobService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\JobService.php", "line": 70}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/Frontend/JobController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Api\\Frontend\\JobController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.599586, "duration": 0.03354, "duration_str": "33.54ms", "memory": 0, "memory_str": null, "filename": "JobRepository.php:162", "source": {"index": 14, "namespace": null, "name": "app/Repositories/JobRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobRepository.php", "line": 162}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FJobRepository.php&line=162", "ajax": false, "filename": "JobRepository.php", "line": "162"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select `job`.*, `job_meta`.`priority` from `job` inner join `job_meta` on `job`.`id` = `job_meta`.`job_id` where `job`.`is_active` = ? and `job`.`status` = ? and `job`.`expire_at` >= ? order by FIELD(job_meta.priority, 'Premium', 'Hot', 'New'), `job`.`publish_at` desc limit 30", "hash": "caea57efbd43c977fa2dd1d63bccf96e2648e17342038477e8abaec10ae0c7f5"}, "start_percent": 27.602, "width_percent": 37.988}, {"sql": "select * from `companies` where `companies`.`id` in (3, 187, 217, 274, 277, 278, 286, 487, 491, 694, 695, 696)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Repositories/JobRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobRepository.php", "line": 162}, {"index": 20, "namespace": null, "name": "app/Services/Frontend/JobService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\JobService.php", "line": 70}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Api/Frontend/JobController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Api\\Frontend\\JobController.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.640697, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "JobRepository.php:162", "source": {"index": 19, "namespace": null, "name": "app/Repositories/JobRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobRepository.php", "line": 162}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FJobRepository.php&line=162", "ajax": false, "filename": "JobRepository.php", "line": "162"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `companies` where `companies`.`id` in (3, 187, 217, 274, 277, 278, 286, 487, 491, 694, 695, 696)", "hash": "44f8fe2f3b72f6f9f67b2dc8d43453fd81097815acf47438ae5801a3c21a14c7"}, "start_percent": 65.591, "width_percent": 0.759}, {"sql": "select * from `job_meta` where `job_meta`.`job_id` in (631, 700, 821, 825, 826, 827, 828, 838, 861, 865, 866, 1625, 1626, 1628, 1629, 1630, 1631, 1632, 1633, 1634, 1635, 1636, 1637, 1638, 1639, 1640, 1641, 1642, 1643, 1644)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Repositories/JobRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobRepository.php", "line": 162}, {"index": 20, "namespace": null, "name": "app/Services/Frontend/JobService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\JobService.php", "line": 70}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Api/Frontend/JobController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Api\\Frontend\\JobController.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.656945, "duration": 0.00313, "duration_str": "3.13ms", "memory": 0, "memory_str": null, "filename": "JobRepository.php:162", "source": {"index": 19, "namespace": null, "name": "app/Repositories/JobRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobRepository.php", "line": 162}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FJobRepository.php&line=162", "ajax": false, "filename": "JobRepository.php", "line": "162"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `job_meta` where `job_meta`.`job_id` in (631, 700, 821, 825, 826, 827, 828, 838, 861, 865, 866, 1625, 1626, 1628, 1629, 1630, 1631, 1632, 1633, 1634, 1635, 1636, 1637, 1638, 1639, 1640, 1641, 1642, 1643, 1644)", "hash": "16e7e17e2c2be170c1a6903f9e06ccc39afe588d2175eb829100c55af2b3a299"}, "start_percent": 66.35, "width_percent": 3.545}, {"sql": "select skills from `job` where `skills` is not null and `skills` != '' and `is_active` = 1 and `status` = 1 and `expire_at` >= '2025-08-04' group by `skills` order by COUNT(id) DESC limit 8", "type": "query", "params": [], "bindings": ["", 1, 1, "2025-08-04"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/JobRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobRepository.php", "line": 441}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/JobService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\JobService.php", "line": 102}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/Frontend/JobController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Api\\Frontend\\JobController.php", "line": 29}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.661894, "duration": 0.01789, "duration_str": "17.89ms", "memory": 0, "memory_str": null, "filename": "JobRepository.php:441", "source": {"index": 14, "namespace": null, "name": "app/Repositories/JobRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobRepository.php", "line": 441}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FJobRepository.php&line=441", "ajax": false, "filename": "JobRepository.php", "line": "441"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select skills from `job` where `skills` is not null and `skills` != ? and `is_active` = ? and `status` = ? and `expire_at` >= ? group by `skills` order by COUNT(id) DESC limit 8", "hash": "aa784896b7ff280b497e3447e4d1ae8dd5b3defe8d4a32514c8b165641bc0607"}, "start_percent": 69.895, "width_percent": 20.263}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 1644 and `type` = 'save'", "type": "query", "params": [], "bindings": [1644, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.234231, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "37cb223e001eb4ebe6d4ad977e897ea212b5d7b37634d3dbf61029cace96ad3d"}, "start_percent": 90.157, "width_percent": 0.815}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 1642 and `type` = 'save'", "type": "query", "params": [], "bindings": [1642, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.248168, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "21f4845ad0691e86083cadbcb755f57cf848010de1485b9b59aa23c8989fceca"}, "start_percent": 90.973, "width_percent": 0.623}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 1643 and `type` = 'save'", "type": "query", "params": [], "bindings": [1643, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.25336, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "a5cdfcd2bfa92b2c59d701bc37b0e7d1450a316a4dba557cc8d45866c1e63b8b"}, "start_percent": 91.596, "width_percent": 0.385}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 631 and `type` = 'save'", "type": "query", "params": [], "bindings": [631, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.257896, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "8d2643fe6adc6a1a3f7851c3611e56fee43fdad48f936edbf94a49089cc45fd8"}, "start_percent": 91.981, "width_percent": 0.43}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 1641 and `type` = 'save'", "type": "query", "params": [], "bindings": [1641, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.261925, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "537142b2bc3e0e85005a5f6c34428524c33bc2909b81add3628e491c9aff481a"}, "start_percent": 92.411, "width_percent": 0.464}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 1640 and `type` = 'save'", "type": "query", "params": [], "bindings": [1640, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.265645, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "824fc96749fc72aa999f4e8f3afc49b43af61ca96062cf6d60fe4fb24a20d5e3"}, "start_percent": 92.876, "width_percent": 0.408}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 1628 and `type` = 'save'", "type": "query", "params": [], "bindings": [1628, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.2692962, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "6da48ab3765623ef048e264b6a92ccfff2a10535ea21a758746e16b0cadf64c0"}, "start_percent": 93.283, "width_percent": 0.306}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 1629 and `type` = 'save'", "type": "query", "params": [], "bindings": [1629, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.272913, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "753c75f1055374341057d59c1957f7061fd00ac61aff62f3e647ce3b41761cc6"}, "start_percent": 93.589, "width_percent": 0.272}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 1630 and `type` = 'save'", "type": "query", "params": [], "bindings": [1630, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.276325, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "7718b159aab3871f9a3ce48d8e15f5097b3c370d3b5241a9a2c51c5a9b9310e4"}, "start_percent": 93.861, "width_percent": 0.408}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 1625 and `type` = 'save'", "type": "query", "params": [], "bindings": [1625, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.279794, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "bb3166f0b9ba1b321b3ca659d8b11c9d3062d27073c9dcd46805c2f9ecbfe6f5"}, "start_percent": 94.269, "width_percent": 0.374}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 1626 and `type` = 'save'", "type": "query", "params": [], "bindings": [1626, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.2830489, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "776d3fc8ff139b31651e2c72ba7dcfcd5dc61352143d30265ef0aa36b14c5e6b"}, "start_percent": 94.643, "width_percent": 0.272}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 1631 and `type` = 'save'", "type": "query", "params": [], "bindings": [1631, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.286386, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "29f5e2bd02f60d76899c648d67a00c7942a602c632bc35571cb68e6319b5a2d5"}, "start_percent": 94.914, "width_percent": 0.261}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 1632 and `type` = 'save'", "type": "query", "params": [], "bindings": [1632, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.289517, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "8cf503455e4a3bc67c23b18545bf5c5d839f3b6f745868d738eea3281e6972d1"}, "start_percent": 95.175, "width_percent": 0.249}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 1633 and `type` = 'save'", "type": "query", "params": [], "bindings": [1633, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.292662, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "f53909da87944c4bccceb4514df4b12d7450f48818525bb234c881e686d926f1"}, "start_percent": 95.424, "width_percent": 0.249}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 1634 and `type` = 'save'", "type": "query", "params": [], "bindings": [1634, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.29579, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "ac9d85b3ccd2b70f3f40fc012ca469b2aa350cc44084c95904e8dedd1f44f40b"}, "start_percent": 95.673, "width_percent": 0.249}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 1635 and `type` = 'save'", "type": "query", "params": [], "bindings": [1635, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.2989109, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "ae778ce1e64a8504e4e45c8a0e086c7c65c93365c83351eb931325aef17ea84b"}, "start_percent": 95.923, "width_percent": 0.249}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 1636 and `type` = 'save'", "type": "query", "params": [], "bindings": [1636, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.302059, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "d5a073afac8d140e114e17e1510c3c5d8e7b1e91971727d9401d3a91ca13ffa3"}, "start_percent": 96.172, "width_percent": 0.249}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 1637 and `type` = 'save'", "type": "query", "params": [], "bindings": [1637, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.305388, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "61282e98af0a8a9b64ba532b430600037ffbd930661e51934e5881c0272d2d00"}, "start_percent": 96.421, "width_percent": 0.249}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 1638 and `type` = 'save'", "type": "query", "params": [], "bindings": [1638, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.3162048, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "b9825853d887a3e7a4ad176a0f6a65d25e7c788e5433e31eb0a534f86634a217"}, "start_percent": 96.67, "width_percent": 0.294}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 1639 and `type` = 'save'", "type": "query", "params": [], "bindings": [1639, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.319742, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "b6aa98ffb9b9aaedf3b7e50a905978b48cf854b7e071a43a93d15645dcc86e54"}, "start_percent": 96.965, "width_percent": 0.249}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 700 and `type` = 'save'", "type": "query", "params": [], "bindings": [700, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.323075, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "a4a7688f4d2a14b474eed8d7053ed00e4eba2b8277ef2c32fedb562a44087eeb"}, "start_percent": 97.214, "width_percent": 0.249}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 821 and `type` = 'save'", "type": "query", "params": [], "bindings": [821, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.326381, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "c755463b56113549e67ed05c69971f07d9dfba0da756ac8fad979e9f529536dd"}, "start_percent": 97.463, "width_percent": 0.249}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 865 and `type` = 'save'", "type": "query", "params": [], "bindings": [865, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.329765, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "c3ed1104d52112e67f2b97a2c763b016ae8647ece13ca14d2ca02ec050f3d6d2"}, "start_percent": 97.712, "width_percent": 0.34}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 866 and `type` = 'save'", "type": "query", "params": [], "bindings": [866, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.336696, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "a314d92e438e7ee2ca9f06b6780e6c89b1a6199c0fcfdbf517387a3b85fd4138"}, "start_percent": 98.052, "width_percent": 0.283}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 861 and `type` = 'save'", "type": "query", "params": [], "bindings": [861, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.3400729, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "0a2d0da7220072e31cb364aadc1bc6f0c60a76719a8df740d5f5f1e291607bb1"}, "start_percent": 98.335, "width_percent": 0.249}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 838 and `type` = 'save'", "type": "query", "params": [], "bindings": [838, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.343512, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "d23eba12ea035e9077f71f7b56575c0c770e6ac1d830c7f4d7b657dc17ab9d84"}, "start_percent": 98.584, "width_percent": 0.249}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 825 and `type` = 'save'", "type": "query", "params": [], "bindings": [825, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.346635, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "e98c863d6ec1cb07b51e0b7988e1b292df9800b92eb2997f96609454357d38f7"}, "start_percent": 98.833, "width_percent": 0.306}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 826 and `type` = 'save'", "type": "query", "params": [], "bindings": [826, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.3499599, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "89fb8cf06a2c1a37653300034eb51e2fcda8537fd65f192bdcf91bed04537427"}, "start_percent": 99.139, "width_percent": 0.283}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 827 and `type` = 'save'", "type": "query", "params": [], "bindings": [827, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.3533409, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "f63424fe268ecad4507b5e91ada88aec8f12067ffe9691b586932fb2490a0f40"}, "start_percent": 99.422, "width_percent": 0.294}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 828 and `type` = 'save'", "type": "query", "params": [], "bindings": [828, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.357005, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "55ac752ae3140a9f7622826834578e6372da9581a62423ce29db221712d9fbad"}, "start_percent": 99.717, "width_percent": 0.283}]}, "models": {"data": {"App\\Models\\Job": {"retrieved": 38, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FJob.php&line=1", "ajax": false, "filename": "Job.php", "line": "?"}}, "App\\Models\\JobMeta": {"retrieved": 30, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FJobMeta.php&line=1", "ajax": false, "filename": "JobMeta.php", "line": "?"}}, "App\\Models\\Company": {"retrieved": 12, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FCompany.php&line=1", "ajax": false, "filename": "Company.php", "line": "?"}}, "Shetabit\\Visitor\\Models\\Visit": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fshetabit%2Fvisitor%2Fsrc%2FModels%2FVisit.php&line=1", "ajax": false, "filename": "Visit.php", "line": "?"}}}, "count": 81, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"created": 1, "retrieved": 80}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "67FrIiAKRzqdDDYE9lpf1v63S50ahQba2svcMgmp", "PHPDEBUGBAR_STACK_DATA": "[]", "_previous": "array:1 [\n  \"url\" => \"http://recland.local\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"data": {"status": "200 OK", "full_url": "http://recland.local/job/api/list", "action_name": "job.api.list", "controller_action": "App\\Http\\Controllers\\Api\\Frontend\\JobController@list", "uri": "POST job/api/list", "controller": "App\\Http\\Controllers\\Api\\Frontend\\JobController@list<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FApi%2FFrontend%2FJobController.php&line=23\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Frontend", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FApi%2FFrontend%2FJobController.php&line=23\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/Frontend/JobController.php:23-43</a>", "middleware": "web, localization, visit-website", "duration": "1.31s", "peak_memory": "64MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-548171819 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-548171819\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1319798181 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>skill</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">vi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1319798181\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">24</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImRPUWpqTWhPV1hRK0R4d2NyZWNOd1E9PSIsInZhbHVlIjoiY3pKcFVNbEthSlZxVmdsTktJaFdIYkV1bExJMHNINyttaExzQ0tNczE2dkcyVzBsWksyMWZoYWNjZkJpa3FiZFlyWFpBVTdQZmdwb0hFMVd1M092cm9pejYvb1FRV0VJc0U3Rjlra2RsUmJmTEFqSmpqdnRjRitITjdHeDdEMTYiLCJtYWMiOiIzNWUyYjA3YzZhNTNjZTQ1MDE5ZTZlZmNlZDlmMmJkYzUyNjBlZWQ5NGM2NTFiMzg1ODk4MGE4OGI4ZTNkMjczIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://recland.local/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,vi;q=0.8,nl;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1257 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImRPUWpqTWhPV1hRK0R4d2NyZWNOd1E9PSIsInZhbHVlIjoiY3pKcFVNbEthSlZxVmdsTktJaFdIYkV1bExJMHNINyttaExzQ0tNczE2dkcyVzBsWksyMWZoYWNjZkJpa3FiZFlyWFpBVTdQZmdwb0hFMVd1M092cm9pejYvb1FRV0VJc0U3Rjlra2RsUmJmTEFqSmpqdnRjRitITjdHeDdEMTYiLCJtYWMiOiIzNWUyYjA3YzZhNTNjZTQ1MDE5ZTZlZmNlZDlmMmJkYzUyNjBlZWQ5NGM2NTFiMzg1ODk4MGE4OGI4ZTNkMjczIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IlROWDZYWmNteUFndTRCdDJueUEzamc9PSIsInZhbHVlIjoiSVMxdU1ha1QybXYzaGNvaHd4QnNvQXZmek1VQWx3S1hVcWk3eCttWFltN0RlQmQrUzE4eUxZaVhTRzlJM044Q1hxbUhJSC9lTlIxMmJFWW44QnpoY2FtOTlJSk1aTXNuYWcxUHpHTS9DN1pONHltUkR4NVZ5eWpRK0pIT1MvQUwiLCJtYWMiOiJlZDI3OWI3NzQ2YjA1MGVhNjczZDdlMGZiYjc4ZDA2ZTE3ZmQ1OTk0NTNiNGQ1YzNlNTY4ODk0NjM1ZjliYWE2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-*********7 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|cm8f4SgFu8wrAd7wPR9b0gsQwQ8YYGlGSWS8Y9hgKQXIsj7qT8VG3KwC3dMx|$2y$10$iTSniY9ReMSnjm6GaX0bRuGWPNsyk9pQat6r8dKHdyoQSgwMkVojS</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">67FrIiAKRzqdDDYE9lpf1v63S50ahQba2svcMgmp</span>\"\n  \"<span class=sf-dump-key>recland_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">v1riCItHM0lP1ly6DcovIBSGnsbUL8yYQ8j9OzIc</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********7\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1781014049 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 04 Aug 2025 01:36:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1781014049\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-506362148 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">67FrIiAKRzqdDDYE9lpf1v63S50ahQba2svcMgmp</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-506362148\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://recland.local/job/api/list", "action_name": "job.api.list", "controller_action": "App\\Http\\Controllers\\Api\\Frontend\\JobController@list"}, "badge": null}}