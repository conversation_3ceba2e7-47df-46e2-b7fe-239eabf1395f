# Changelog

## [1.6.6](https://github.com/Lara<PERSON>-Backpack/PRO/tree/1.6.6) (07-Jun-2023)

- fix date options key [\#162](https://github.com/Laravel-Backpack/PRO/pull/162) (@pxpm)

## [1.6.5](https://github.com/Laravel-Backpack/PRO/tree/1.6.5) (05-Jun-2023)

- \[BUG\] date picker language config key has wrong name [\#160](https://github.com/Laravel-Backpack/PRO/pull/160) (@pxpm)
- Fix select2 unselect behaviour. [\#148](https://github.com/Laravel-Backpack/PRO/pull/148) (@karandatwani92)
- BulkTrash Fixes [\#127](https://github.com/Laravel-Backpack/PRO/pull/127) (@karandatwani92)

- branch update [\#147](https://github.com/Laravel-Backpack/PRO/pull/147) (@karandatwani92)

## [1.6.4](https://github.com/Laravel-Backpack/PRO/tree/1.6.4) (03-Apr-2023)

- fix pagination on ajax filter [\#114](https://github.com/Laravel-Backpack/PRO/pull/114) (@pxpm)

- WIP image should send back the db value instead of the display url [\#139](https://github.com/Laravel-Backpack/PRO/pull/139) (@pxpm)

## [1.6.3](https://github.com/Laravel-Backpack/PRO/tree/1.6.3) (11-Mar-2023)

- fix phone field populating prefix with US when not doing geoIP lookup [\#137](https://github.com/Laravel-Backpack/PRO/pull/137) (@tabacitu)

## [1.6.2](https://github.com/Laravel-Backpack/PRO/tree/1.6.2) (03-Mar-2023)

- add path property to AutomaticServiceProvider [\#136](https://github.com/Laravel-Backpack/PRO/pull/136) (@tabacitu)

## [1.6.1](https://github.com/Laravel-Backpack/PRO/tree/1.6.1) (28-Feb-2023)

- fix phone field using geoIP when editing [\#135](https://github.com/Laravel-Backpack/PRO/pull/135) (@tabacitu)

## [1.6.0](https://github.com/Laravel-Backpack/PRO/tree/1.6.0) (31-Jan-2023)

- \[Enhancement\] Inline create modal focuses on first focusable input [\#65](https://github.com/Laravel-Backpack/PRO/pull/65) (@maurohmartinez)

- review changes [\#121](https://github.com/Laravel-Backpack/PRO/pull/121) (@pxpm)
- Trash Operations for CRUD  [\#96](https://github.com/Laravel-Backpack/PRO/pull/96) (@karandatwani92)

## [1.5.0](https://github.com/Laravel-Backpack/PRO/tree/1.5.0) (27-Dec-2022)

- Fix address\_google field loading of google api script  [\#116](https://github.com/Laravel-Backpack/PRO/pull/116) (@pxpm)
- add google\_map field type [\#98](https://github.com/Laravel-Backpack/PRO/pull/98) (@pxpm)

## [1.4.2](https://github.com/Laravel-Backpack/PRO/tree/1.4.2) (20-Dec-2022)

- add options per color field [\#115](https://github.com/Laravel-Backpack/PRO/pull/115) (@pxpm)

## [1.4.1](https://github.com/Laravel-Backpack/PRO/tree/1.4.1) (13-Dec-2022)

- Fix phone field css [\#111](https://github.com/Laravel-Backpack/PRO/pull/111) (@jorgetwgroup)

## [1.4.0](https://github.com/Laravel-Backpack/PRO/tree/1.4.0) (30-Nov-2022)

- Phone changes [\#109](https://github.com/Laravel-Backpack/PRO/pull/109) (@pxpm)
- Phone Field [\#103](https://github.com/Laravel-Backpack/PRO/pull/103) (@jorgetwgroup)

## [1.3.2](https://github.com/Laravel-Backpack/PRO/tree/1.3.2) (28-Nov-2022)

- Fix the usage of JS API inside InlineCreate [\#90](https://github.com/Laravel-Backpack/PRO/pull/90) (@pxpm)

- Updated service provider [\#107](https://github.com/Laravel-Backpack/PRO/pull/107) (@promatik)
- Exclude issues in changelog generation [\#105](https://github.com/Laravel-Backpack/PRO/pull/105) (@phpfour)
- Focus on filter inputs when filter open [\#101](https://github.com/Laravel-Backpack/PRO/pull/101) (@pxpm)

## [1.3.1](https://github.com/Laravel-Backpack/PRO/tree/1.3.1) (27-Oct-2022)

- \[hot fix\]Fix morph fields when using all of the same type. [\#97](https://github.com/Laravel-Backpack/PRO/pull/97) (@pxpm)

- fix the function name [\#102](https://github.com/Laravel-Backpack/PRO/pull/102) (@pxpm)
- add table show/hide properties to elements [\#94](https://github.com/Laravel-Backpack/PRO/pull/94) (@pxpm)

## [1.3.0](https://github.com/Laravel-Backpack/PRO/tree/1.3.0) (14-Oct-2022)

- bump CRUD requirement to 5.4.0 [\#95](https://github.com/Laravel-Backpack/PRO/pull/95) (@tabacitu)
- Add default locale To `defaultOptions`  [\#93](https://github.com/Laravel-Backpack/PRO/pull/93) (@EGYWEB-Mohamed)
- add morphTo relation support [\#18](https://github.com/Laravel-Backpack/PRO/pull/18) (@pxpm)

## [1.2.8](https://github.com/Laravel-Backpack/PRO/tree/1.2.8) (08-Sep-2022)

- Update workflow for Changelog generation [\#88](https://github.com/Laravel-Backpack/PRO/pull/88) (@phpfour)

## [1.2.7](https://github.com/Laravel-Backpack/PRO/tree/1.2.7) (31-Aug-2022)

- require CRUD 5.3.7 [\#87](https://github.com/Laravel-Backpack/PRO/pull/87) (@tabacitu)

## [1.2.6](https://github.com/Laravel-Backpack/PRO/tree/1.2.6) (31-Aug-2022)

- Allow repeatable to use Laravel Collections too [\#84](https://github.com/Laravel-Backpack/PRO/pull/84) (@pxpm)

- use the same fetch convention whe [\#85](https://github.com/Laravel-Backpack/PRO/pull/85) (@pxpm)
- use ViewNamespaces class to load views [\#73](https://github.com/Laravel-Backpack/PRO/pull/73) (@pxpm)

## [1.2.5](https://github.com/Laravel-Backpack/PRO/tree/1.2.5) (19-Aug-2022)

## [1.2.4](https://github.com/Laravel-Backpack/PRO/tree/1.2.4) (19-Aug-2022)

- Delete changelog.md [\#83](https://github.com/Laravel-Backpack/PRO/pull/83) (@tabacitu)

## [1.2.3](https://github.com/Laravel-Backpack/PRO/tree/1.2.3) (19-Aug-2022)

- move scripts stack higher in file hierarchy for InlineCreate [\#79](https://github.com/Laravel-Backpack/PRO/pull/79) (@pxpm)

- Create releases.yml [\#82](https://github.com/Laravel-Backpack/PRO/pull/82) (@tabacitu)

## [1.2.2](https://github.com/Laravel-Backpack/PRO/tree/1.2.2) (15-Aug-2022)

- \[Feature\] Repeatable field focuses on first focusable input [\#64](https://github.com/Laravel-Backpack/PRO/pull/64) (@maurohmartinez)

- Fix for repeatable field when using with a fake translatable [\#76](https://github.com/Laravel-Backpack/PRO/pull/76) (@promatik)

- CrudFields - Subfields callbacks on the fly [\#80](https://github.com/Laravel-Backpack/PRO/pull/80) (@promatik)
- Replace HTML comments with Blade comments [\#78](https://github.com/Laravel-Backpack/PRO/pull/78) (@promatik)
- FetchOperation to use simplePaginate to avoid counting query [\#77](https://github.com/Laravel-Backpack/PRO/pull/77) (@pxpm)

## [1.2.1](https://github.com/Laravel-Backpack/PRO/tree/1.2.1) (23-Jul-2022)

- fix pivot select option disable [\#74](https://github.com/Laravel-Backpack/PRO/pull/74) (@pxpm)

## [1.2.0](https://github.com/Laravel-Backpack/PRO/tree/1.2.0) (19-Jul-2022)

- fix inline create not applying operation callback configuration [\#72](https://github.com/Laravel-Backpack/PRO/pull/72) (@pxpm)

- don't mess with configs, use viewNamespaces as a crud setting [\#68](https://github.com/Laravel-Backpack/PRO/pull/68) (@pxpm)

## [1.1.4](https://github.com/Laravel-Backpack/PRO/tree/1.1.4) (14-Jul-2022)

- add placeholder to select2 fields [\#58](https://github.com/Laravel-Backpack/PRO/pull/58) (@pxpm)

- Fix repeatable fields on `InlineCreate` modals [\#69](https://github.com/Laravel-Backpack/PRO/pull/69) (@pxpm)
- add check for input presence before applying change event [\#63](https://github.com/Laravel-Backpack/PRO/pull/63) (@pxpm)

- Select2 Grouped and Nested clear and placeholder  [\#66](https://github.com/Laravel-Backpack/PRO/pull/66) (@promatik)
- JS Fields - Allow multiple subfield callbacks [\#59](https://github.com/Laravel-Backpack/PRO/pull/59) (@promatik)

## [1.1.3](https://github.com/Laravel-Backpack/PRO/tree/1.1.3) (29-Jun-2022)

- \[Bug\] Fix select\_and\_order subfield in repeatable fields [\#61](https://github.com/Laravel-Backpack/PRO/pull/61) (@maurohmartinez)
- \[Bug\] Fix pagination for ajax fields and filter [\#60](https://github.com/Laravel-Backpack/PRO/pull/60) (@maurohmartinez)

## [1.1.2](https://github.com/Laravel-Backpack/PRO/tree/1.1.2) (17-Jun-2022)

- fix the input event on repeatable [\#56](https://github.com/Laravel-Backpack/PRO/pull/56) (@pxpm)
- select\_and\_order wrap the value as string [\#55](https://github.com/Laravel-Backpack/PRO/pull/55) (@pxpm)

- Inline create independant translations [\#54](https://github.com/Laravel-Backpack/PRO/pull/54) (@pxpm)

## [1.1.1](https://github.com/Laravel-Backpack/PRO/tree/1.1.1) (10-Jun-2022)

- add input identifier to repeatable container [\#52](https://github.com/Laravel-Backpack/PRO/pull/52) (@pxpm)

## [1.1.0](https://github.com/Laravel-Backpack/PRO/tree/1.1.0) (10-Jun-2022)

- Properly parse container name [\#51](https://github.com/Laravel-Backpack/PRO/pull/51) (@pxpm)
- Select and order fixes [\#48](https://github.com/Laravel-Backpack/PRO/pull/48) (@pxpm)

- Rename js events [\#50](https://github.com/Laravel-Backpack/PRO/pull/50) (@pxpm)
- small fixes for js api [\#49](https://github.com/Laravel-Backpack/PRO/pull/49) (@pxpm)
- subfields should trigger change dynamically too [\#47](https://github.com/Laravel-Backpack/PRO/pull/47) (@pxpm)
- Add `slug` field to PRO [\#46](https://github.com/Laravel-Backpack/PRO/pull/46) (@tabacitu)
- Refactor repeatable selectors [\#45](https://github.com/Laravel-Backpack/PRO/pull/45) (@pxpm)
- add support for repeatable field [\#43](https://github.com/Laravel-Backpack/PRO/pull/43) (@pxpm)
- fix for date\_range fileld to work with js api [\#40](https://github.com/Laravel-Backpack/PRO/pull/40) (@pxpm)
- Support subfields in js api [\#38](https://github.com/Laravel-Backpack/PRO/pull/38) (@pxpm)
- Multiple fixes for js api [\#37](https://github.com/Laravel-Backpack/PRO/pull/37) (@pxpm)
- fix images events [\#36](https://github.com/Laravel-Backpack/PRO/pull/36) (@pxpm)
- add support for browse and browse multiple in js api [\#30](https://github.com/Laravel-Backpack/PRO/pull/30) (@pxpm)
- refator backpack field event names [\#29](https://github.com/Laravel-Backpack/PRO/pull/29) (@pxpm)
- fix tinymce change event duplication [\#28](https://github.com/Laravel-Backpack/PRO/pull/28) (@pxpm)
- Images fields fixes [\#26](https://github.com/Laravel-Backpack/PRO/pull/26) (@pxpm)
- fix icon picker to work with js api [\#25](https://github.com/Laravel-Backpack/PRO/pull/25) (@pxpm)
- Enable disable rich editors [\#23](https://github.com/Laravel-Backpack/PRO/pull/23) (@pxpm)
- crud.fields\(\) JS API for PRO fields [\#22](https://github.com/Laravel-Backpack/PRO/pull/22) (@tabacitu)
- make wysiwyg editors and color picker work [\#21](https://github.com/Laravel-Backpack/PRO/pull/21) (@pxpm)

## [1.0.19](https://github.com/Laravel-Backpack/PRO/tree/1.0.19) (02-Jun-2022)

- use blade directives for asset loading, fix parent loaded assets key [\#42](https://github.com/Laravel-Backpack/PRO/pull/42) (@pxpm)

- Revert "Merge pull request \#7 from DigitallyHappy/remove-width-inline… [\#44](https://github.com/Laravel-Backpack/PRO/pull/44) (@pxpm)

## [1.0.18](https://github.com/Laravel-Backpack/PRO/tree/1.0.18) (24-May-2022)

- add multiple brakets in multiple inputs [\#41](https://github.com/Laravel-Backpack/PRO/pull/41) (@pxpm)
- always ensure locale is set on datepicker fields [\#39](https://github.com/Laravel-Backpack/PRO/pull/39) (@pxpm)

## [1.0.17](https://github.com/Laravel-Backpack/PRO/tree/1.0.17) (17-May-2022)

- add configurable search operator to fetch like we did for list [\#33](https://github.com/Laravel-Backpack/PRO/pull/33) (@pxpm)

## [1.0.16](https://github.com/Laravel-Backpack/PRO/tree/1.0.16) (10-May-2022)

- ignore inputs without name when generating the repeatable name [\#35](https://github.com/Laravel-Backpack/PRO/pull/35) (@pxpm)

- Inline create fixes - backdrop & form fields [\#34](https://github.com/Laravel-Backpack/PRO/pull/34) (@pxpm)

## [1.0.15](https://github.com/Laravel-Backpack/PRO/tree/1.0.15) (04-May-2022)

- Swap rows without remove [\#32](https://github.com/Laravel-Backpack/PRO/pull/32) (@pxpm)

## [1.0.14](https://github.com/Laravel-Backpack/PRO/tree/1.0.14) (26-Apr-2022)

- use jquery wrapper when attaching the element in page [\#27](https://github.com/Laravel-Backpack/PRO/pull/27) (@pxpm)
- Add ability to send the element and the row that triggered ajax request [\#8](https://github.com/Laravel-Backpack/PRO/pull/8) (@pxpm)

- configurable inline create add button [\#24](https://github.com/Laravel-Backpack/PRO/pull/24) (@pxpm)
- fix fetch\_or\_create container - merge before tag new version [\#19](https://github.com/Laravel-Backpack/PRO/pull/19) (@pxpm)
- fix pivot disable previsously selected [\#16](https://github.com/Laravel-Backpack/PRO/pull/16) (@pxpm)
- date\_range work as subfield [\#15](https://github.com/Laravel-Backpack/PRO/pull/15) (@pxpm)

## [1.0.13](https://github.com/Laravel-Backpack/PRO/tree/1.0.13) (13-Apr-2022)

- Tinymce initialization [\#17](https://github.com/Laravel-Backpack/PRO/pull/17) (@pxpm)
- make sure the defaults are only applied when this operation is requested [\#14](https://github.com/Laravel-Backpack/PRO/pull/14) (@pxpm)

## [1.0.12](https://github.com/Laravel-Backpack/PRO/tree/1.0.12) (01-Apr-2022)

## [1.0.11](https://github.com/Laravel-Backpack/PRO/tree/1.0.11) (01-Apr-2022)

- add cropend event to image fields [\#12](https://github.com/Laravel-Backpack/PRO/pull/12) (@pxpm)
- add showAsterisk for subfields that are required [\#10](https://github.com/Laravel-Backpack/PRO/pull/10) (@pxpm)

- Delete select\_grouped.blade.php [\#11](https://github.com/Laravel-Backpack/PRO/pull/11) (@tabacitu)

## [1.0.10](https://github.com/Laravel-Backpack/PRO/tree/1.0.10) (01-Mar-2022)

- fix repeatable hidden container field names [\#9](https://github.com/Laravel-Backpack/PRO/pull/9) (@pxpm)

- Select2 fields - remove inline width 100%, since it's now fixed by a general CSS rule [\#7](https://github.com/Laravel-Backpack/PRO/pull/7) (@pxpm)

## [1.0.9](https://github.com/Laravel-Backpack/PRO/tree/1.0.9) (15-Feb-2022)

- fix view caching [\#6](https://github.com/Laravel-Backpack/PRO/pull/6) (@tabacitu)

## [1.0.8](https://github.com/Laravel-Backpack/PRO/tree/1.0.8) (14-Feb-2022)

- Fix for `address_algolia` field not found [\#4](https://github.com/Laravel-Backpack/PRO/pull/4) (@promatik)

- Update readme.md [\#5](https://github.com/Laravel-Backpack/PRO/pull/5) (@tabacitu)

## [1.0.7](https://github.com/Laravel-Backpack/PRO/tree/1.0.7) (10-Feb-2022)

- make ajax selects work with translatable attributes [\#1](https://github.com/Laravel-Backpack/PRO/pull/1) (@pxpm)

## [1.0.6](https://github.com/Laravel-Backpack/PRO/tree/1.0.6) (09-Feb-2022)

- change github token for changelog-from-release action [\#3](https://github.com/Laravel-Backpack/PRO/pull/3) (@tabacitu)

## [1.0.5](https://github.com/Laravel-Backpack/PRO/tree/1.0.5) (09-Feb-2022)

## [1.0.4](https://github.com/Laravel-Backpack/PRO/tree/1.0.4) (09-Feb-2022)

- add view helper to tabbed fields in inline create modals. [\#2](https://github.com/Laravel-Backpack/PRO/pull/2) (@pxpm)

## [1.0.3](https://github.com/Laravel-Backpack/PRO/tree/1.0.3) (04-Feb-2022)

## [1.0.2](https://github.com/Laravel-Backpack/PRO/tree/1.0.2) (03-Feb-2022)

## [1.0.1](https://github.com/Laravel-Backpack/PRO/tree/1.0.1) (03-Feb-2022)

## [1.0.0](https://github.com/Laravel-Backpack/PRO/tree/1.0.0) (24-Jan-2022)



\* *This Changelog was automatically generated by [github_changelog_generator](https://github.com/github-changelog-generator/github-changelog-generator)*
