[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `about_us_albums` limit 1
-- ","Time:":117.38} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `about_us_album_photos` limit 1
-- ","Time:":2.15} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `audits` limit 1
-- ","Time:":2.46} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `banners` limit 1
-- ","Time:":2.08} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `bonus` limit 1
-- ","Time:":1.94} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `bug_reports` limit 1
-- ","Time:":2.13} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `categories` limit 1
-- ","Time:":1.76} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` limit 1
-- ","Time:":3.51} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `contact_uses` limit 1
-- ","Time:":1.91} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `deposits` limit 1
-- ","Time:":2.05} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `discuss` limit 1
-- ","Time:":2.17} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `email_logs` limit 1
-- ","Time:":2.28} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` limit 1
-- ","Time:":2.19} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` limit 1
-- ","Time:":1.81} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `information_contacts` limit 1
-- ","Time:":1.97} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_search_cvs` limit 1
-- ","Time:":2.26} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_users` limit 1
-- ","Time:":2.18} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_user_cvs` limit 1
-- ","Time:":2.28} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_user_external_sources` limit 1
-- ","Time:":2.04} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job` limit 1
-- ","Time:":4.97} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_comments` where `job_comments`.`deleted_at` is null limit 1
-- ","Time:":2.35} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_logs` limit 1
-- ","Time:":1.95} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_meta` limit 1
-- ","Time:":1.75} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_seo` limit 1
-- ","Time:":1.89} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_tops` limit 1
-- ","Time:":1.74} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `level_by_job_tops` limit 1
-- ","Time:":1.88} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `level_by_skill_mains` limit 1
-- ","Time:":1.78} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `meta_data` limit 1
-- ","Time:":1.68} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `password_resets` limit 1
-- ","Time:":1.77} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payins` limit 1
-- ","Time:":1.7} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payin_months` limit 1
-- ","Time:":1.76} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payment_debits` limit 1
-- ","Time:":1.66} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payout_logs` limit 1
-- ","Time:":1.91} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payout_log_histories` limit 1
-- ","Time:":1.73} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `posts` limit 1
-- ","Time:":1.98} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `post_meta` limit 1
-- ","Time:":1.74} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `post_seo` limit 1
-- ","Time:":2.63} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `reports` limit 1
-- ","Time:":1.78} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `roles` limit 1
-- ","Time:":1.73} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` limit 1
-- ","Time:":4.86} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `settings` limit 1
-- ","Time:":2.14} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `skills` limit 1
-- ","Time:":1.75} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `skill_mains` limit 1
-- ","Time:":1.85} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs` limit 1
-- ","Time:":2.38} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_books` limit 1
-- ","Time:":2.03} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_discuss` limit 1
-- ","Time:":1.79} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_history_payment` limit 1
-- ","Time:":1.78} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_history_status` limit 1
-- ","Time:":1.7} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cv_metas` limit 1
-- ","Time:":1.88} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_onboards` limit 1
-- ","Time:":3.59} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_payment_debits` limit 1
-- ","Time:":1.7} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `survey_fields` limit 1
-- ","Time:":1.98} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `testimonials` limit 1
-- ","Time:":1.92} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `top_recs` limit 1
-- ","Time:":1.85} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `transactions` limit 1
-- ","Time:":1.72} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` limit 1
-- ","Time:":3.31} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_infos` limit 1
-- ","Time:":1.84} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_job_cares` limit 1
-- ","Time:":1.7} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_role` limit 1
-- ","Time:":1.88} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_survey_results` limit 1
-- ","Time:":1.54} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` limit 1
-- ","Time:":2.03} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallet_transactions` limit 1
-- ","Time:":1.71} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cvs` limit 1
-- ","Time:":2.61} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_sellings` where `warehouse_cv_sellings`.`deleted_at` is null limit 1
-- ","Time:":2.15} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buys` where `warehouse_cv_selling_buys`.`deleted_at` is null limit 1
-- ","Time:":1.97} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_books` limit 1
-- ","Time:":1.77} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_discuss` limit 1
-- ","Time:":1.8} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_history_status` limit 1
-- ","Time:":1.74} 
[2025-08-04 08:31:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_evaluates` limit 1
-- ","Time:":1.74} 
[2025-08-04 08:31:46] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_history_buys` limit 1
-- ","Time:":1.72} 
[2025-08-04 08:31:46] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_qas` limit 1
-- ","Time:":1.78} 
[2025-08-04 08:31:46] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_saves` limit 1
-- ","Time:":1.82} 
[2025-08-04 08:31:46] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_onboards` limit 1
-- ","Time:":1.76} 
[2025-08-04 08:31:46] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `zalopay_transactions` limit 1
-- ","Time:":1.92} 
[2025-08-04 08:35:55] local.DEBUG: [SQL EXEC] {"SQL:":"
select `value`, `key` from `settings`
-- ","Time:":70.16} 
[2025-08-04 08:35:57] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http://recland.local', '', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-04 08:35:57', '2025-08-04 08:35:57')
-- ","Time:":2.43} 
[2025-08-04 08:35:57] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'home' limit 1
-- ","Time:":0.87} 
[2025-08-04 08:35:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1' limit 1
-- ","Time:":1.17} 
[2025-08-04 08:35:59] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1' limit 1
-- ","Time:":3.07} 
[2025-08-04 08:36:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)
-- ","Time:":0.92} 
[2025-08-04 08:36:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `users` where `type` = 'employer' and `is_active` = '1' and `is_real` = '1'
-- ","Time:":21.29} 
[2025-08-04 08:36:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `users` where `type` = 'rec' and `is_active` = '1' and `is_real` = '1'
-- ","Time:":6.71} 
[2025-08-04 08:36:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `warehouse_cvs` where `is_active` = '1' and `is_real` = '1'
-- ","Time:":90.4} 
[2025-08-04 08:36:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `is_active` = '1' and `is_real` = '1'
-- ","Time:":15.72} 
[2025-08-04 08:36:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `companies` where `is_active` = '1' and `is_real` = '1'
-- ","Time:":1.93} 
[2025-08-04 08:36:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select COUNT(id) as total, DATE_FORMAT(created_at, \"%m\") as month from `warehouse_cvs` where `is_active` = '1' and `is_real` = '1' and `created_at` between '2025-01-01 00:00:00' and '2025-12-31 23:59:59' group by DATE_FORMAT(created_at, \"%m\") order by DATE_FORMAT(created_at, \"%m\")
-- ","Time:":48.7} 
[2025-08-04 08:36:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select (count(id)) as total, (DATE_FORMAT(created_at, '%m')) as month from `users` where `type` = 'rec' and `is_active` = '1' and `is_real` = '1' and `created_at` >= '2025-01-01 00:00:00' and `created_at` <= '2025-12-31 23:59:59' group by DATE_FORMAT(created_at, '%m') order by DATE_FORMAT(created_at, '%m') asc
-- ","Time:":8.89} 
[2025-08-04 08:36:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select COUNT(id) as total, DATE_FORMAT(created_at, \"%m\") as month from `warehouse_cv_sellings` where `status` = '0' and `is_real` = '1' and `created_at` between '2025-01-01 00:00:00' and '2025-12-31 23:59:59' and `warehouse_cv_sellings`.`deleted_at` is null group by DATE_FORMAT(created_at, \"%m\") order by DATE_FORMAT(created_at, \"%m\")
-- ","Time:":67.35} 
[2025-08-04 08:36:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select COUNT(submit_cvs.id) as total, DATE_FORMAT(submit_cvs.created_at, \"%m\") as month from `submit_cvs` inner join `job` on `submit_cvs`.`job_id` = `job`.`id` where `submit_cvs`.`is_active` = '1' and `job`.`is_real` = '1' and `submit_cvs`.`created_at` between '2025-01-01 00:00:00' and '2025-12-31 23:59:59' group by DATE_FORMAT(submit_cvs.created_at, \"%m\") order by DATE_FORMAT(submit_cvs.created_at, \"%m\")
-- ","Time:":9.64} 
[2025-08-04 08:36:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(id) as total, DATE_FORMAT(created_at, \"%m\") as month from `users` where `is_active` = '1' and `is_real` = '1' and `type` = 'employer' and `created_at` between '2025-01-01 00:00:00' and '2025-12-31 23:59:59' group by DATE_FORMAT(created_at, \"%m\") order by DATE_FORMAT(created_at, \"%m\")
-- ","Time:":8.07} 
[2025-08-04 08:36:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(id) as total, DATE_FORMAT(created_at, \"%m\") as month from `job` where `is_active` = '1' and `is_real` = '1' and `created_at` between '2025-01-01 00:00:00' and '2025-12-31 23:59:59' group by DATE_FORMAT(created_at, \"%m\") order by DATE_FORMAT(created_at, \"%m\")
-- ","Time:":7.06} 
[2025-08-04 08:36:03] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `testimonials` where `type` = '0' and `is_active` = '1'
-- ","Time:":0.6} 
[2025-08-04 08:36:03] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `top_recs` order by `amount` desc
-- ","Time:":0.77} 
[2025-08-04 08:36:03] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `posts` order by `id` desc limit 1
-- ","Time:":1.67} 
[2025-08-04 08:36:04] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `categories`
-- ","Time:":0.81} 
[2025-08-04 08:36:06] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\"skill\":null,\"lang\":\"vi\"}', 'http://recland.local/job/api/list', 'http://recland.local/', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"24\"],\"x-xsrf-token\":[\"eyJpdiI6ImRPUWpqTWhPV1hRK0R4d2NyZWNOd1E9PSIsInZhbHVlIjoiY3pKcFVNbEthSlZxVmdsTktJaFdIYkV1bExJMHNINyttaExzQ0tNczE2dkcyVzBsWksyMWZoYWNjZkJpa3FiZFlyWFpBVTdQZmdwb0hFMVd1M092cm9pejYvb1FRV0VJc0U3Rjlra2RsUmJmTEFqSmpqdnRjRitITjdHeDdEMTYiLCJtYWMiOiIzNWUyYjA3YzZhNTNjZTQ1MDE5ZTZlZmNlZDlmMmJkYzUyNjBlZWQ5NGM2NTFiMzg1ODk4MGE4OGI4ZTNkMjczIiwidGFnIjoiIn0=\"],\"x-requested-with\":[\"XMLHttpRequest\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"application\\/json, text\\/plain, *\\/*\"],\"content-type\":[\"application\\/json\"],\"origin\":[\"http:\\/\\/recland.local\"],\"referer\":[\"http:\\/\\/recland.local\\/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImRPUWpqTWhPV1hRK0R4d2NyZWNOd1E9PSIsInZhbHVlIjoiY3pKcFVNbEthSlZxVmdsTktJaFdIYkV1bExJMHNINyttaExzQ0tNczE2dkcyVzBsWksyMWZoYWNjZkJpa3FiZFlyWFpBVTdQZmdwb0hFMVd1M092cm9pejYvb1FRV0VJc0U3Rjlra2RsUmJmTEFqSmpqdnRjRitITjdHeDdEMTYiLCJtYWMiOiIzNWUyYjA3YzZhNTNjZTQ1MDE5ZTZlZmNlZDlmMmJkYzUyNjBlZWQ5NGM2NTFiMzg1ODk4MGE4OGI4ZTNkMjczIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IlROWDZYWmNteUFndTRCdDJueUEzamc9PSIsInZhbHVlIjoiSVMxdU1ha1QybXYzaGNvaHd4QnNvQXZmek1VQWx3S1hVcWk3eCttWFltN0RlQmQrUzE4eUxZaVhTRzlJM044Q1hxbUhJSC9lTlIxMmJFWW44QnpoY2FtOTlJSk1aTXNuYWcxUHpHTS9DN1pONHltUkR4NVZ5eWpRK0pIT1MvQUwiLCJtYWMiOiJlZDI3OWI3NzQ2YjA1MGVhNjczZDdlMGZiYjc4ZDA2ZTE3ZmQ1OTk0NTNiNGQ1YzNlNTY4ODk0NjM1ZjliYWE2IiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-04 08:36:06', '2025-08-04 08:36:06')
-- ","Time:":24.37} 
[2025-08-04 08:36:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select `job`.*, `job_meta`.`priority` from `job` inner join `job_meta` on `job`.`id` = `job_meta`.`job_id` where `job`.`is_active` = '1' and `job`.`status` = '1' and `job`.`expire_at` >= '2025-08-04' order by FIELD(job_meta.priority, 'Premium', 'Hot', 'New'), `job`.`publish_at` desc limit 30
-- ","Time:":33.54} 
[2025-08-04 08:36:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` in (3, 187, 217, 274, 277, 278, 286, 487, 491, 694, 695, 696)
-- ","Time:":0.67} 
[2025-08-04 08:36:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_meta` where `job_meta`.`job_id` in (631, 700, 821, 825, 826, 827, 828, 838, 861, 865, 866, 1625, 1626, 1628, 1629, 1630, 1631, 1632, 1633, 1634, 1635, 1636, 1637, 1638, 1639, 1640, 1641, 1642, 1643, 1644)
-- ","Time:":3.13} 
[2025-08-04 08:36:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select skills from `job` where `skills` is not null and `skills` != '' and `is_active` = '1' and `status` = '1' and `expire_at` >= '2025-08-04' group by `skills` order by COUNT(id) DESC limit 8
-- ","Time:":17.89} 
[2025-08-04 08:36:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1644' and `type` = 'save'
-- ","Time:":0.72} 
[2025-08-04 08:36:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1642' and `type` = 'save'
-- ","Time:":0.55} 
[2025-08-04 08:36:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1643' and `type` = 'save'
-- ","Time:":0.34} 
[2025-08-04 08:36:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '631' and `type` = 'save'
-- ","Time:":0.38} 
[2025-08-04 08:36:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1641' and `type` = 'save'
-- ","Time:":0.41} 
[2025-08-04 08:36:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1640' and `type` = 'save'
-- ","Time:":0.36} 
[2025-08-04 08:36:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1628' and `type` = 'save'
-- ","Time:":0.27} 
[2025-08-04 08:36:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1629' and `type` = 'save'
-- ","Time:":0.24} 
[2025-08-04 08:36:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1630' and `type` = 'save'
-- ","Time:":0.36} 
[2025-08-04 08:36:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1625' and `type` = 'save'
-- ","Time:":0.33} 
[2025-08-04 08:36:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1626' and `type` = 'save'
-- ","Time:":0.24} 
[2025-08-04 08:36:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1631' and `type` = 'save'
-- ","Time:":0.23} 
[2025-08-04 08:36:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1632' and `type` = 'save'
-- ","Time:":0.22} 
[2025-08-04 08:36:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1633' and `type` = 'save'
-- ","Time:":0.22} 
[2025-08-04 08:36:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1634' and `type` = 'save'
-- ","Time:":0.22} 
[2025-08-04 08:36:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1635' and `type` = 'save'
-- ","Time:":0.22} 
[2025-08-04 08:36:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1636' and `type` = 'save'
-- ","Time:":0.22} 
[2025-08-04 08:36:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1637' and `type` = 'save'
-- ","Time:":0.22} 
[2025-08-04 08:36:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1638' and `type` = 'save'
-- ","Time:":0.26} 
[2025-08-04 08:36:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1639' and `type` = 'save'
-- ","Time:":0.22} 
[2025-08-04 08:36:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '700' and `type` = 'save'
-- ","Time:":0.22} 
[2025-08-04 08:36:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '821' and `type` = 'save'
-- ","Time:":0.22} 
[2025-08-04 08:36:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '865' and `type` = 'save'
-- ","Time:":0.3} 
[2025-08-04 08:36:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '866' and `type` = 'save'
-- ","Time:":0.25} 
[2025-08-04 08:36:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '861' and `type` = 'save'
-- ","Time:":0.22} 
[2025-08-04 08:36:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '838' and `type` = 'save'
-- ","Time:":0.22} 
[2025-08-04 08:36:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '825' and `type` = 'save'
-- ","Time:":0.27} 
[2025-08-04 08:36:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '826' and `type` = 'save'
-- ","Time:":0.25} 
[2025-08-04 08:36:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '827' and `type` = 'save'
-- ","Time:":0.26} 
[2025-08-04 08:36:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '828' and `type` = 'save'
-- ","Time:":0.25} 
[2025-08-04 08:36:31] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `users` where `type` = 'employer'
-- ","Time:":12.18} 
[2025-08-04 08:36:31] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `users` where `type` = 'employer' and month(`created_at`) = '08' and year(`created_at`) = '2025'
-- ","Time:":8.06} 
[2025-08-04 08:36:33] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1' limit 1
-- ","Time:":0.58} 
[2025-08-04 08:36:34] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `users` where `type` = 'employer'
-- ","Time:":29.06} 
[2025-08-04 08:36:34] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `users` where `type` = 'employer' and month(`created_at`) = '08' and year(`created_at`) = '2025'
-- ","Time:":3.15} 
[2025-08-04 08:36:34] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `type` = 'employer' order by `id` desc limit 25
-- ","Time:":1.99} 
[2025-08-04 08:36:34] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as total_rows from (select `users`.`id` from `users`) as `users_aggregator`
-- ","Time:":2.0} 
[2025-08-04 08:36:34] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as total_rows from (select `users`.`id` from `users` where `type` = 'employer') as `users_aggregator`
-- ","Time:":2.43} 
[2025-08-04 08:36:34] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '7480' and `wallets`.`user_id` is not null limit 1
-- ","Time:":3.77} 
[2025-08-04 08:36:34] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `job`.`employer_id` = '7480' and `job`.`employer_id` is not null
-- ","Time:":4.15} 
[2025-08-04 08:36:34] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '7474' and `wallets`.`user_id` is not null limit 1
-- ","Time:":5.59} 
[2025-08-04 08:36:34] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `job`.`employer_id` = '7474' and `job`.`employer_id` is not null
-- ","Time:":7.92} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '7473' and `wallets`.`user_id` is not null limit 1
-- ","Time:":4.34} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `job`.`employer_id` = '7473' and `job`.`employer_id` is not null
-- ","Time:":3.89} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '7457' and `wallets`.`user_id` is not null limit 1
-- ","Time:":2.38} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `job`.`employer_id` = '7457' and `job`.`employer_id` is not null
-- ","Time:":3.95} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` = '696' and `companies`.`id` is not null limit 1
-- ","Time:":0.38} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '7456' and `wallets`.`user_id` is not null limit 1
-- ","Time:":2.33} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `job`.`employer_id` = '7456' and `job`.`employer_id` is not null
-- ","Time:":3.86} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '7447' and `wallets`.`user_id` is not null limit 1
-- ","Time:":2.55} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `job`.`employer_id` = '7447' and `job`.`employer_id` is not null
-- ","Time:":3.82} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '7424' and `wallets`.`user_id` is not null limit 1
-- ","Time:":5.44} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `job`.`employer_id` = '7424' and `job`.`employer_id` is not null
-- ","Time:":3.82} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '7419' and `wallets`.`user_id` is not null limit 1
-- ","Time:":2.56} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `job`.`employer_id` = '7419' and `job`.`employer_id` is not null
-- ","Time:":3.76} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` = '695' and `companies`.`id` is not null limit 1
-- ","Time:":0.38} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '7415' and `wallets`.`user_id` is not null limit 1
-- ","Time:":5.34} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `job`.`employer_id` = '7415' and `job`.`employer_id` is not null
-- ","Time:":8.63} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` = '694' and `companies`.`id` is not null limit 1
-- ","Time:":0.73} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '7412' and `wallets`.`user_id` is not null limit 1
-- ","Time:":2.36} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `job`.`employer_id` = '7412' and `job`.`employer_id` is not null
-- ","Time:":3.73} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '7410' and `wallets`.`user_id` is not null limit 1
-- ","Time:":2.47} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `job`.`employer_id` = '7410' and `job`.`employer_id` is not null
-- ","Time:":3.45} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '7402' and `wallets`.`user_id` is not null limit 1
-- ","Time:":2.45} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `job`.`employer_id` = '7402' and `job`.`employer_id` is not null
-- ","Time:":3.43} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` = '693' and `companies`.`id` is not null limit 1
-- ","Time:":0.39} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '7372' and `wallets`.`user_id` is not null limit 1
-- ","Time:":2.38} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `job`.`employer_id` = '7372' and `job`.`employer_id` is not null
-- ","Time:":3.53} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '7350' and `wallets`.`user_id` is not null limit 1
-- ","Time:":2.66} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `job`.`employer_id` = '7350' and `job`.`employer_id` is not null
-- ","Time:":3.49} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` = '692' and `companies`.`id` is not null limit 1
-- ","Time:":0.37} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '6644' and `wallets`.`user_id` is not null limit 1
-- ","Time:":2.45} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `job`.`employer_id` = '6644' and `job`.`employer_id` is not null
-- ","Time:":3.31} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` = '691' and `companies`.`id` is not null limit 1
-- ","Time:":0.36} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '6643' and `wallets`.`user_id` is not null limit 1
-- ","Time:":2.35} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `job`.`employer_id` = '6643' and `job`.`employer_id` is not null
-- ","Time:":3.25} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` = '690' and `companies`.`id` is not null limit 1
-- ","Time:":0.33} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '6642' and `wallets`.`user_id` is not null limit 1
-- ","Time:":2.32} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `job`.`employer_id` = '6642' and `job`.`employer_id` is not null
-- ","Time:":3.26} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` = '689' and `companies`.`id` is not null limit 1
-- ","Time:":0.33} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '6641' and `wallets`.`user_id` is not null limit 1
-- ","Time:":2.31} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `job`.`employer_id` = '6641' and `job`.`employer_id` is not null
-- ","Time:":3.3} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` = '688' and `companies`.`id` is not null limit 1
-- ","Time:":0.34} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '6640' and `wallets`.`user_id` is not null limit 1
-- ","Time:":2.35} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `job`.`employer_id` = '6640' and `job`.`employer_id` is not null
-- ","Time:":3.33} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` = '687' and `companies`.`id` is not null limit 1
-- ","Time:":0.36} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '6639' and `wallets`.`user_id` is not null limit 1
-- ","Time:":2.39} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `job`.`employer_id` = '6639' and `job`.`employer_id` is not null
-- ","Time:":7.21} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` = '686' and `companies`.`id` is not null limit 1
-- ","Time:":0.35} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '6638' and `wallets`.`user_id` is not null limit 1
-- ","Time:":5.37} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `job`.`employer_id` = '6638' and `job`.`employer_id` is not null
-- ","Time:":3.58} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` = '685' and `companies`.`id` is not null limit 1
-- ","Time:":0.34} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '6637' and `wallets`.`user_id` is not null limit 1
-- ","Time:":2.39} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `job`.`employer_id` = '6637' and `job`.`employer_id` is not null
-- ","Time:":3.55} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` = '684' and `companies`.`id` is not null limit 1
-- ","Time:":0.34} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '6636' and `wallets`.`user_id` is not null limit 1
-- ","Time:":2.35} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `job`.`employer_id` = '6636' and `job`.`employer_id` is not null
-- ","Time:":3.52} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` = '683' and `companies`.`id` is not null limit 1
-- ","Time:":0.35} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '6635' and `wallets`.`user_id` is not null limit 1
-- ","Time:":2.31} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `job`.`employer_id` = '6635' and `job`.`employer_id` is not null
-- ","Time:":3.43} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` = '682' and `companies`.`id` is not null limit 1
-- ","Time:":0.34} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '6634' and `wallets`.`user_id` is not null limit 1
-- ","Time:":2.31} 
[2025-08-04 08:36:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `job`.`employer_id` = '6634' and `job`.`employer_id` is not null
-- ","Time:":3.41} 
[2025-08-04 09:01:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `about_us_albums` limit 1
-- ","Time:":4.7} 
[2025-08-04 09:01:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `about_us_album_photos` limit 1
-- ","Time:":0.31} 
[2025-08-04 09:01:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `audits` limit 1
-- ","Time:":0.31} 
[2025-08-04 09:01:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `banners` limit 1
-- ","Time:":0.28} 
[2025-08-04 09:01:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `bonus` limit 1
-- ","Time:":0.28} 
[2025-08-04 09:01:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `bug_reports` limit 1
-- ","Time:":0.31} 
[2025-08-04 09:01:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `categories` limit 1
-- ","Time:":0.26} 
[2025-08-04 09:01:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` limit 1
-- ","Time:":0.83} 
[2025-08-04 09:01:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `contact_uses` limit 1
-- ","Time:":0.26} 
[2025-08-04 09:01:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `deposits` limit 1
-- ","Time:":0.38} 
[2025-08-04 09:01:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `discuss` limit 1
-- ","Time:":0.28} 
[2025-08-04 09:01:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `email_logs` limit 1
-- ","Time:":0.28} 
[2025-08-04 09:01:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` limit 1
-- ","Time:":0.25} 
[2025-08-04 09:01:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` limit 1
-- ","Time:":0.38} 
[2025-08-04 09:01:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `information_contacts` limit 1
-- ","Time:":0.39} 
[2025-08-04 09:01:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_search_cvs` limit 1
-- ","Time:":0.46} 
[2025-08-04 09:01:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_users` limit 1
-- ","Time:":0.45} 
[2025-08-04 09:01:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_user_cvs` limit 1
-- ","Time:":0.39} 
[2025-08-04 09:01:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_user_external_sources` limit 1
-- ","Time:":0.43} 
[2025-08-04 09:01:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job` limit 1
-- ","Time:":2.52} 
[2025-08-04 09:01:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_comments` where `job_comments`.`deleted_at` is null limit 1
-- ","Time:":0.42} 
[2025-08-04 09:01:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_logs` limit 1
-- ","Time:":0.38} 
[2025-08-04 09:01:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_meta` limit 1
-- ","Time:":0.41} 
[2025-08-04 09:01:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_seo` limit 1
-- ","Time:":0.43} 
[2025-08-04 09:01:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_tops` limit 1
-- ","Time:":0.34} 
[2025-08-04 09:01:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `level_by_job_tops` limit 1
-- ","Time:":0.35} 
[2025-08-04 09:01:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `level_by_skill_mains` limit 1
-- ","Time:":0.4} 
[2025-08-04 09:01:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `meta_data` limit 1
-- ","Time:":0.35} 
[2025-08-04 09:01:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `password_resets` limit 1
-- ","Time:":0.33} 
[2025-08-04 09:01:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payins` limit 1
-- ","Time:":0.33} 
[2025-08-04 09:01:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payin_months` limit 1
-- ","Time:":0.41} 
[2025-08-04 09:01:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payment_debits` limit 1
-- ","Time:":0.32} 
[2025-08-04 09:01:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payout_logs` limit 1
-- ","Time:":0.38} 
[2025-08-04 09:01:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payout_log_histories` limit 1
-- ","Time:":0.37} 
[2025-08-04 09:01:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `posts` limit 1
-- ","Time:":0.46} 
[2025-08-04 09:01:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `post_meta` limit 1
-- ","Time:":0.34} 
[2025-08-04 09:01:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `post_seo` limit 1
-- ","Time:":0.25} 
[2025-08-04 09:01:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `reports` limit 1
-- ","Time:":0.24} 
[2025-08-04 09:01:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `roles` limit 1
-- ","Time:":0.24} 
[2025-08-04 09:01:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` limit 1
-- ","Time:":0.25} 
[2025-08-04 09:01:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `settings` limit 1
-- ","Time:":0.25} 
[2025-08-04 09:01:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `skills` limit 1
-- ","Time:":0.24} 
[2025-08-04 09:01:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `skill_mains` limit 1
-- ","Time:":0.22} 
[2025-08-04 09:01:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs` limit 1
-- ","Time:":0.38} 
[2025-08-04 09:01:46] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_books` limit 1
-- ","Time:":0.36} 
[2025-08-04 09:01:46] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_discuss` limit 1
-- ","Time:":0.38} 
[2025-08-04 09:01:46] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_history_payment` limit 1
-- ","Time:":0.4} 
[2025-08-04 09:01:46] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_history_status` limit 1
-- ","Time:":0.39} 
[2025-08-04 09:01:46] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cv_metas` limit 1
-- ","Time:":0.27} 
[2025-08-04 09:01:46] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_onboards` limit 1
-- ","Time:":0.25} 
[2025-08-04 09:01:46] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_payment_debits` limit 1
-- ","Time:":0.25} 
[2025-08-04 09:01:46] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `survey_fields` limit 1
-- ","Time:":0.32} 
[2025-08-04 09:01:46] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `testimonials` limit 1
-- ","Time:":0.24} 
[2025-08-04 09:01:46] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `top_recs` limit 1
-- ","Time:":0.24} 
[2025-08-04 09:01:46] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `transactions` limit 1
-- ","Time:":0.23} 
[2025-08-04 09:01:46] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` limit 1
-- ","Time:":0.46} 
[2025-08-04 09:01:46] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_infos` limit 1
-- ","Time:":0.24} 
[2025-08-04 09:01:46] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_job_cares` limit 1
-- ","Time:":0.23} 
[2025-08-04 09:01:46] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_role` limit 1
-- ","Time:":0.22} 
[2025-08-04 09:01:46] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_survey_results` limit 1
-- ","Time:":0.36} 
[2025-08-04 09:01:46] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` limit 1
-- ","Time:":0.33} 
[2025-08-04 09:01:46] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallet_transactions` limit 1
-- ","Time:":0.39} 
[2025-08-04 09:01:46] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cvs` limit 1
-- ","Time:":0.45} 
[2025-08-04 09:01:46] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_sellings` where `warehouse_cv_sellings`.`deleted_at` is null limit 1
-- ","Time:":0.29} 
[2025-08-04 09:01:46] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buys` where `warehouse_cv_selling_buys`.`deleted_at` is null limit 1
-- ","Time:":0.27} 
[2025-08-04 09:01:46] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_books` limit 1
-- ","Time:":0.24} 
[2025-08-04 09:01:46] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_discuss` limit 1
-- ","Time:":0.22} 
[2025-08-04 09:01:46] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_history_status` limit 1
-- ","Time:":0.23} 
[2025-08-04 09:01:46] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_evaluates` limit 1
-- ","Time:":0.48} 
[2025-08-04 09:01:46] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_history_buys` limit 1
-- ","Time:":0.4} 
[2025-08-04 09:01:46] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_qas` limit 1
-- ","Time:":0.37} 
[2025-08-04 09:01:46] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_saves` limit 1
-- ","Time:":0.33} 
[2025-08-04 09:01:46] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_onboards` limit 1
-- ","Time:":0.39} 
[2025-08-04 09:01:46] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `zalopay_transactions` limit 1
-- ","Time:":0.37} 
[2025-08-04 10:37:54] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1' limit 1
-- ","Time:":15.6} 
[2025-08-04 10:37:55] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1' limit 1
-- ","Time:":19.24} 
[2025-08-04 10:37:55] local.DEBUG: [SQL EXEC] {"SQL:":"
select `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)
-- ","Time:":0.64} 
[2025-08-04 10:37:55] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `users` where `type` = 'employer' and `is_active` = '1' and `is_real` = '1'
-- ","Time:":13.67} 
[2025-08-04 10:37:55] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `users` where `type` = 'rec' and `is_active` = '1' and `is_real` = '1'
-- ","Time:":8.46} 
[2025-08-04 10:37:55] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `warehouse_cvs` where `is_active` = '1' and `is_real` = '1'
-- ","Time:":39.72} 
[2025-08-04 10:37:55] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `is_active` = '1' and `is_real` = '1'
-- ","Time:":6.59} 
[2025-08-04 10:37:55] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `companies` where `is_active` = '1' and `is_real` = '1'
-- ","Time:":1.78} 
[2025-08-04 10:37:55] local.DEBUG: [SQL EXEC] {"SQL:":"
select COUNT(id) as total, DATE_FORMAT(created_at, \"%m\") as month from `warehouse_cvs` where `is_active` = '1' and `is_real` = '1' and `created_at` between '2025-01-01 00:00:00' and '2025-12-31 23:59:59' group by DATE_FORMAT(created_at, \"%m\") order by DATE_FORMAT(created_at, \"%m\")
-- ","Time:":48.67} 
[2025-08-04 10:37:55] local.DEBUG: [SQL EXEC] {"SQL:":"
select (count(id)) as total, (DATE_FORMAT(created_at, '%m')) as month from `users` where `type` = 'rec' and `is_active` = '1' and `is_real` = '1' and `created_at` >= '2025-01-01 00:00:00' and `created_at` <= '2025-12-31 23:59:59' group by DATE_FORMAT(created_at, '%m') order by DATE_FORMAT(created_at, '%m') asc
-- ","Time:":9.11} 
[2025-08-04 10:37:55] local.DEBUG: [SQL EXEC] {"SQL:":"
select COUNT(id) as total, DATE_FORMAT(created_at, \"%m\") as month from `warehouse_cv_sellings` where `status` = '0' and `is_real` = '1' and `created_at` between '2025-01-01 00:00:00' and '2025-12-31 23:59:59' and `warehouse_cv_sellings`.`deleted_at` is null group by DATE_FORMAT(created_at, \"%m\") order by DATE_FORMAT(created_at, \"%m\")
-- ","Time:":51.17} 
[2025-08-04 10:37:55] local.DEBUG: [SQL EXEC] {"SQL:":"
select COUNT(submit_cvs.id) as total, DATE_FORMAT(submit_cvs.created_at, \"%m\") as month from `submit_cvs` inner join `job` on `submit_cvs`.`job_id` = `job`.`id` where `submit_cvs`.`is_active` = '1' and `job`.`is_real` = '1' and `submit_cvs`.`created_at` between '2025-01-01 00:00:00' and '2025-12-31 23:59:59' group by DATE_FORMAT(submit_cvs.created_at, \"%m\") order by DATE_FORMAT(submit_cvs.created_at, \"%m\")
-- ","Time:":9.1} 
[2025-08-04 10:37:55] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(id) as total, DATE_FORMAT(created_at, \"%m\") as month from `users` where `is_active` = '1' and `is_real` = '1' and `type` = 'employer' and `created_at` between '2025-01-01 00:00:00' and '2025-12-31 23:59:59' group by DATE_FORMAT(created_at, \"%m\") order by DATE_FORMAT(created_at, \"%m\")
-- ","Time:":7.17} 
[2025-08-04 10:37:55] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(id) as total, DATE_FORMAT(created_at, \"%m\") as month from `job` where `is_active` = '1' and `is_real` = '1' and `created_at` between '2025-01-01 00:00:00' and '2025-12-31 23:59:59' group by DATE_FORMAT(created_at, \"%m\") order by DATE_FORMAT(created_at, \"%m\")
-- ","Time:":6.8} 
[2025-08-04 10:38:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1' limit 1
-- ","Time:":15.41} 
[2025-08-04 10:38:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)
-- ","Time:":0.57} 
[2025-08-04 10:38:01] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1' limit 1
-- ","Time:":19.12} 
[2025-08-04 10:38:01] local.DEBUG: [SQL EXEC] {"SQL:":"
select `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)
-- ","Time:":0.4} 
[2025-08-04 10:38:01] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `type` = 'rec' order by `id` desc limit 10
-- ","Time:":0.53} 
[2025-08-04 10:38:01] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as total_rows from (select `users`.`id` from `users`) as `users_aggregator`
-- ","Time:":1.79} 
[2025-08-04 10:38:01] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as total_rows from (select `users`.`id` from `users` where `type` = 'rec') as `users_aggregator`
-- ","Time:":2.61} 
[2025-08-04 10:38:04] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1' limit 1
-- ","Time:":19.8} 
[2025-08-04 10:38:04] local.DEBUG: [SQL EXEC] {"SQL:":"
select `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)
-- ","Time:":0.55} 
[2025-08-04 10:38:04] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '7486' limit 1
-- ","Time:":0.54} 
[2025-08-04 10:38:04] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_role` where `user_role`.`user_id` in (7486)
-- ","Time:":0.38} 
[2025-08-04 10:38:04] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_infos` where `user_infos`.`user_id` = '7486' and `user_infos`.`user_id` is not null limit 1
-- ","Time:":1.0} 
[2025-08-04 10:38:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1' limit 1
-- ","Time:":18.35} 
[2025-08-04 10:38:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)
-- ","Time:":0.56} 
[2025-08-04 10:38:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `submit_cvs` where `user_id` = '7486'
-- ","Time:":2.99} 
[2025-08-04 11:23:13] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1' limit 1
-- ","Time:":66.2} 
[2025-08-04 11:23:13] local.DEBUG: [SQL EXEC] {"SQL:":"
select `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)
-- ","Time:":0.84} 
[2025-08-04 11:23:13] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '7486' limit 1
-- ","Time:":0.65} 
[2025-08-04 11:23:13] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_role` where `user_role`.`user_id` in (7486)
-- ","Time:":0.4} 
[2025-08-04 11:23:14] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_infos` where `user_infos`.`user_id` = '7486' and `user_infos`.`user_id` is not null limit 1
-- ","Time:":1.73} 
[2025-08-04 11:23:17] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1' limit 1
-- ","Time:":3.21} 
[2025-08-04 11:23:17] local.DEBUG: [SQL EXEC] {"SQL:":"
select `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)
-- ","Time:":0.81} 
[2025-08-04 11:23:17] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `submit_cvs` where `user_id` = '7486'
-- ","Time:":3.21} 
[2025-08-04 14:07:37] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1' limit 1
-- ","Time:":73.61} 
[2025-08-04 14:07:38] local.DEBUG: [SQL EXEC] {"SQL:":"
select `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)
-- ","Time:":0.84} 
[2025-08-04 14:07:41] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1' limit 1
-- ","Time:":17.24} 
[2025-08-04 14:07:41] local.DEBUG: [SQL EXEC] {"SQL:":"
select `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)
-- ","Time:":0.63} 
[2025-08-04 14:07:41] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `type` = 'rec' order by `id` desc limit 10
-- ","Time:":0.83} 
[2025-08-04 14:07:41] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as total_rows from (select `users`.`id` from `users`) as `users_aggregator`
-- ","Time:":4.16} 
[2025-08-04 14:07:41] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as total_rows from (select `users`.`id` from `users` where `type` = 'rec') as `users_aggregator`
-- ","Time:":2.57} 
[2025-08-04 14:07:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1' limit 1
-- ","Time:":16.97} 
[2025-08-04 14:07:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)
-- ","Time:":0.6} 
[2025-08-04 14:07:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '7486' limit 1
-- ","Time:":0.59} 
[2025-08-04 14:07:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_role` where `user_role`.`user_id` in (7486)
-- ","Time:":0.44} 
[2025-08-04 14:07:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_infos` where `user_infos`.`user_id` = '7486' and `user_infos`.`user_id` is not null limit 1
-- ","Time:":1.63} 
[2025-08-04 14:07:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1' limit 1
-- ","Time:":3.58} 
[2025-08-04 14:07:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)
-- ","Time:":0.59} 
[2025-08-04 14:07:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `submit_cvs` where `user_id` = '7486'
-- ","Time:":3.13} 
[2025-08-04 14:08:31] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job`
-- ","Time:":12.67} 
[2025-08-04 14:08:31] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `is_active` = '1'
-- ","Time:":7.94} 
[2025-08-04 14:08:31] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where month(`created_at`) = '08' and year(`created_at`) = '2025'
-- ","Time:":8.14} 
[2025-08-04 14:08:31] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1' limit 1
-- ","Time:":14.54} 
[2025-08-04 14:08:31] local.DEBUG: [SQL EXEC] {"SQL:":"
select `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)
-- ","Time:":0.54} 
[2025-08-04 14:08:31] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1' limit 1
-- ","Time:":0.99} 
[2025-08-04 14:08:32] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1' limit 1
-- ","Time:":20.27} 
[2025-08-04 14:08:32] local.DEBUG: [SQL EXEC] {"SQL:":"
select `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)
-- ","Time:":0.58} 
[2025-08-04 14:08:32] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1' limit 1
-- ","Time:":23.24} 
[2025-08-04 14:08:32] local.DEBUG: [SQL EXEC] {"SQL:":"
select `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)
-- ","Time:":0.75} 
[2025-08-04 14:08:33] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `type` = 'rec' order by `id` desc limit 10
-- ","Time:":0.77} 
[2025-08-04 14:08:33] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as total_rows from (select `users`.`id` from `users`) as `users_aggregator`
-- ","Time:":3.95} 
[2025-08-04 14:08:33] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as total_rows from (select `users`.`id` from `users` where `type` = 'rec') as `users_aggregator`
-- ","Time:":4.7} 
