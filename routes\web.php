<?php

use App\Http\Controllers\Frontend\PolicyController;
use Illuminate\Support\Facades\Route;

use App\Http\Controllers\Frontend\UserController;
use App\Http\Controllers\Frontend\LanguageController;
use App\Http\Controllers\Frontend\JobController;
use App\Http\Controllers\Frontend\UserJobCareController;
use App\Http\Controllers\Frontend\HomeController;
use App\Http\Controllers\Frontend\RecController;
use App\Http\Controllers\Frontend\EmployerController;
use App\Http\Controllers\Frontend\PostController;
use App\Http\Controllers\Frontend\HtmlController;
use App\Http\Controllers\Frontend\WareHouseSubmitCvController;
use App\Http\Controllers\Frontend\AjaxController;
use App\Http\Controllers\Frontend\OtherController;
use App\Http\Controllers\Frontend\CompanyController;
use App\Http\Controllers\Frontend\ConfirmCandidateController;
use App\Http\Controllers\Admin\DownloadFileController;
use App\Http\Controllers\Admin\AdminDiscussController;
use App\Http\Controllers\BugReportController;

//language
Route::get('/language/{language}', [LanguageController::class, 'changeLanguage'])->name('change-language');
Route::get('/baocao/gov', [\App\Http\Controllers\Api\ReportGovController::class, 'report'])->name('api.report-gov');
//end language

Route::group(['middleware' => ['localization', 'visit-website']], function () {
    Route::get('/', [HomeController::class, 'index'])->name('home');

    Route::group(['prefix' => 'policy'], function () {
        Route::get('/mechanism-of-action-rec', [PolicyController::class, 'mechanismOfActionRec'])->name('mechanismOfActionRec');
        Route::get('/mechanism-of-action-collaborator', [PolicyController::class, 'mechanismOfActionCollaborator'])->name('mechanismOfActionCollaborator');
        Route::get('/terms-of-service', [PolicyController::class, 'termsOfService'])->name('termsOfService');
        Route::get('/privacy-policy', [PolicyController::class, 'privacyPolicy'])->name('privacyPolicy');
        Route::get('/guild-delete-data', [PolicyController::class, 'guideDeleteData'])->name('guideDeleteData');
    });

    //CTV
    Route::get('/login', [UserController::class, 'recLogin'])->name('rec-login');
    Route::get('/register', [UserController::class, 'recRegister'])->name('rec-register');
    Route::post('/login', [UserController::class, 'recPostLogin'])->name('rec-post-login');
    Route::post('/register', [UserController::class, 'recPostRegister'])->name('rec-post-register');
    Route::get('/verify', [UserController::class, 'verifyEmail'])->name('verify-email');
    Route::get('/verify-employer', [UserController::class, 'verifyEmailEmployer'])->name('verify-email-employer');
    Route::get('rec/forgot-password', [UserController::class, 'recShowSendEmailForgotPassword'])->name('rec-show-forgot-password');
    Route::post('/send-mail-password', [UserController::class, 'recSendMailPassword'])->name('rec-send-mail-password');
    Route::get('/rec/form-reset-password', [UserController::class, 'recFormResetPassword'])->name('rec-form-reset-password');
    Route::get('/forgot-password', [UserController::class, 'recForgotPassword'])->name('rec-forgot-password');
    Route::post('/reset-password', [UserController::class, 'recResetPassword'])->name('rec-reset-password');
    Route::get('/logout', [UserController::class, 'recLogout'])->name('rec-logout');

    Route::get('/redirect/{social}', [UserController::class, 'redirect'])->name('rec-login-social');;
    Route::get('/callback/{social}', [UserController::class, 'callback']);

    Route::get('/affi/{referral_code}', [UserController::class, 'recRegisterFromAffi'])->name('rec-register-from-affi');
    Route::post('/affi/post', [UserController::class, 'recPostLoginAffi'])->name('rec-register-from-affi-post');

    Route::middleware(['check-rec'])->group(function () {
        Route::prefix('rec')->group(function () {
            Route::get('/', [RecController::class, 'index'])->name('rec-dashboard');
            Route::get('profile', [RecController::class, 'profile'])->name('rec-profile');
            Route::post('update-profile', [RecController::class, 'profilePost'])->name('rec-update-profile');
            Route::post('update-bankinfo', [RecController::class, 'updateBankInfo'])->name('rec-update-bankinfo');
            Route::post('ajax-update-bankinfo', [RecController::class, 'ajaxUpdateBankInfo'])->name('ajax-rec-update-bankinfo');
            //tab change password
            Route::post('collaborator-change-password', [RecController::class, 'changePassword'])->name('rec-change-password');
            //Quan ly gioi thieu ung vien
            Route::get('submitcv', [RecController::class, 'submitCv'])->name('rec-submitcv');
            Route::get('cv-submit-detail/{id}', [RecController::class, 'getDetailCvSubmit'])->name('rec-detail-cv-submit');
            //HỦy ứng tuyển
            Route::get('submitcv/cancel-status/{id}', [RecController::class, 'cancelStatusSubmitCv'])->name('rec-submitcv-cancel');
            //Candidate hủy ứng tuyển với hoàn tiền
            Route::post('submitcv/candidate-cancel/{id}', [RecController::class, 'candidateCancelSubmitCv'])->name('rec-candidate-cancel-submit');
            //Chi tiet ung tuyen gioi thieu ung vien
            Route::get('submitcv/detail/{id}', [RecController::class, 'detailSubmitCv'])->name('rec-submitcv-detail');
            //Quan ly kho cv
            Route::get('warehousecv', [RecController::class, 'warehouseCv'])->name('rec-warehousecv');
            //Them moi ứng vien
            Route::get('warehousecv/regist', [RecController::class, 'registWarehousecv'])->name('rec-warehousecv-regist');
            //Them moi ứng vien submit
            Route::post('warehousecv/regist', [RecController::class, 'registWarehousecvPost'])->name('rec-warehousecv-regist-post');
            //edit CV
            Route::get('warehousecv/edit/{id}', [RecController::class, 'editWarehouseCv'])->name('rec-warehousecv-edit');
            Route::post('warehousecv/edit/{id}', [RecController::class, 'editWarehouseCvPost'])->name('rec-warehousecv-submit');
            Route::get('warehousecv/get-data/{id}', [RecController::class, 'getWarehouseCv'])->name('getdata-warehousecv');
            // Upload multiple files route
            Route::post('warehousecv/upload-multiple-files', [RecController::class, 'uploadMultipleFiles'])->name('rec-upload-multiple-files');
            //danh sách hỏi đáp CTV của : CV đang bán
            Route::get('warehousecv-selling-qa/get-data/{id}', [RecController::class, 'getWarehouseCvSellingQa'])->name('getdata-warehousecv-selling-qa');
            Route::get('warehousecv-selling-qa-dl/delete/{id}', [RecController::class, 'getWarehouseCvSellingDeleteQa'])->name('warehousecv-selling-qa-delete');
            Route::post('warehousecv-selling-qa-send/send', [RecController::class, 'getWarehouseCvSellingSendQa'])->name('warehousecv-selling-qa-send');
            Route::post('warehousecv-selling-qa-ctv/comment', [RecController::class, 'getWarehouseCvSellingSendQaComment'])->name('warehousecv-selling-qa-ctv-comment');

            // Job đã save
            Route::get('saved-jobs', [RecController::class, 'savedJobs'])->name('rec-saved-jobs');

            Route::get('turnovers', [RecController::class, 'turnovers'])->name('rec-turnovers');
            Route::get('team-manager', [RecController::class, 'teamManager'])->name('rec-team-manager');

            Route::get('teams', [RecController::class, 'teams'])->name('rec-teams');
            Route::get('teams/{id}', [RecController::class, 'detailUserTeam'])->name('rec-detail-user-team');
            Route::get('ajax-search-skill', [RecController::class, 'searchSkill'])->name('rec-ajax-search-skill');

            Route::get('cv-selling', [RecController::class, 'getCvSelling'])->name('rec-cv-selling');
            Route::post('cv-selling', [RecController::class, 'postCvSelling'])->name('rec-cv-selling-post');
            Route::get('cv-selling-cancel/{id}', [RecController::class, 'cancelCvSelling'])->name('rec-cv-selling-cancel');
            Route::get('cv-selling/{id}', [RecController::class, 'getDetailCvSelling'])->name('rec-detail-cv-selling');

            Route::get('cv-sold', [RecController::class, 'getCvSold'])->name('rec-cv-sold');
            Route::get('cv-sold/{id}', [RecController::class, 'getDetailCvSold'])->name('rec-detail-cv-sold');
            Route::get('discusses/{id}', [RecController::class, 'discusses'])->name('rec-discusses');
            Route::get('discusses-submit/{id}', [RecController::class, 'discussesSubmit'])->name('rec-discusses-submit');
            Route::post('send-discusses', [RecController::class, 'sendDiscusses'])->name('rec-send-discusses');
            Route::post('send-discusses-submit', [RecController::class, 'sendDiscussesSubmit'])->name('rec-send-discusses-submit');
            Route::post('complain-status', [RecController::class, 'complainStatus'])->name('rec-complain-status');
            Route::post('complain-status-submit', [RecController::class, 'complainStatusSubmit'])->name('rec-complain-status-submit');
            Route::post('book-status', [RecController::class, 'bookStatus'])->name('rec-book-status');
            Route::post('book-status-submit', [RecController::class, 'bookStatusSubmit'])->name('rec-book-status-submit');
            Route::post('onboard-status', [RecController::class, 'onboardStatus'])->name('rec-onboard-status');
            Route::post('onboard-status-submit', [RecController::class, 'onboardStatusSubmit'])->name('rec-onboard-status-submit');
            Route::get('revenue/{month}/{year}', [RecController::class, 'revenue'])->name('rec-revenue');
            Route::post('request-withdraw', [RecController::class, 'requestWithdraw'])->name('ajax-request-withdraw');
            Route::get('history-withdraw', [RecController::class, 'historyWithdraw'])->name('history-withdraw');
            Route::get('ajax-payout-detail/{id}', [RecController::class, 'payoutDetail'])->name('ajax-payout-detail');
            Route::post('collaborator/survey',  [RecController::class, 'saveSurvey'])->name('frontend.auth.collaborator.survey');
        });
    });
    //end CTV

    //NTD
    //    Route::get('/employer/login', [UserController::class, 'employerLogin'])->name('employer-login');
    Route::post('/employer/login', [UserController::class, 'employerPostLogin'])->name('employer-post-login');
    Route::post('/employer/login-ajax', [UserController::class, 'employerPostLoginAjax'])->name('employer-post-login-ajax');
    Route::get('/employer/forgot-password', [UserController::class, 'employerShowSendEmailForgotPassword'])->name('employer.send-email-forgot-password');
    Route::post('/employer/forgot-password', [UserController::class, 'employerSubmitSendEmailForgotPassword'])->name('employer.send-email-forgot-password-submit');
    Route::post('/employer/submit-reset-password', [UserController::class, 'employerSubmitResetPassword'])->name('employer-submit-reset-password');
    Route::get('/employer/form-reset-password', [UserController::class, 'employerFormResetPassword'])->name('employer-form-reset-password');
    //    Route::get('/employer/login', [UserController::class, 'employerLogin'])->name('employer-login');
    Route::get('/employer/register', [UserController::class, 'employerRegister'])->name('employer-register');
    Route::post('/employer/register', [UserController::class, 'employerPostRegister'])->name('employer-post-register');
    Route::post('/employer/register-ajax', [UserController::class, 'employerPostRegisterAjax'])->name('employer-post-register-ajax');

    Route::post('/employer/contact-uses', [\App\Http\Controllers\Frontend\ContactUsesController::class, 'contactEmployerUses'])->name('client.contactEmployerUses');
    Route::get('/employer/register-success', [UserController::class, 'employerRegisterSuccess'])->name('employer-success');
    Route::post('/employer/send-mail-password', [UserController::class, 'employerSendMailPassword'])->name('employer-send-mail-password');
    Route::post('/employer/reset-password', [UserController::class, 'employerResetPassword'])->name('employer-reset-password');
    Route::get('landing-page-demo', [UserController::class, 'landingPageDemo'])->name('landing-page-demo');
    Route::get('/employer/logout', [UserController::class, 'employerLogout'])->name('employer-logout');
    Route::get('/employer', [EmployerController::class, 'index'])->name('employer-dashboard');
    Route::get('/employer/dang-ky-ngay', [App\Http\Controllers\Frontend\EmployerController::class, 'registerLanding'])->name('employer-landing-register');
    Route::middleware(['check-employer'])->group(function () {
        Route::prefix('employer')->group(function () {
            Route::get('job', [EmployerController::class, 'listJob'])->name('employer-job');
            Route::get('job/create', [EmployerController::class, 'createJob'])->name('employer-create');
            Route::post('job/store', [EmployerController::class, 'storeJob'])->name('employer-store');
            Route::get('job/edit/{id}', [EmployerController::class, 'editJob'])->name('employer-edit');
            Route::put('job/update/{id}', [EmployerController::class, 'updateJob'])->name('employer-update');
            Route::get('job/submitcv/{slug}', action: [EmployerController::class, 'submitcvByEmployer'])->name('employer-submitcv-by-job');
            Route::get('submitcv', [EmployerController::class, 'submitcvByEmployer'])->name('employer-submitcv');
            //Chi tiet ung tuyen gioi thieu ung vien
            Route::get('submitcv/detail/{id}', [EmployerController::class, 'detailSubmitCv'])->name('employer-submitcv-detail');
            Route::get('submitcv/status/{id}', [EmployerController::class, 'detailStatusSubmitCv'])->name('employer-submitcv-status-detail');
            Route::post('submitcv/change-status', [EmployerController::class, 'changeStatusSubmitCv'])->name('employer-submitcv-status-change');
            Route::get('dashboard', [EmployerController::class, 'dashboard'])->name('employer-manager-dashboard');
            Route::get('company-profile', [EmployerController::class, 'companyProfile'])->name('employer-company-profile');
            Route::post('change-password', [EmployerController::class, 'changePassword'])->name('employer-change-password');
            Route::post('change-user', [EmployerController::class, 'changeUser'])->name('employer-change-user');
            Route::post('change-info-company', [EmployerController::class, 'changeInfoCompany'])->name('employer-change-info-company');

            Route::get('users', [EmployerController::class, 'users'])->name('employer-users');
            Route::post('users', [EmployerController::class, 'createUsers'])->name('employer-users-create');
            Route::post('users/confirm-policy', [EmployerController::class, 'confirmPolicy'])->name('frontend.auth.employer.confirm');
            Route::get('users-role', [EmployerController::class, 'usersRole'])->name('employer-users-role');

            Route::post('buy-cv', [AjaxController::class, 'buyCV'])->name('ajax-buy-cv');
            Route::post('payment-submit-cv', [AjaxController::class, 'paymentSubmitCv'])->name('ajax-payment-submit-cv');
            Route::post('evaluate-cv', [AjaxController::class, 'evaluateCV'])->name('ajax-evaluate-cv');
            Route::get('evaluate-cv/{id}', [AjaxController::class, 'getEvaluateCV'])->name('ajax-get-evaluate-cv');
            Route::get('faq-cv/{id}', [AjaxController::class, 'getFaqCV'])->name('ajax-get-faq-cv');
            Route::post('faq-cv', [AjaxController::class, 'faqCV'])->name('ajax-faq-cv');
            Route::post('save-cv', [AjaxController::class, 'saveCV'])->name('ajax-save-cv');
            Route::post('un-save-cv', [AjaxController::class, 'unSaveCV'])->name('ajax-un-save-cv');

            Route::get('check-cv-before-buy/{id}', [AjaxController::class, 'checkCvBeforeBuy'])->name('ajax-check-cv-before-buy');
            Route::get('check-job-before-buy/{id?}', [AjaxController::class, 'checkJobBeforeBuy'])->name('ajax-check-job-before-buy');
            Route::get('get-submit-price-popup/{id}', [AjaxController::class, 'getSubmitPricePopup'])->name('ajax-get-submit-price-popup');
            Route::get('check-job-before-buy', [AjaxController::class, 'checkJobBeforeBuy'])->name('ajax-check-job-before-buy');


            Route::get('cv-bought', [EmployerController::class, 'cvBought'])->name('employer-cv-bought');
            Route::get('download', [DownloadFileController::class, 'download'])->name('employer-download-file');
            Route::get('discusses/{id}', [EmployerController::class, 'discusses'])->name('employer-discusses');
            Route::get('discusses-submit/{id}', [EmployerController::class, 'discussesSubmit'])->name('employer-discusses-submit');
            Route::post('send-discusses', [EmployerController::class, 'sendDiscusses'])->name('employer-send-discusses');
            Route::post('send-discusses-submit', [EmployerController::class, 'sendDiscussesSubmit'])->name('employer-send-discusses-submit');
            Route::post('schedule-interview', [EmployerController::class, 'scheduleInterview'])->name('employer-schedule-interview');
            Route::post('schedule-interview-submit', [EmployerController::class, 'scheduleInterviewSubmit'])->name('employer-schedule-interview-submit');
            Route::post('schedule-onboard', [EmployerController::class, 'scheduleOnboard'])->name('employer-schedule-onboard');
            Route::post('schedule-onboard-submit', [EmployerController::class, 'scheduleOnboardSubmit'])->name('employer-schedule-onboard-submit');
            Route::post('complain', [EmployerController::class, 'complain'])->name('employer-complain');
            Route::post('complain-submit', [EmployerController::class, 'complainSubmit'])->name('employer-complain-submit');
            Route::get('view-popup-change-status/{id}', [EmployerController::class, 'viewPopupChangeStatus'])->name('employer-view-popup-change-status');
            Route::get('view-popup-change-status-submit/{id}', [EmployerController::class, 'viewPopupChangeStatusSubmit'])->name('employer-view-popup-change-status-submit');
            Route::post('warehouse-cv-selling-buys-change-status-recruitment', [EmployerController::class, 'warehouseCvSellingBuyChangeStatus'])->name('warehouse-cv-selling-buys-change-status-recruitment');
            Route::post('submit-cv-change-status-recruitment', [EmployerController::class, 'submitCvChangeStatus'])->name('submit-cv-change-status-recruitment');
            Route::get('payment-history', [EmployerController::class, 'paymentHistory'])->name('employer-payment-history');
            Route::get('payment-info', [EmployerController::class, 'paymentInfo'])->name('employer-payment-info');

            Route::get('wallet', [EmployerController::class, 'wallet'])->name('employer-wallet');
            Route::post('deposit', [EmployerController::class, 'deposit'])->name('employer-deposit');
            Route::post('cancel-interview', [EmployerController::class, 'cancelInterview'])->name('cancel-interview');
            Route::post('cancel-interview-submit', [EmployerController::class, 'cancelInterviewSubmit'])->name('cancel-interview-submit');
        });
    });
    Route::get('employer/market', [WareHouseSubmitCvController::class, 'market'])->name('market-cv');


    Route::get('blog', [PostController::class, 'index'])->name('blog-index');
    Route::get('sub-category', [PostController::class, 'subCategory'])->name('sub-category');
    Route::get('blog-search', [PostController::class, 'searchBlog'])->name('blog-search');
    Route::get('blog-list/search/{keyword}', [PostController::class, 'blogListSearch'])->name('blog-list-search');
    Route::get('blog-list/{slug?}', [PostController::class, 'listPosts'])->name('blog-list');
    Route::get('blog/{slug}', [PostController::class, 'detail'])->name('blog-detail');
    Route::get('job', [JobController::class, 'index'])->name('job-index');
    Route::get('job-search-ajax', [JobController::class, 'searchJob'])->name('job-search-ajax');
    Route::get('job/detail/{slug}', [\App\Http\Controllers\Api\Frontend\JobController::class, 'detailJob'])->name('job.detailJob');
    Route::get('job-count-search-ajax', [JobController::class, 'countSearchJob'])->name('job-count-search-ajax');
    Route::get('job/{slug}', [JobController::class, 'show'])->name('job-detail');
    Route::get('ajax/job/{slug}', [JobController::class, 'ajaxDetailJob'])->name('job-detail-ajax');
    Route::post('user-job-care/change', [UserJobCareController::class, 'change'])->name('user-job-care-change');

    //test gioi thieu ứng viên
    Route::post('save-cv-submit', [WareHouseSubmitCvController::class, 'index'])->name('user-save-cv-submit');
    Route::post('user-candidate-introduction', [WareHouseSubmitCvController::class, 'candidateIntroduction'])->name('user-candidate-introduction');
    Route::get('get-cv-by-user', [WareHouseSubmitCvController::class, 'getWareHouseCvByCtv'])->name('get-cv-by-user-submit');
    Route::post('get-cv-by-id', [WareHouseSubmitCvController::class, 'getWareHouseCvById'])->name('get-cv-by-id');
    Route::post('upload-public-cv', [WareHouseSubmitCvController::class, 'uploadPublicCv'])->name('upload-public-cv');
    Route::post('re-upload-public-cv', [WareHouseSubmitCvController::class, 'reUploadPublicCv'])->name('re-upload-public-cv');
    Route::post('upload-private-cv', [WareHouseSubmitCvController::class, 'uploadPrivateCv'])->name('upload-private-cv');
    Route::post('upload-private-cv-for-submit-cv', [WareHouseSubmitCvController::class, 'uploadPrivateCvForSubmitCv'])->name('upload-private-cv-for-submit-cv');
    Route::get('get-cv-selling-by-user', [WareHouseSubmitCvController::class, 'getWareHouseCvSelling'])->name('get-cv-selling-by-user');


    // lưu trữ tạm nạp tiền
    Route::prefix('storage-recharge')->group(function () {
        Route::post('/create', [\App\Http\Controllers\Frontend\ZalopayTransactionController::class, 'store'])->name('storage-recharge-store');
    });




    Route::prefix('ajax')->group(function () {
        Route::post('update-status-notification', [UserController::class, 'ajaxUpdateNoti'])->name('ajax-update-noti');
        Route::get('list-job', [AjaxController::class, 'ajaxListJob'])->name('ajax-list-job');
        Route::get('ajax-get-job', [AjaxController::class, 'getJob'])->name('ajax-get-job');
        Route::get('list-skill', [AjaxController::class, 'ajaxListSkill'])->name('ajax-list-skill');
        Route::get('list-company', [AjaxController::class, 'ajaxListCompany'])->name('ajax-list-company');
        Route::get('detail-company/{id}', [AjaxController::class, 'ajaxDetailCompany'])->name('ajax-detail-company');
        Route::get('update-status-submitcv/{id}', [AjaxController::class, 'ajaxUpdateSubmitcv'])->name('ajax-update-submit-cv');
        Route::post('update-all-notification', [AjaxController::class, 'ajaxUpdateAllNoti'])->name('ajax-update-all-noti');
        Route::post('check-email-candidate', [AjaxController::class, 'ajaxCheckEmailCandidate'])->name('ajax-check-email-candidate');
        Route::post('check-candidate', [AjaxController::class, 'ajaxCheckCandidate'])->name('ajax-check-candidate');
        Route::post('check-duplicate-submit-by-cv', [AjaxController::class, 'ajaxCheckDuplicateSubmitByCv'])->name('ajax-check-duplicate-submit-by-cv');
        Route::post('check-duplicate-submit-by-new-info', [AjaxController::class, 'ajaxCheckDuplicateSubmitByNewInfo'])->name('check-duplicate-submit-by-new-info');
        Route::get('submitcv-count-search-ajax', [AjaxController::class, 'ajaxSubmitCvCountSearch'])->name('submitcv-count-search-ajax');
        Route::get('warehouse-count-search-ajax', [AjaxController::class, 'ajaxWareHouseCountSearch'])->name('warehouse-count-search-ajax');
        Route::post('change-role/{id}', [AjaxController::class, 'ajaxChangeRole'])->name('change-role');
        Route::post('change-active/{id}', [AjaxController::class, 'ajaxChangeActive'])->name('change-active');
        Route::delete('role/{id}', [AjaxController::class, 'ajaxDeleteRole'])->name('delete-role');
        Route::get('role/{id}', [AjaxController::class, 'ajaxGetRole'])->name('get-role');
        Route::post('role/{id}', [AjaxController::class, 'ajaxUpdateRole'])->name('update-role');
        Route::post('role', [AjaxController::class, 'ajaxAddRole'])->name('add-role');
        Route::post('check-email-user-type', [AjaxController::class, 'ajaxCheckEmailUserType'])->name('ajax-check-email-user-type');
        Route::post('resend-mail', [AjaxController::class, 'ajaxResendMail'])->name('ajax-resend-mail');

        Route::post('check-validate-cv', [AjaxController::class, 'ajaxValidateCv'])->name('ajax-check-candidate-cv');
        Route::post('check-validate-cv-email', [AjaxController::class, 'checkValidateCvEmail'])->name('ajax-check-cv-email');

        Route::get('get-skill-main-it', [AjaxController::class, 'getSkillMainIT'])->name('ajax-get-skill-main-it');
        Route::get('get-level', [AjaxController::class, 'getLevel'])->name('ajax-get-level');
        Route::get('get-level-by-career', [AjaxController::class, 'getLevelByCareer'])->name('ajax-get-level-by-career');
        Route::get('get-price', [AjaxController::class, 'getPrice'])->name('ajax-get-price');
        Route::get('get-min-submit-price', [AjaxController::class, 'getMinSubmitPrice'])->name('ajax-get-min-submit-price');
        Route::get('check-cv-selling-with-type', [AjaxController::class, 'checkCvSellingWithType'])->name('ajax-check-cv-selling-with-type');
        Route::get('get-detail-cv-selling/{id}', [AjaxController::class, 'getDetailCvSelling'])->name('ajax-get-detail-cv-selling');
        Route::get('get-submit-price-popup/{id}', [AjaxController::class, 'getSubmitPricePopup'])->name('ajax-get-submit-price-popup');
        Route::get('get-date-detail-cv-selling-buy/{id}', [AjaxController::class, 'getDataDetailCvSellingBuy'])->name('ajax-get-data-detail-cv-selling-buy');
        Route::get('get-date-detail-submit-cv/{id}', [AjaxController::class, 'getDataDetailSubmitCv'])->name('ajax-get-data-detail-submit-cv');
        Route::post('ajax-recuriter-reject-cv/{id}', [AjaxController::class, 'ajaxRecruiterRejectCv'])->name('ajax-recuriter-reject-cv');

        Route::get('list-skill', [RecController::class, 'searchSkill'])->name('ajax-list-skill');
    });

    Route::get('about-us', [OtherController::class, 'aboutUs'])->name('about-us');
    Route::get('/contact-us', [OtherController::class, 'contactUs'])->name('contact-us');
    Route::post('/contact-uses', [\App\Http\Controllers\Frontend\ContactUsesController::class, 'contactUses'])->name('client.contactUses');
    Route::get('company', [CompanyController::class, 'listCompany'])->name('list-company');
    Route::get('filter-company', [CompanyController::class, 'filterCompany'])->name('filter-company');
    Route::get('company/{slug}', [CompanyController::class, 'detailCompany'])->name('detail-company');
    Route::get('verify-employer-invite', [EmployerController::class, 'verifyEmployerInvite'])->name('verify-employer-invite');
    Route::post('verify-employer-invite', [EmployerController::class, 'registerEmployerInvite'])->name('register-employer-invite');
    Route::get('/confirm-candidate/{candidateId}/{jobId}', [ConfirmCandidateController::class, 'index'])->name('confirm-candidate');

    Route::get('approve', [OtherController::class, 'approveCandidate'])->name('approve-candidate');
    Route::get('reject', [OtherController::class, 'rejectCandidate'])->name('reject-candidate');
    Route::get('verify-email-candidate', [AjaxController::class, 'verifyEmailCandidate'])->name('verify-email-candidate');
    Route::get('verify-email-candidate-submit-cv', [AjaxController::class, 'verifyEmailCandidateSubmitCv'])->name('verify-email-candidate-submit-cv');

    Route::post('report', [OtherController::class, 'report'])->name('report');
    Route::post('job/comments', [\App\Http\Controllers\Api\Frontend\JobCommentController::class, 'store']);
    Route::get('job/comments/{jobId}', [\App\Http\Controllers\Api\Frontend\JobCommentController::class, 'index']);
    Route::get('rec/apply-list-in-team', [\App\Http\Controllers\Api\Frontend\RecController::class, 'applyListInTeam']);

    Route::post('/job/api/list', [\App\Http\Controllers\Api\Frontend\JobController::class, 'list'])->name('job.api.list');
    Route::post('/job/api/list-search', [\App\Http\Controllers\Api\Frontend\JobController::class, 'listAllJob'])->name('job.api.listAllJob');
});
Route::post('/zalopay/callback', [\App\Http\Controllers\Frontend\ZalopayTransactionController::class, 'zalopayCallback'])->name('zalopay-callback');
Route::get('/zalopay/process-after-payment', [\App\Http\Controllers\Frontend\ZalopayTransactionController::class, 'zalopayProcessAfterPayment'])->name('zalopay-process-after-payment');

//download pdf
Route::get('/download-pdf/{slug}', [\App\Http\Controllers\Frontend\PdfController::class, 'downloadPDF'])->name('download.pdf');


Route::get('/html/home-ntd', [HtmlController::class, 'homeNtd']);
Route::get('/html/home-ctv', [HtmlController::class, 'homeCtv']);
Route::get('/html/list-job', [HtmlController::class, 'listJob']);
Route::get('/html/list-new', [HtmlController::class, 'listNew']);
Route::get('/html/no-job', [HtmlController::class, 'noJob']);
Route::get('/html/new-detail', [HtmlController::class, 'newDetail']);
Route::get('/html/profile-ctv', [HtmlController::class, 'profileCtv']);
Route::get('/html/dashboard-ctv', [HtmlController::class, 'dashboardCtv']);
Route::get('/html/candidate-introduction-manager', [HtmlController::class, 'candidateIntroductionManager']);
Route::get('/html/dashboard-storage', [HtmlController::class, 'dashboardStorage']);
Route::get('/html/dashboard-storage-edit', [HtmlController::class, 'dashboardStorageEdit']);
Route::get('/html/dashboard-list-job', [HtmlController::class, 'dashboardListJob']);
Route::get('/html/dashboard-edit-job', [HtmlController::class, 'dashboardEditJob']);
Route::get('/html/dashboard-list-recruitment', [HtmlController::class, 'dashboardListRecruitment']);
Route::get('/html/dashboard-info-company', [HtmlController::class, 'dashboardInfoCompany']);
Route::get('/html/about-us', [HtmlController::class, 'aboutUs']);
Route::get('/html/contact-us', [HtmlController::class, 'contactUs']);
Route::get('/html/privacy-policy', [HtmlController::class, 'privacyPolicy']);
Route::get('/html/email', [HtmlController::class, 'email']);
Route::get('/html/search-company', [HtmlController::class, 'searchCompany']);
Route::get('/html/search-company2', [HtmlController::class, 'searchCompany2']);
Route::get('/html/detail-company', [HtmlController::class, 'detailCompany']);
Route::get('/html/member-manager', [HtmlController::class, 'memberManager']);
Route::get('/html/list-member', [HtmlController::class, 'listMember']);
Route::get('/html/employer-register', [HtmlController::class, 'employerRegister']);
Route::get('/html/candidate', [HtmlController::class, 'listCandidate']);
Route::get('/html/cv-on-sale', [HtmlController::class, 'cvOnSale']);
Route::get('/html/cv-sales', [HtmlController::class, 'cvSales']);
Route::get('/html/cv-bought', [HtmlController::class, 'cvBought']);
Route::get('/html/wallet-detail', [HtmlController::class, 'walletDetail']);
Route::get('/html/approve-candidate', [HtmlController::class, 'approveCandidate']);
Route::get('/html/wallet-rec', [HtmlController::class, 'walletRec']);
Route::get('/html/wallet-employer', [HtmlController::class, 'walletEmployer']);
Route::get('/html/wallet-payment', [HtmlController::class, 'historyPayment']);

// Bug Report Routes
// Route::middleware('auth')->group(function () {
Route::post('/bug-report', [BugReportController::class, 'store'])->name('bug-report.store');
// });
