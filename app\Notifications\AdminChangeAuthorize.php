<?php

namespace App\Notifications;

use App\Helpers\Common;
use App\Services\Frontend\SettingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class AdminChangeAuthorize extends Notification implements ShouldQueue
{
    use Queueable;

    protected $user;
    protected $authorize;
    protected $submitCv;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($user, $authorize, $submitCv)
    {
        $this->user = $user;
        $this->authorize = $authorize;
        $this->submitCv = $submitCv;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     *
     * @return array
     */
    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     *
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $name = $this->user->name;
        $candidateName = $this->submitCv->warehouseCv->candidate_name;

        if ($this->authorize == 1) {
            return (new MailMessage)
                ->view('email.adminAcceptAuthorize', ['name' => $name, 'candidateName' => $candidateName])
                ->subject('[Recland] [ỦY QUYỀN THÀNH CÔNG]');
        } else {
            return (new MailMessage)
                ->view('email.adminRejectAuthorize', ['name' => $name, 'candidateName' => $candidateName])
                ->subject('[Recland] [TỪ CHỐI ỦY QUYỀN]');
        }
    }

    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     *
     * @return array
     */
    public function toArray($notifiable)
    {
        $settings = resolve(SettingService::class);

        $str = 'notification';

        $arrLangVi = $settings->getAllKeyWithQuery($str, config('constant.language.vi'));
        $arrLangEn = $settings->getAllKeyWithQuery($str, config('constant.language.en'));

        if ($this->authorize == 1) {
            $contentVi = Common::transLang($arrLangVi['ctv_authorize_accept'], ['name' => $this->submitCv->warehouseCv->candidate_name]);
            $contentEn = Common::transLang($arrLangEn['ctv_authorize_accept'], ['name' => $this->submitCv->warehouseCv->candidate_name]);
        } else {
            $contentVi = Common::transLang($arrLangVi['ctv_authorize_reject'], ['name' => $this->submitCv->warehouseCv->candidate_name]);
            $contentEn = Common::transLang($arrLangEn['ctv_authorize_reject'], ['name' => $this->submitCv->warehouseCv->candidate_name]);
        }

        return [
            'content_vi' => $contentVi,
            'content_en' => $contentEn,
        ];
    }
}
