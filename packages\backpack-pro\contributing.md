# Contributing

Contributions are welcome and will be fully credited:
- code contributions are accepted via Pull Requests to [this Github repo](https://github.com/backpack/pro);
- financial contributions are usually in the form of [donations](), [projects]() or [jobs](); 
- documentation contributions are accepted via Pull Requests to [this Github repo](https://github.com/backpack/pro);
- other contributions are most likely welcome in the way you see fit;

# Things you could do
If you want to contribute but do not know where to start, this list provides some starting points.
- Add license text
- Remove rewriteRules.php
- Set up TravisCI, StyleCI, ScrutinizerCI
- Write a comprehensive ReadMe
- improve [our documentation]();
- provide more examples;
- review and test [existing PRs]();
- review [bug reports]();
- offer to code [approved feature requests]();
- answer [support requests]();


## How to do those things

### Pull Requests
- **Add tests!** - Your patch will be accepted MUCH faster if it has tests.
- **Document any change in behaviour** - Make sure the `readme.md` and any other relevant documentation are kept up-to-date.
- **Consider our release cycle** - We try to follow [SemVer v2.0.0](http://semver.org/). Randomly breaking public APIs is not an option.
- **One pull request per feature** - If you want to do more than one thing, send multiple pull requests.
- **Send coherent history** - Make sure each individual commit in your pull request is meaningful. If you had to make multiple intermediate commits while developing, please [squash them](http://www.git-scm.com/book/en/v2/Git-Tools-Rewriting-History#Changing-Multiple-Commit-Messages) before submitting.

### Issues
- **Answer all items in the issue template** - It helps us resolve problems without a lot of back-and-forth.
- **No support requests** - The maintainers and people who follow this project are all busy people. Please do not expect them to help you code something that will only work inside your project or debug an issue that is most likely your own doing. 

### Rules for writing effectively

This project stands by [The Whole Fruit Manifesto](https://github.com/the-whole-fruit/manifesto). We believe that “_writing good code_” is not _only_ about “_writing good code_”. It’s also about the words around it. That’s why, to make sure your contribution is well received, we ask you to [read and apply the ONE=MOR framework and guidelines](https://github.com/the-whole-fruit/manifesto) when writing comment blocks, PR titles, PR descriptions, and in general, when writing to our community.


Happy coding!