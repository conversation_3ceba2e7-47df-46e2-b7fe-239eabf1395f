<!DOCTYPE html>
<html lang="vi" xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport"
        content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1, user-scalable=yes">
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-title" content="" />
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <?php echo SEOMeta::generate(); ?>

    <?php echo OpenGraph::generate(); ?>

    <?php if(isset($page_image_url)): ?>
    <meta property="og:image" content="<?php echo e($page_image_url); ?>" />
    <?php else: ?>
    <meta property="og:image" content="<?php echo e(config('settings.global.page_image_url')); ?>" />
    <?php endif; ?>
    <link type="image/x-icon" href="<?php echo e(gen_url_file_s3(config('settings.global.img_favicon'))); ?>" rel="shortcut icon" />
    <link rel="stylesheet" href="<?php echo e(asset2('frontend/assets_v2/css/all.css')); ?>" media="all" />
    <!-- build:head2 -->
    <!-- build:head -->
    <?php echo $__env->yieldPushContent('style'); ?>
    <?php echo $__env->yieldPushContent('scripts_head'); ?>

    <?php echo $__env->make('frontend.inc_layouts.v2.header_script', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
</head>

<body class="">

    <?php echo $__env->make('frontend.inc_layouts.v2.home_header', [
    'topnav_class' =>
    Route::current()->getName() == 'home'
    ? 'home'
    : (in_array(Route::current()->getName(), [
    'employer-dashboard',
    'employer-register',
    'employer.send-email-forgot-password',
    'employer-form-reset-password',
    'rec-show-forgot-password',
    'rec-form-reset-password',
    ])
    ? 'home sticky'
    : ''),
    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <div id="app">
        <?php echo $__env->yieldContent('content'); ?>
    </div>

    <!--Footer-->
    <?php echo $__env->make('frontend.inc_layouts.v2.hh_footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <!--End Footer-->

    <!-- Bug Report Modal -->
    <?php if(auth()->guard('client')->check()): ?>
    <?php echo $__env->make('frontend.partials.bug_report_modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php endif; ?>

    <div class="overlay"></div>
    <!--Jquery lib-->
    <?php echo $__env->yieldPushContent('scripts'); ?>
    <script>
        window.default_locale = "<?php echo e(app()->getLocale()); ?>"; //current language set by default
        window.fallback_locale = "<?php echo e(config('constant.language.vi')); ?>";
    </script>
    <script type="text/javascript" src="<?php echo e(asset2('frontend/assets_v2/js/all.js')); ?>"></script>
    <?php if(use_vue_in_route()): ?>
    <script src="<?php echo e(mix('js/app.js')); ?>"></script>
    <?php endif; ?>
    
    <!-- build:script -->

    <script>
        // $('.topnav').addClass('home');
    </script>
    <?php echo $__env->yieldPushContent('after_styles'); ?>
    <?php echo $__env->yieldContent('after_styles'); ?>
    <?php echo $__env->yieldPushContent('after_scripts'); ?>
    <?php echo $__env->yieldContent('after_scripts'); ?>
    <div style="position:fixed;top:0px;left:0px;width:0;height:0;" id='scrollzipPoint'></div>
    <?php if(Session::has('message')): ?>
    <script>
        $(document).ready(function() {
                toastr.success('<?php echo e(Session('message')); ?>');
            });
    </script>
    <?php endif; ?>
    <?php if(Session::has('error')): ?>
    <script>
        $(document).ready(function() {
                toastr.error('<?php echo e(Session('error')); ?>');
            });
    </script>
    <?php endif; ?>
    <?php echo App\Services\Admin\Toast\Facades\Toast::message(); ?>

    <?php echo $__env->make('frontend.inc_layouts.v2.contactus', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    
    <script>
        $(document).ready(function() {
            <?php if(request()->get('msg') == 'register_success'): ?>
                swal({
                    title: 'Đăng ký thành công',
                    text: 'Bạn đã đăng ký thành công, vui lòng kiểm tra email để xác thực tài khoản',
                    icon: 'success',
                    button: 'Đóng'
                });
                <?php endif; ?>
            });
    </script>
    
</body>

</html><?php /**PATH D:\Projects\HRI\RecLand\resources\views/frontend/layouts/v2.blade.php ENDPATH**/ ?>