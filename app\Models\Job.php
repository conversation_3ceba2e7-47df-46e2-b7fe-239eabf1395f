<?php

namespace App\Models;

use App\Helpers\Common;
use App\Services\Frontend\JobService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use OwenIt\Auditing\Contracts\Auditable;
use Backpack\CRUD\app\Models\Traits\CrudTrait;
use Zoha\Metable;

class Job extends BaseModel implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    use HasFactory;
    use CrudTrait;
    use Metable;

    protected $table = 'job';
    public $timestamps = true;
    protected $guarded = ['id'];
    protected $appends = ['full_path_slug', 'file_job'];
    protected $audit_tags = [];

    public function addAuditTag($tag)
    {
        if (is_array($tag)) {
            $this->audit_tags = array_merge($this->audit_tags, $tag);
        } else {
            $this->audit_tags[] = $tag;
        }
    }
    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'employer_id', 'id');
    }

    public function jobMeta()
    {
        return $this->hasOne(JobMeta::class, 'job_id', 'id');
    }

    public function jobSeo()
    {
        return $this->hasOne(JobSeo::class, 'job_id', 'id');
    }

    public function submitCv()
    {
        return $this->hasMany(SubmitCv::class, 'job_id', 'id');
    }

    public function ntdGetSubmitCount()
    {
        $notin_status = config('constant.status_recruitment.ntd_not_display');
        return $this->submitCv()->whereNotIn('status', $notin_status)->count();
    }

    public function getAddressValueAttribute()
    {
        return json_decode($this->address);
    }

    public function getExpireAtValueAttribute()
    {
        return Carbon::createFromFormat('Y-m-d', $this->expire_at)->format('d/m/Y');
    }

    public function getCareerValueAttribute()
    {
        $lang = app()->getLocale();
        $career = config('job.career.' . $lang);
        $careerArr = explode(',', $this->career);
        $careers = [];
        foreach ($careerArr as $item) {
            if (isset($career[$item])) {
                $careers[] = $career[$item];
            }
        }
        return $careers;
    }

    public function getBonusForCtvAttribute()
    {
        $jobService = resolve(JobService::class);
        if ($this->manual_bonus_for_ctv) {
            return $this->manual_bonus_for_ctv;
        }
        return $jobService->calculateBonusForCtv($this->bonus, $this->bonus_type);
    }

    public function getTypeValueAttribute()
    {
        $lang = app()->getLocale();
        $type = config('job.type.' . $lang);
        return isset($type[$this->type]) ? $type[$this->type] : '';
    }

    public function getJobTypeValueAttribute()
    {
        $lang = app()->getLocale();
        $job_type = config('job.job_type.' . $lang);
        return isset($job_type[$this->job_type]) ? $job_type[$this->job_type] : '';
    }


    public function getRankNameAttribute()
    {

        $rank = '';


        foreach (explode(',', $this->rank) as $value) {
            if (in_array((int)$this->career, config('job.it_career'))) {
                $level = LevelBySkillMain::find($value);
                if ($level) {
                    $level = $level->toArray();
                    $rank .= $level['name_' . app()->getLocale()] . ', ';
                    // $rank .= config('job.rank_it.' . app()->getLocale())[$value] . ', ';
                }
            } else {
                $level = LevelByJobTop::find($value);
                if ($level) {
                    $level = $level->toArray();
                    $rank .= $level['name_' . app()->getLocale()] . ', ';
                    // $rank .= config('job.rank_it.' . app()->getLocale())[$value] . ', ';
                }
            }
        }

        return rtrim($rank, ', ');
    }
    public function getRankValueAttribute()
    {
        $lang = app()->getLocale();
        $rank = config('job.rank.' . $lang);
        $rankArr = explode(',', $this->rank);
        $ranks = [];
        foreach ($rankArr as $item) {
            if (isset($rank[$item])) {
                $ranks[] = $rank[$item];
            }
        }
        return $ranks;
    }

    public function getGroupAddressAttribute()
    {
        $addresses = json_decode($this->address, true);
        $group = [];
        $cities = Common::getCities();
        foreach ($addresses as $address) {
            if (!isset($address['area'])) {
                continue;
            }
            $group[$address['area']]['city'] = $cities[$address['area']];
            $group[$address['area']]['address'][] = $address['address'];
        }
        return $group;
    }

    public function getUrlFontEndDetailAttribute()
    {
        if ($this->slug) {
            return route('job-detail', ['slug' => $this->slug]);
        }
        return '';
    }

    public function getPathFileJdAttribute()
    {
        return gen_url_file_s3($this->file_jd);
    }
    public function getFileJobAttribute()
    {
        return gen_url_file_s3($this->file_jd);
    }

    public function getFirstAddressAttribute()
    {
        $addresses = json_decode($this->address, true);
        $cities = Common::getCities();
        return isset($addresses[0]['area']) ? $cities[$addresses[0]['area']] : '';
    }

    public function getFullPathSlugAttribute()
    {
        return config('constant.url') . '/job/' . $this->slug;
    }

    public function getAddressCityValueAttribute()
    {
        $addresses = json_decode($this->address, true);
        $group = [];
        $cities = Common::getCities();
        foreach ($addresses as $address) {
            if (!isset($address['area'])) {
                continue;
            }

            if (in_array($cities[$address['area']], $group)) {
                continue;
            }
            $group[] = $cities[$address['area']];
        }
        return $group;
    }

    public function getCityStrAttribute()
    {
        $addresses = json_decode($this->address, true);
        $group = [];
        $cities = Common::getCities();
        foreach ($addresses as $address) {
            if (!isset($address['area'])) {
                continue;
            }
            $group[] = $cities[$address['area']];
        }
        $group = array_unique($group);
        return implode(', ', $group);
    }

    public function getNoteCharactersAttribute()
    {
        return substr($this->note, 0, 100);
    }

    public function getCanSubmitAttribute()
    {
        if ($this->status != 1) {
            return false;
        }
        if ($this->expire_at < now()) {
            return false;
        }
        return true;
    }
}
