@extends('frontend.layouts.collaborator.app')
@section('content-collaborator')
    <div id="page-user-profile">
        <div class="header-tab">
            <ul class="nav nav-pills header-tab" role="tablist">
                <li class="nav-item" role="presentation">
                    <a href="javascript:void(0)"
                       class="item-header-tab from-storage  @if(!Session::has('action')) active @endif"
                       data-bs-toggle="pill" data-bs-target="#content-tab-user-profile">
                        <span class="icon icon-change-profile"></span><span>{!! config('settings.' . app()->getLocale() . '.rec_profile.thongtincoban') !!}</span>
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a href="javascript:void(0)"
                       class="item-header-tab new @if(Session::has('action') && Session::get('action') == 'change-password') active @endif"
                       data-bs-toggle="pill" data-bs-target="#content-tab-password">
                        <span class="icon icon-change-password"></span><span>{!! config('settings.' . app()->getLocale() . '.rec_profile.doimatkhau') !!}</span>
                    </a>
                </li>
            </ul>
        </div>
        <div class="tab-content">
            <div class="tab-pane fade  @if(!Session::has('action'))  show active  @endif" id="content-tab-user-profile">
                <div class="main-form">
                    <div class="wapper-form">
                        <div class="title-group">
                            {!! config('settings.' . app()->getLocale() . '.rec_profile.thongtincanhan') !!}
                            <button data-toggle="edit-form" class="button-edit"><img
                                    src="{{asset2('frontend/asset/images/dashboard-ctv/icon-edit.svg')}}"></button>
                        </div>
                        <div class="wapper-group-avatar" id="ava-in-page">
                            <div class="avatar">
                                {!!auth('client')->user()->avatar_render!!}
                            </div>
                            <div class="icon-edit-ava" style="display: none">
                                <a href="javascript:void(0)" data-bs-toggle="dropdown" aria-expanded="false">
                                    <img src="{{asset2('frontend/asset/images/dashboard-ctv/icon-edit.svg')}}">
                                </a>
                                <div class="dropdown-menu drop-edit-ava">
                                    <ul>
                                        @if(isset(auth('client')->user()->avatar))
                                            <li>
                                                <a href="{{auth('client')->user()->avatar_url}}" target="_blank"><span
                                                        class="icon"><img
                                                            src="{{asset2('frontend/asset/images/dashboard-ctv/icon-user.svg')}}"></span> {!! config('settings.' . app()->getLocale() . '.rec_profile.xemanhdaidien') !!}
                                                </a>
                                            </li>
                                        @endif
                                        <li>
                                            <a href="javascript:void(0)" data-toggle="change-avatar"><span class="icon"><img
                                                        src="{{asset2('frontend/asset/images/dashboard-ctv/icon-image.svg')}}"></span> {!! config('settings.' . app()->getLocale() . '.rec_profile.capnhatanhdaidien') !!}
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="error-ava text-center">
                            @if($errors->has('avatar'))
                                <label id="avatar-error" class="error" for="avatar">{{$errors->first('avatar')}}</label>
                            @endif
                        </div>

                        <form method="post" id="form-profile" action="{{route('rec-update-profile')}}"
                              enctype="multipart/form-data">
                            @csrf
                            <input class="file-avatar" type="text" style="display: none;" name="avatar">
                            <input class="file-cccd-front" type="text" style="display: none;" name="cccd_front_image">
                            <input class="file-cccd-back" type="text" style="display: none;" name="cccd_back_image">
                            <div class="row row-form">
                                <div class="col-lg-4 col-md-12 item-col-form">
                                    <div class="group-item-field">
                                        <label>{!! config('settings.' . app()->getLocale() . '.rec_profile.hovaten') !!} <span>*</span></label>
                                        <div class="item-form">
                                            <input disabled class="item-field" name="name"
                                                   value="{{old('name',$client->name)}}">
                                            @if($errors->has('name'))
                                                <label id="name-error" class="error"
                                                       for="name">{{$errors->first('name')}}</label>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-4 col-md-12 item-col-form">
                                    <div class="group-item-field">
                                        <label>{!! config('settings.' . app()->getLocale() . '.rec_profile.ngaysinh') !!} <span>*</span></label>
                                        <div class="item-form">
                                            <input disabled class="item-field fc-datepicker-profile" name="birthday"
                                                   value="{{old('birthday',$client->birthday_value)}}">
                                            @if($errors->has('birthday'))
                                                <label id="birthday-error" class="error"
                                                       for="birthday">{{$errors->first('birthday')}}</label>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-4 col-md-12 item-col-form">
                                    <div class="group-item-field">
                                        <label>{!! config('settings.' . app()->getLocale() . '.rec_profile.dienthoai') !!} <span>*</span></label>
                                        <div class="item-form">
                                            <input disabled class="item-field" name="mobile"
                                                   value="{{old('mobile',$client->mobile)}}">
                                            @if($errors->has('mobile'))
                                                <label id="birthday-error" class="error"
                                                       for="mobile">{{$errors->first('mobile')}}</label>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-4 col-md-12 item-col-form">
                                    <div class="group-item-field">
                                        <label>{!! config('settings.' . app()->getLocale() . '.rec_profile.email') !!} <span>*</span></label>
                                        <div class="item-form">
                                            <input disabled class="item-field" type="email" name="email"
                                                   value="{{old('email',$client->email)}}">
                                            @if($errors->has('email'))
                                                <label id="email-error" class="error"
                                                       for="email">{{$errors->first('email')}}</label>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12 item-col-form">
                                    <div class="group-item-field">
                                        <label>{!! config('settings.' . app()->getLocale() . '.rec_profile.diachi') !!} <span>*</span></label>
                                        <div class="item-form">
                                            <input name="address" disabled class="item-field"
                                                   value="{{old('address',$client->address)}}">
                                            @if($errors->has('address'))
                                                <label id="address-error" class="error"
                                                       for="address">{{$errors->first('address')}}</label>
                                            @endif
                                        </div>
                                    </div>
                                </div>

                                {{-- Thông tin CCCD --}}
                                <div class="col-lg-4 col-md-12 item-col-form">
                                    <div class="group-item-field">
                                        <label>Số căn cước công dân</label>
                                        <div class="item-form">
                                            <input disabled class="item-field" name="cccd_number"
                                                   value="{{old('cccd_number',isset($userInfo)?$userInfo->cccd_number:'')}}">
                                            @if($errors->has('cccd_number'))
                                                <label id="cccd_number-error" class="error"
                                                       for="cccd_number">{{$errors->first('cccd_number')}}</label>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-4 col-md-12 item-col-form">
                                    <div class="group-item-field">
                                        <label>Ảnh mặt trước CCCD</label>
                                        <div class="item-form">
                                            <div class="cccd-image-upload" data-type="front">
                                                <div class="upload-area" style="border: 2px dashed #ccc; padding: 20px; text-align: center; cursor: pointer;">
                                                    @if(isset($userInfo) && $userInfo->cccd_front_image)
                                                        <img src="{{ gen_url_file_s3($userInfo->cccd_front_image)}}" alt="CCCD Front" style="max-width: 200px; max-height: 150px;">
                                                        <p>Click để thay đổi ảnh</p>
                                                    @else
                                                        <p>Click để tải lên ảnh mặt trước CCCD</p>
                                                    @endif
                                                </div>
                                                <input type="file" class="cccd-file-input" accept="image/*" style="display: none;">
                                            </div>
                                            @if($errors->has('cccd_front_image'))
                                                <label id="cccd_front_image-error" class="error"
                                                       for="cccd_front_image">{{$errors->first('cccd_front_image')}}</label>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-4 col-md-12 item-col-form">
                                    <div class="group-item-field">
                                        <label>Ảnh mặt sau CCCD</label>
                                        <div class="item-form">
                                            <div class="cccd-image-upload" data-type="back">
                                                <div class="upload-area" style="border: 2px dashed #ccc; padding: 20px; text-align: center; cursor: pointer;">
                                                    @if(isset($userInfo) && $userInfo->cccd_back_image)
                                                        <img src="{{gen_url_file_s3($userInfo->cccd_back_image)}}" alt="CCCD Back" style="max-width: 200px; max-height: 150px;">
                                                        <p>Click để thay đổi ảnh</p>
                                                    @else
                                                        <p>Click để tải lên ảnh mặt sau CCCD</p>
                                                    @endif
                                                </div>
                                                <input type="file" class="cccd-file-input" accept="image/*" style="display: none;">
                                            </div>
                                            @if($errors->has('cccd_back_image'))
                                                <label id="cccd_back_image-error" class="error"
                                                       for="cccd_back_image">{{$errors->first('cccd_back_image')}}</label>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="bottom-form" style="display: none;">
                                <div class="item-button-form">
                                    <a href="" class="destroy-form item-button">{!! config('settings.' . app()->getLocale() . '.rec_profile.huy') !!}</a>
                                </div>
                                <div class="item-button-form">
                                    <button class="save-form item-button">{!! config('settings.' . app()->getLocale() . '.rec_profile.luu') !!}</button>
                                </div>
                            </div>
                        </form>
                    </div>


                    <div class="wapper-form">
                        <div class="title-group">
                            {!! config('settings.' . app()->getLocale() . '.rec_profile.thongtinthanhtoan') !!}
                            <button data-toggle="edit-form" class="button-edit"><img
                                    src="{{asset2('frontend/asset/images/dashboard-ctv/icon-edit.svg')}}"></button>
                        </div>
                        <form method="post" id="form-bankinfo" action="{{route('rec-update-bankinfo')}}">
                            @csrf
                            <div class="row row-form">
                                <div class="col-lg-3 col-md-12 item-col-form">
                                    <div class="group-item-field">
                                        <label>{!! config('settings.' . app()->getLocale() . '.rec_profile.hovaten') !!} <span>*</span></label>
                                        <div class="item-form">
                                            <input disabled class="item-field" name="bank_account"
                                                   value="{{old('bank_account',isset($userInfo)?$userInfo->bank_account:'')}}">
                                            @if($errors->has('bank_account'))
                                                <label id="bank_account-error" class="error"
                                                       for="bank_account">{{$errors->first('bank_account')}}</label>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-12 item-col-form">
                                    <div class="group-item-field">
                                        <label>{!! config('settings.' . app()->getLocale() . '.rec_profile.nganhang') !!} <span>*</span></label>
                                        <div class="item-form">
                                            <input disabled class="item-field" name="bank_name"
                                                   value="{{old('bank_name',isset($userInfo)?$userInfo->bank_name:'')}}">
                                            @if($errors->has('bank_name'))
                                                <label id="bank_name-error" class="error"
                                                       for="bank_name">{{$errors->first('bank_name')}}</label>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-12 item-col-form">
                                    <div class="group-item-field">
                                        <label>{!! config('settings.' . app()->getLocale() . '.rec_profile.sotaikhoan') !!} <span>*</span></label>
                                        <div class="item-form">
                                            <input disabled class="item-field" name="bank_account_number"
                                                   value="{{old('bank_account_number',isset($userInfo)?$userInfo->bank_account_number:'')}}">
                                            @if($errors->has('bank_account_number'))
                                                <label id="bank_account_number-error" class="error"
                                                       for="bank_account_number">{{$errors->first('bank_account_number')}}</label>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-12 item-col-form">
                                    <div class="group-item-field">
                                        <label>{!! config('settings.' . app()->getLocale() . '.rec_profile.chinhanh') !!} <span>*</span></label>
                                        <div class="item-form">
                                            <input disabled class="item-field" name="bank_branch"
                                                   value="{{old('bank_branch',isset($userInfo)?$userInfo->bank_branch:'')}}">
                                            @if($errors->has('bank_branch'))
                                                <label id="bank_branch-error" class="error"
                                                       for="bank_branch">{{$errors->first('bank_branch')}}</label>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="bottom-form" style="display: none">
                                <div class="item-button-form">
                                    <a href="" class="destroy-form item-button">{!! config('settings.' . app()->getLocale() . '.rec_profile.huy') !!}</a>
                                </div>
                                <div class="item-button-form">
                                    <button class="save-form item-button">{!! config('settings.' . app()->getLocale() . '.rec_profile.luu') !!}</button>
                                </div>
                            </div>
                        </form>

                    </div>


                    <div class="wapper-form">
                        <div class="title-group">
                            {!! config('settings.' . app()->getLocale() . '.rec_profile.refferalcode') !!}
                        </div>
                        <div class="row row-form">
                            <div class="col-lg-4 col-md-12 item-col-form">
                                <div class="group-item-field">
                                    <label>{!! config('settings.' . app()->getLocale() . '.rec_profile.refferalcodecuaban') !!}</label>
                                    <div class="item-form" data-toggle="tooltip-boostrap" data-placement="top"
                                         title="Copy Referral Define" data-url="{{$referralDefineCode}}">
                                        <input disabled class="item-field icon-clone" value="{{$referralDefineCode}}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-12 item-col-form">
                                <div class="group-item-field">
                                    <label>{!! config('settings.' . app()->getLocale() . '.rec_profile.refferalcodeduocnhan') !!}</label>
                                    <div class="item-form" data-toggle="tooltip-boostrap" data-placement="top"
                                         title="Copy Referral" data-url="{{$client->referral_code}}">
                                        <input disabled class="item-field icon-clone"
                                               value="{{$client->referral_code}}">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
            <div
                class="tab-pane fade @if(Session::has('action') && Session::get('action') == 'change-password') show active @endif"
                id="content-tab-password">
                <div class="main-form">
                    <form method="post" id="form-change-password" action="{{route('rec-change-password')}}">
                        @csrf
                        <div class="form-change-password">
                            <div class="row item-form-change-password">
                                <div class="col-md-4">
                                    <label>{!! config('settings.' . app()->getLocale() . '.rec_profile.matkhauhientai') !!}</label>
                                </div>
                                <div class="col-md-8">
                                    <div class="group-item-field field-item-st1 field-password">
                                        <div class="item-form">
                                            <input class="item-field" type="password" name="current_password" id="current_password"
                                                   placeholder="" autocomplete="off">
                                            @if($errors->has('current_password'))
                                                <label id="current_password-error" class="error"
                                                       for="current_password">{{$errors->first('current_password')}}</label>
                                            @endif
                                            <span class="toggle-password show-password"
                                                  data-toggle="toggle-password"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row item-form-change-password">
                                <div class="col-md-4">
                                    <label>{!! config('settings.' . app()->getLocale() . '.rec_profile.matkhaumoi') !!}</label>
                                </div>
                                <div class="col-md-8">
                                    <div class="group-item-field field-item-st1 field-password">
                                        <div class="item-form">
                                            <input class="item-field" type="password" name="password" placeholder=""
                                                   autocomplete="off">
                                            @if($errors->has('password'))
                                                <label id="password-error" class="error"
                                                       for="password">{{$errors->first('password')}}</label>
                                            @endif
                                            <span class="toggle-password show-password"
                                                  data-toggle="toggle-password"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row item-form-change-password">
                                <div class="col-md-4">
                                    <label>{!! config('settings.' . app()->getLocale() . '.rec_profile.nhaplai') !!}</label>
                                </div>
                                <div class="col-md-8">
                                    <div class="group-item-field field-item-st1 field-password">
                                        <div class="item-form">
                                            <input class="item-field" type="password" name="confirm_password"
                                                   placeholder="" autocomplete="off">
                                            @if($errors->has('confirm_password'))
                                                <label id="confirm_password-error" class="error"
                                                       for="confirm_password">{{$errors->first('confirm_password')}}</label>
                                            @endif
                                            <span class="toggle-password show-password"
                                                  data-toggle="toggle-password"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="bottom-form">
                                <div class="item-button-form">
                                    <a href="" class="destroy-form item-button">{!! config('settings.' . app()->getLocale() . '.rec_profile.huy') !!}</a>
                                </div>
                                <div class="item-button-form">
                                    <button class="save-form item-button">{!! config('settings.' . app()->getLocale() . '.rec_profile.luu') !!}</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="actions" style="display: none">
        <a class="btn file-btn">
            <input type="file" id="upload" value="Choose a file" accept="image/*">
        </a>
    </div>
    <div class="modal fade" id="modal-crop-ava" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-body">
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    {{--                    <div class="header">Cập nhật ảnh đại diện</div>--}}
                    <div id="group-crop-image">
                        <div class="upload-msg" data-toggle="upload-ava-for-crop">
                            Chọn ảnh
                        </div>
                        <div id="upload-demo-wrap" style="display: none">
                            <div>
                                <div id="upload-demo"></div>
                            </div>
                            <button class="upload-result">Cập nhật làm ảnh đại diện</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@section('scripts')
    <script src="https://cdn.jsdelivr.net/jquery.validation/1.16.0/additional-methods.min.js"></script>
    <link rel="stylesheet" href="{{asset2('frontend/asset/croppie-master/croppie.css')}}">
    <script src="{{asset2('frontend/asset/croppie-master/croppie.js')}}"></script>
    <script>
        $.validator.addMethod('regex', function (value) {
            var regex = /^[0-9()+.-]*$/;
            return value.trim().match(regex);
        });
        $.validator.addMethod('filesize', function (value, element, param) {
            return this.optional(element) || (element.files[0].size <= param * 1000000)
        }, 'File size must be less than {0} MB');
        $.validator.addMethod('before_future', function (value) {
            var now = new Date();
            var dateParts = value.split("/");
            var dateObject = new Date(+dateParts[2], dateParts[1] - 1, +dateParts[0]);
            return dateObject.getTime() < now.getTime();
        });
        $.validator.addMethod('base64_extension', function (value, element, param) {
            if (!value) {
                return true;
            }
            let typeCheck = param.split(',')
            let type = value.split(';')[0].split('/')[1];
            return typeCheck.includes(type);
        });


        var $uploadCrop;

        function readFile(input) {
            if (input.files && input.files[0]) {
                var reader = new FileReader();

                reader.onload = function (e) {
                    $('.upload-demo').addClass('ready');
                    $uploadCrop.croppie('bind', {
                        url: e.target.result
                    }).then(function () {
                        console.log('jQuery bind complete');
                    });

                }

                reader.readAsDataURL(input.files[0]);
            } else {

            }
        }

        $uploadCrop = $('#upload-demo').croppie({
            viewport: {
                width: 100,
                height: 100,
                type: 'circle'
            },
            enableExif: true
        });

        $('#upload').on('change', function () {
            readFile(this);
        });
        $('.upload-result').on('click', function (ev) {
            $uploadCrop.croppie('result', {
                type: 'canvas',
                size: 'viewport'
            }).then(function (resp) {
                popupResult({
                    src: resp
                });
            });
        });

        function popupResult(result) {
            $("#modal-crop-ava").modal('hide');
            $('#ava-in-page .avatar-user').attr('src', result.src);
            $('[name="avatar"]').val(result.src);
        }

        $(document).ready(function () {

            $('.upload-result').on('click', function (ev) {
                $uploadCrop.croppie('result', {
                    type: 'canvas',
                    size: 'viewport'
                }).then(function (resp) {
                    popupResult({
                        src: resp
                    });
                });
            });

            $('[data-toggle="upload-ava-for-crop"]').click(function () {
                $("#upload").trigger('click');
            });
            $("#upload").change(function () {
                $('#upload-demo-wrap').show();
                $('[data-toggle="upload-ava-for-crop"]').hide();
            })

            $('[data-toggle="edit-form"]').click(function () {
                var field = $(this).parents('.wapper-form').find(':disabled');
                field.each(function (i, v) {
                    $(v).prop("disabled", false);
                });
                $(this).parents('.wapper-form').find('.icon-edit-ava').show();
                $(this).parents('.wapper-form').find('.bottom-form').show();
                $(this).hide();
            });

            $("#form-profile").validate({
                ignore: [],
                rules: {
                    email: {
                        required: true,
                        email: true
                    },
                    name: {
                        required: true,
                    },
                    mobile: {
                        required: true,
                        minlength: 10,
                        maxlength: 16,
                        regex: true,
                    },
                    address: {
                        required: true,
                    },
                    birthday: {
                        required: true,
                        before_future: true,
                    },
                    avatar: {
                        base64_extension: "jpeg,jpg,png",
                        // filesize: 5,
                    },
                    cccd_number: {
                        minlength: 9,
                        maxlength: 12,
                        regex: /^[0-9]+$/
                    },
                    cccd_front_image: {
                        base64_extension: "jpeg,jpg,png"
                    },
                    cccd_back_image: {
                        base64_extension: "jpeg,jpg,png"
                    },
                },
                messages: {
                    email: {
                        required: '{{__('frontend/validation.required')}}',
                        email: '{{__('frontend/validation.email')}}',
                    },
                    name: {
                        required: '{{__('frontend/validation.required')}}',
                    },
                    mobile: {
                        required: '{{__('frontend/validation.required')}}',
                        minlength: '{{__('frontend/validation.min', ['min' => 10])}}',
                        maxlength: '{{__('frontend/validation.max', ['max' => 16])}}',
                        regex: '{{__('frontend/validation.regex_phone')}}',
                    },
                    address: {
                        required: '{{__('frontend/validation.required')}}',
                    },
                    birthday: {
                        required: '{{__('frontend/validation.required')}}',
                        before_future: '{{__('frontend/validation.before_now')}}',
                    },
                    avatar: {
                        base64_extension: '{{__('frontend/validation.mimes')}}',
                        filesize: '{{__('frontend/validation.file_max')}}',
                    },
                    cccd_number: {
                        minlength: 'Số CCCD phải có ít nhất 9 chữ số',
                        maxlength: 'Số CCCD không được quá 12 chữ số',
                        regex: 'Số CCCD chỉ được chứa các chữ số'
                    },
                    cccd_front_image: {
                        base64_extension: '{{__('frontend/validation.mimes')}}'
                    },
                    cccd_back_image: {
                        base64_extension: '{{__('frontend/validation.mimes')}}'
                    },
                },
                highlight: function (element, errorClass, validClass) {
                    var elem = $(element);
                    if (elem.hasClass("file-avatar")) {
                        $('.error-ava').addClass(errorClass)
                    } else {
                        elem.addClass(errorClass);
                    }
                },
                unhighlight: function (element, errorClass, validClass) {
                    var elem = $(element);
                    if (elem.hasClass("file-avatar")) {
                        $('.error-ava').removeClass(errorClass)
                    } else {
                        elem.removeClass(errorClass);
                    }
                },
                errorPlacement: function (error, element) {
                    var elem = $(element);
                    if (elem.hasClass("file-avatar")) {
                        element = $('.error-ava');
                        element.html(error);
                    } else {
                        error.insertAfter(element);
                    }
                },
                submitHandler: function (form) {
                    form.submit();
                }
            });

            $("#form-bankinfo").validate({
                rules: {
                    bank_account: {
                        required: true,
                    },
                    bank_name: {
                        required: true,
                    },
                    bank_account_number: {
                        required: true,
                    },
                    bank_branch: {
                        required: true,
                    },
                },
                messages: {
                    bank_account: {
                        required: '{{__('frontend/validation.required')}}',
                    },
                    bank_name: {
                        required: '{{__('frontend/validation.required')}}',
                    },
                    bank_account_number: {
                        required: '{{__('frontend/validation.required')}}',
                    },
                    bank_branch: {
                        required: '{{__('frontend/validation.required')}}',
                    },
                },
                submitHandler: function (form) {
                    form.submit();
                }
            });

            $("#form-change-password").validate({
                rules: {
                    current_password: {
                        required: true,
                    },
                    password: {
                        required: true,
                        minlength: 8,
                        maxlength: 20,
                        notEqual: true
                    },
                    confirm_password: {
                        required: true,
                    },
                },
                messages: {
                    current_password: {
                        required: '{{__('frontend/validation.required')}}',
                    },
                    password: {
                        required: '{{__('frontend/validation.required')}}',
                        minlength: '{{__('frontend/validation.min', ['min' => 8])}}',
                        maxlength: '{{__('frontend/validation.max', ['max' => 20])}}',
                        notEqual: '{{__('frontend/validation.not_equal_password')}}',
                    },
                    confirm_password: {
                        required: '{{__('frontend/validation.required')}}',
                        equalTo: '{{__('frontend/validation.same')}}',
                    },
                },
                submitHandler: function (form) {
                    form.submit();
                }
            });



            jQuery.validator.addMethod("notEqual", function(value, element, param) {
                let current_pass = $('#current_password').val();
                return this.optional(element) || value != current_pass;
            });

            $('[data-toggle="change-avatar"]').click(function () {
                // $('[name="avatar"]').trigger('click');
                $("#upload").val('');
                $("#modal-crop-ava").modal('show');
            })

            $("#modal-crop-ava").on("hidden.bs.modal", function () {
                $('#upload-demo-wrap').hide();
                $('[data-toggle="upload-ava-for-crop"]').show();
            });

            // $('[name="avatar"]').change(function (){
            //     let file = $(this).get(0).files[0];
            //     let reader = new FileReader();
            //     reader.onload = function (event) {
            //         $(".avatar-user")
            //             .attr("src", event.target.result);
            //     };
            //     reader.readAsDataURL(file);
            // });

            var dateToday = new Date();
            $(".fc-datepicker-profile").datepicker({
                showOtherMonths: !0,
                selectOtherMonths: !0,
                dateFormat: 'dd/mm/yy',
                maxDate: dateToday,
            })
        })

        // Xử lý upload ảnh CCCD
        $('.cccd-image-upload').each(function() {
            const uploadArea = $(this).find('.upload-area');
            const fileInput = $(this).find('.cccd-file-input');
            const type = $(this).data('type');
            uploadArea.on('click', function() {
                if ($(this).closest('.wapper-form').find('.button-edit').is(':visible')) {
                    alert ("Chuyển chế độ sửa profile trước khi đổi ảnh");
                    return; // Chỉ cho phép upload khi đang ở chế độ edit (button edit bị ẩn)
                } else {
                }
                fileInput.click();
            });
            
            fileInput.on('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const base64 = e.target.result;
                        uploadArea.html('<img src="' + base64 + '" alt="CCCD ' + type + '" style="max-width: 200px; max-height: 150px;"><p>Click để thay đổi ảnh</p>');
                        
                        // Lưu base64 vào hidden input
                        if (type === 'front') {
                            $('.file-cccd-front').val(base64);
                        } else {
                            $('.file-cccd-back').val(base64);
                        }
                    };
                    reader.readAsDataURL(file);
                }
            });
        });

        // Xử lý nút Hủy cho tất cả các form
        $('.destroy-form').on('click', function(e) {
            e.preventDefault();
            const form = $(this).closest('.wapper-form');
            
            // Disable lại tất cả các field
            form.find(':input').prop('disabled', true);
            
            // Ẩn bottom-form và hiện lại button edit
            form.find('.bottom-form').hide();
            form.find('.button-edit').show();
            form.find('.icon-edit-ava').hide();
            
            // Reset form về trạng thái ban đầu
            const formElement = form.find('form')[0];
            if (formElement) {
                formElement.reset();
                // Reload lại trang để reset về dữ liệu gốc
                location.reload();
            }
        });
    </script>
@endsection
