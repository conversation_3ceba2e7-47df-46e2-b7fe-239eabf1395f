{"__meta": {"id": "01K1SZT55S0809E64V8RDGZNJV", "datetime": "2025-08-04 14:30:22", "utime": **********.522562, "method": "POST", "uri": "/rec/update-profile", "ip": "127.0.0.1"}, "php": {"version": "8.1.6", "interface": "apache2handler"}, "messages": {"count": 21, "messages": [{"message": "[14:30:17] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `seos` where `key` = 'rec-update-profile' limit 1\\n-- \",\n    \"Time:\": 22.2\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.523561, "xdebug_link": null, "collector": "log"}, {"message": "[14:30:17] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\\\"_token\\\":\\\"67FrIiAKRzqdDDYE9lpf1v63S50ahQba2svcMgmp\\\",\\\"avatar\\\":null,\\\"cccd_front_image\\\":null,\\\"cccd_back_image\\\":null,\\\"name\\\":\\\"HAU Nguyen Thi\\\",\\\"birthday\\\":\\\"04\\\\\\/02\\\\\\/2000\\\",\\\"mobile\\\":\\\"097845634555\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"address\\\":\\\"60 D\\\\u01b0\\\\u01a1ng Khu\\\\u00ea M\\\\u1ef9 \\\\u0110\\\\u00ecnh2\\\",\\\"cccd_number\\\":null}', 'http:\\/\\/recland.local\\/rec\\/update-profile', 'http:\\/\\/recland.local\\/rec\\/profile', '[\\\"en-us\\\",\\\"en\\\",\\\"vi\\\",\\\"nl\\\"]', 'Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********', '{\\\"host\\\":[\\\"recland.local\\\"],\\\"connection\\\":[\\\"keep-alive\\\"],\\\"content-length\\\":[\\\"1127\\\"],\\\"cache-control\\\":[\\\"max-age=0\\\"],\\\"upgrade-insecure-requests\\\":[\\\"1\\\"],\\\"user-agent\\\":[\\\"Mozilla\\\\\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\\\\\/537.36 (KHTML, like Gecko) Chrome\\\\\\/********* Safari\\\\\\/537.36 Edg\\\\\\/*********\\\"],\\\"origin\\\":[\\\"http:\\\\\\/\\\\\\/recland.local\\\"],\\\"content-type\\\":[\\\"multipart\\\\\\/form-data; boundary=----WebKitFormBoundary1fIoQC6TQk59Uc2h\\\"],\\\"accept\\\":[\\\"text\\\\\\/html,application\\\\\\/xhtml+xml,application\\\\\\/xml;q=0.9,image\\\\\\/avif,image\\\\\\/webp,image\\\\\\/apng,*\\\\\\/*;q=0.8,application\\\\\\/signed-exchange;v=b3;q=0.7\\\"],\\\"referer\\\":[\\\"http:\\\\\\/\\\\\\/recland.local\\\\\\/rec\\\\\\/profile\\\"],\\\"accept-encoding\\\":[\\\"gzip, deflate\\\"],\\\"accept-language\\\":[\\\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\\\"],\\\"cookie\\\":[\\\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImxyWFptaVBGK1BuR2MvQWh4MlZpUWc9PSIsInZhbHVlIjoiSGRqMWdEd3lNSS9ZenRwdlNZQ3hjTmV2ZktDRjczQ0FPaWR3NVFHek5vV3o3Tm1nYU1OQzk1dUIxY0ZSRmwyT1ByRC82d0lXRzBWTWRFUTltUGFNQ29ZNmhuU2RKbncwOWU4bXZxaFNPemtoRE1pNVp6b2hsVWh0RjlwSFYzQnEiLCJtYWMiOiI1OGE0MjY5MjNjOGFiOTdkMWQ3NWNlYmRhNTZmMzYyZmNiMWY4MWRjNjA5NTJiNDkxODU3OWEyZWYwODMxZWJhIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6Ik0zS2V3Q25ZT253VG92bGRzYnRBcVE9PSIsInZhbHVlIjoicy9UZ2NLZFNjYmJQOTd2UG4zWGtlcFU3NHdwWldvbFd0VXVVcVVMNDJLWlZjem1PYXlPVFFrb2hMWTVOYVVBcUh3d0xzc0c3SWI1L09uamR6N1dIMjRMWmhpUE5ESkphY0FOditERGlJVXBFcUlXWFgvbXpaYURGbUJoem1xL0YiLCJtYWMiOiJiNTcyZmE5M2ZlMjlkYTA0NjY2OTcxNWI3MjllMDc1N2M0ZGE2YjQyOTVkZTcxYjU1Njg1NmQ5N2E3ODA0YzBlIiwidGFnIjoiIn0%3D\\\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-04 14:30:17', '2025-08-04 14:30:17')\\n-- \",\n    \"Time:\": 0.68\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.553557, "xdebug_link": null, "collector": "log"}, {"message": "[14:30:17] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `id` = '512' limit 1\\n-- \",\n    \"Time:\": 0.68\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.562373, "xdebug_link": null, "collector": "log"}, {"message": "[14:30:17] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `id` != '512' and `email` = '<EMAIL>' and `type` = 'rec' limit 1\\n-- \",\n    \"Time:\": 0.64\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.579873, "xdebug_link": null, "collector": "log"}, {"message": "[14:30:17] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `users`.`id` = '512' limit 1\\n-- \",\n    \"Time:\": 0.54\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.584483, "xdebug_link": null, "collector": "log"}, {"message": "[14:30:17] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nupdate `users` set `birthday` = '2000-02-04 14:30:17', `users`.`updated_at` = '2025-08-04 14:30:17' where `id` = '512'\\n-- \",\n    \"Time:\": 0.79\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.608154, "xdebug_link": null, "collector": "log"}, {"message": "[14:30:17] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `id` = '1' limit 1\\n-- \",\n    \"Time:\": 0.56\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.611229, "xdebug_link": null, "collector": "log"}, {"message": "[14:30:17] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `audits` (`old_values`, `new_values`, `event`, `auditable_id`, `auditable_type`, `user_id`, `user_type`, `tags`, `ip_address`, `user_agent`, `url`, `updated_at`, `created_at`) values ('{\\\"birthday\\\":\\\"2000-02-04\\\"}', '{\\\"birthday\\\":\\\"2000-02-04T07:30:17.000000Z\\\"}', 'updated', '512', 'App\\\\Models\\\\User', '1', 'App\\\\Models\\\\User', '', '127.0.0.1', 'Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********', 'http:\\/\\/recland.local\\/rec\\/update-profile', '2025-08-04 14:30:17', '2025-08-04 14:30:17')\\n-- \",\n    \"Time:\": 0.71\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.616611, "xdebug_link": null, "collector": "log"}, {"message": "[14:30:17] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `users`.`id` = '512' limit 1\\n-- \",\n    \"Time:\": 0.61\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.618439, "xdebug_link": null, "collector": "log"}, {"message": "[14:30:19] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `job_logs` (`job_id`, `name`, `queue`, `payload`, `updated_at`, `created_at`) values ('', 'App\\\\Notifications\\\\UpdateProfileEmployer', 'sync', '{\\\"uuid\\\":\\\"b76be44e-ea3b-45b4-a0b2-7a6993223046\\\",\\\"displayName\\\":\\\"App\\\\\\\\Notifications\\\\\\\\UpdateProfileEmployer\\\",\\\"job\\\":\\\"Illuminate\\\\\\\\Queue\\\\\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"failOnTimeout\\\":false,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"Illuminate\\\\\\\\Notifications\\\\\\\\SendQueuedNotifications\\\",\\\"command\\\":\\\"O:48:\\\\\\\"Illuminate\\\\\\\\Notifications\\\\\\\\SendQueuedNotifications\\\\\\\":3:{s:11:\\\\\\\"notifiables\\\\\\\";O:45:\\\\\\\"Illuminate\\\\\\\\Contracts\\\\\\\\Database\\\\\\\\ModelIdentifier\\\\\\\":5:{s:5:\\\\\\\"class\\\\\\\";s:15:\\\\\\\"App\\\\\\\\Models\\\\\\\\User\\\\\\\";s:2:\\\\\\\"id\\\\\\\";a:1:{i:0;i:512;}s:9:\\\\\\\"relations\\\\\\\";a:0:{}s:10:\\\\\\\"connection\\\\\\\";s:5:\\\\\\\"mysql\\\\\\\";s:15:\\\\\\\"collectionClass\\\\\\\";N;}s:12:\\\\\\\"notification\\\\\\\";O:39:\\\\\\\"App\\\\\\\\Notifications\\\\\\\\UpdateProfileEmployer\\\\\\\":2:{s:7:\\\\\\\"\\\\u0000*\\\\u0000user\\\\\\\";O:45:\\\\\\\"Illuminate\\\\\\\\Contracts\\\\\\\\Database\\\\\\\\ModelIdentifier\\\\\\\":5:{s:5:\\\\\\\"class\\\\\\\";s:15:\\\\\\\"App\\\\\\\\Models\\\\\\\\User\\\\\\\";s:2:\\\\\\\"id\\\\\\\";i:512;s:9:\\\\\\\"relations\\\\\\\";a:0:{}s:10:\\\\\\\"connection\\\\\\\";s:5:\\\\\\\"mysql\\\\\\\";s:15:\\\\\\\"collectionClass\\\\\\\";N;}s:2:\\\\\\\"id\\\\\\\";s:36:\\\\\\\"ab09a0fa-017b-44cb-b273-dc7211b47c61\\\\\\\";}s:8:\\\\\\\"channels\\\\\\\";a:1:{i:0;s:4:\\\\\\\"mail\\\\\\\";}}\\\"}}', '2025-08-04 14:30:19', '2025-08-04 14:30:19')\\n-- \",\n    \"Time:\": 11.06\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.460094, "xdebug_link": null, "collector": "log"}, {"message": "[14:30:19] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `users`.`id` = '512' limit 1\\n-- \",\n    \"Time:\": 0.64\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.498896, "xdebug_link": null, "collector": "log"}, {"message": "[14:30:19] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `users`.`id` in (512)\\n-- \",\n    \"Time:\": 0.52\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.500908, "xdebug_link": null, "collector": "log"}, {"message": "[14:30:20] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `email_logs` (`from`, `to`, `cc`, `subject`, `html_content`, `hash`, `status`, `updated_at`, `created_at`) values ('[\\\"<EMAIL>\\\"]', '[\\\"<EMAIL>\\\"]', '[]', '[Recland] [C\\u1eacP NH\\u1eacT H\\u1ed2 S\\u01a0 C\\u00c1 NH\\u00c2N TH\\u00c0NH C\\u00d4NG]', '<!DOCTYPE html>\\r\\n<html>\\r\\n\\r\\n<body width=\\\"100%\\\"\\r\\n    style=\\\"margin: 0; padding: 0 !important; mso-line-height-rule: exactly; background-color: #f1f1f1;font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif\\\">\\r\\n    <center style=\\\"width: 100%; background-color: #f1f1f1;\\\">\\r\\n        <div style=\\\"max-width: 600px; margin: 0 auto;\\\">\\r\\n            <table align=\\\"center\\\" role=\\\"presentation\\\" cellspacing=\\\"0\\\" cellpadding=\\\"0\\\" border=\\\"0\\\" width=\\\"100%\\\"\\r\\n                style=\\\"margin: auto;\\\">\\r\\n                <tr>\\r\\n                    <td>\\r\\n                        <div style=\\\"text-align: center;padding: 25px 0;background: #FCFCFE;\\\">\\r\\n                            <img style=\\\"max-width: 100%\\\"\\r\\n                                src=\\\"http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/logo.png?v=6890618c6ca18\\\">\\r\\n                        <\\/div>\\r\\n                    <\\/td>\\r\\n                <\\/tr>\\r\\n            <\\/table>\\r\\n            <div style=\\\"background-image: linear-gradient(192.45deg, #155969 18.03%, #DAE6E9 93.73%);\\\">\\r\\n                <div\\r\\n                    style=\\\"padding-top: 0;padding-left: 40px;padding-right: 40px;padding-bottom: 32px;\\r\\n                background-image: url(http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/background.png?v=6890618c6ca42);\\r\\n                background-repeat: no-repeat;background-size: 100%;\\\">\\r\\n                    <table>\\r\\n                        <tr>\\r\\n                            <td>\\r\\n                                <div\\r\\n                                    style=\\\"padding-top: 80px;height: 194px;text-align: center;font-style: normal;font-weight: 600;font-size: 26px;line-height: 31px;color: #FCFCFE;\\\">\\r\\n                                    <div style=\\\"margin-bottom: 14px\\\">\\r\\n                                                                            <\\/div>\\r\\n                                    <div>\\r\\n                                                                            <\\/div>\\r\\n\\r\\n                                <\\/div>\\r\\n                            <\\/td>\\r\\n                        <\\/tr>\\r\\n                    <\\/table>\\r\\n                    <table style=\\\"width: 100%\\\" role=\\\"presentation\\\" cellspacing=\\\"0\\\" cellpadding=\\\"0\\\" border=\\\"0\\\"\\r\\n                        width=\\\"100%\\\">\\r\\n                        <tr>\\r\\n                            <td>\\r\\n                                    <div\\r\\n        style=\\\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\\\">\\r\\n        <p>Xin ch\\u00e0o, HAU Nguyen Thi<\\/p>\\r\\n        Recland th\\u00f4ng b\\u00e1o h\\u1ed3 s\\u01a1 c\\u1ee7a b\\u1ea1n \\u0111\\u00e3 \\u0111\\u01b0\\u1ee3c c\\u1eadp nh\\u1eadt th\\u00e0nh c\\u00f4ng.\\r\\n        <p><b>Tr\\u00e2n tr\\u1ecdng,<\\/b><\\/p>\\r\\n        <p><i>\\u0110\\u1ed9i ng\\u0169 Recland.<\\/i><\\/p>\\r\\n    <\\/div>\\r\\n    <div style=\\\"border: 5px solid #F7F7F7;\\\"><\\/div>\\r\\n    <div\\r\\n        style=\\\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\\\">\\r\\n        <p>Hello, HAU Nguyen Thi<\\/p>\\r\\n        Recland informs you that your profile has been successfully updated.\\r\\n        <p><b>Best regards,<\\/b><\\/p>\\r\\n        <p><i>Recland team.<\\/i><\\/p>\\r\\n    <\\/div>\\r\\n                            <\\/td>\\r\\n                        <\\/tr>\\r\\n                        <tr>\\r\\n                            <td>\\r\\n                                <div style=\\\"padding:12px 0\\\">\\r\\n                                    <div\\r\\n                                        style=\\\"background-image: url(http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/background-footer.png?v=6890618c6ca88);background-position: bottom left;background-repeat: no-repeat;text-align: center;background-color: #FCFCFE;padding-top: 22px;padding-bottom: 12px\\\">\\r\\n                                        <div style=\\\"margin-bottom: 12px;text-align: center\\\">\\r\\n                                            <a style=\\\"padding-left: 16px;padding-right: 16px\\\" href=\\\"#\\\"><img\\r\\n                                                    src=\\\"http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/icon-twitter.png?v=6890618c6caa3\\\"><\\/a>\\r\\n                                            <a style=\\\"padding-left: 16px;padding-right: 16px\\\" href=\\\"#\\\"><img\\r\\n                                                    src=\\\"http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/icon-facebook.png?v=6890618c6cabd\\\"><\\/a>\\r\\n                                            <a style=\\\"padding-left: 16px;padding-right: 16px\\\" href=\\\"#\\\"><img\\r\\n                                                    src=\\\"http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/icon-instagram.png?v=6890618c6cad6\\\"><\\/a>\\r\\n                                        <\\/div>\\r\\n                                        <p\\r\\n                                            style=\\\"margin-top: 0;margin-bottom: 8px;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #17677B;\\\">\\r\\n                                            N\\u1ec1n t\\u1ea3ng t\\u1ea1o ra c\\u01a1 h\\u1ed9i ki\\u1ebfm ti\\u1ec1n d\\u00e0nh cho HR Freelancer<\\/p>\\r\\n                                        <p\\r\\n                                            style=\\\"margin-top: 0;margin-bottom: 0;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #3C4455;\\\">\\r\\n                                            \\u00a9 2022 Recland.co<\\/p>\\r\\n                                    <\\/div>\\r\\n                                <\\/div>\\r\\n\\r\\n                            <\\/td>\\r\\n                        <\\/tr>\\r\\n                    <\\/table>\\r\\n                <\\/div>\\r\\n            <\\/div>\\r\\n        <\\/div>\\r\\n    <\\/center>\\r\\n<\\/body>\\r\\n\\r\\n<\\/html>\\r\\n', 'a14feb746f7078d6df8aa7c2bfa18c50', '0', '2025-08-04 14:30:20', '2025-08-04 14:30:20')\\n-- \",\n    \"Time:\": 1.75\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.546002, "xdebug_link": null, "collector": "log"}, {"message": "[14:30:22] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `job_logs` (`job_id`, `name`, `queue`, `payload`, `updated_at`, `created_at`) values ('', 'App\\\\Jobs\\\\UpdateEmailLogStatus', 'sync', '{\\\"uuid\\\":\\\"e64860ca-3568-4942-ab79-d8ffdac0c70a\\\",\\\"displayName\\\":\\\"App\\\\\\\\Jobs\\\\\\\\UpdateEmailLogStatus\\\",\\\"job\\\":\\\"Illuminate\\\\\\\\Queue\\\\\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"failOnTimeout\\\":false,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"App\\\\\\\\Jobs\\\\\\\\UpdateEmailLogStatus\\\",\\\"command\\\":\\\"O:29:\\\\\\\"App\\\\\\\\Jobs\\\\\\\\UpdateEmailLogStatus\\\\\\\":1:{s:5:\\\\\\\"email\\\\\\\";O:28:\\\\\\\"Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Email\\\\\\\":6:{i:0;N;i:1;N;i:2;s:5639:\\\\\\\"<!DOCTYPE html>\\\\r\\\\n<html>\\\\r\\\\n\\\\r\\\\n<body width=\\\\\\\"100%\\\\\\\"\\\\r\\\\n    style=\\\\\\\"margin: 0; padding: 0 !important; mso-line-height-rule: exactly; background-color: #f1f1f1;font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif\\\\\\\">\\\\r\\\\n    <center style=\\\\\\\"width: 100%; background-color: #f1f1f1;\\\\\\\">\\\\r\\\\n        <div style=\\\\\\\"max-width: 600px; margin: 0 auto;\\\\\\\">\\\\r\\\\n            <table align=\\\\\\\"center\\\\\\\" role=\\\\\\\"presentation\\\\\\\" cellspacing=\\\\\\\"0\\\\\\\" cellpadding=\\\\\\\"0\\\\\\\" border=\\\\\\\"0\\\\\\\" width=\\\\\\\"100%\\\\\\\"\\\\r\\\\n                style=\\\\\\\"margin: auto;\\\\\\\">\\\\r\\\\n                <tr>\\\\r\\\\n                    <td>\\\\r\\\\n                        <div style=\\\\\\\"text-align: center;padding: 25px 0;background: #FCFCFE;\\\\\\\">\\\\r\\\\n                            <img style=\\\\\\\"max-width: 100%\\\\\\\"\\\\r\\\\n                                src=\\\\\\\"http:\\\\\\/\\\\\\/recland.local\\\\\\/frontend\\\\\\/asset\\\\\\/images\\\\\\/template-email\\\\\\/logo.png?v=6890618c6ca18\\\\\\\">\\\\r\\\\n                        <\\\\\\/div>\\\\r\\\\n                    <\\\\\\/td>\\\\r\\\\n                <\\\\\\/tr>\\\\r\\\\n            <\\\\\\/table>\\\\r\\\\n            <div style=\\\\\\\"background-image: linear-gradient(192.45deg, #155969 18.03%, #DAE6E9 93.73%);\\\\\\\">\\\\r\\\\n                <div\\\\r\\\\n                    style=\\\\\\\"padding-top: 0;padding-left: 40px;padding-right: 40px;padding-bottom: 32px;\\\\r\\\\n                background-image: url(http:\\\\\\/\\\\\\/recland.local\\\\\\/frontend\\\\\\/asset\\\\\\/images\\\\\\/template-email\\\\\\/background.png?v=6890618c6ca42);\\\\r\\\\n                background-repeat: no-repeat;background-size: 100%;\\\\\\\">\\\\r\\\\n                    <table>\\\\r\\\\n                        <tr>\\\\r\\\\n                            <td>\\\\r\\\\n                                <div\\\\r\\\\n                                    style=\\\\\\\"padding-top: 80px;height: 194px;text-align: center;font-style: normal;font-weight: 600;font-size: 26px;line-height: 31px;color: #FCFCFE;\\\\\\\">\\\\r\\\\n                                    <div style=\\\\\\\"margin-bottom: 14px\\\\\\\">\\\\r\\\\n                                                                            <\\\\\\/div>\\\\r\\\\n                                    <div>\\\\r\\\\n                                                                            <\\\\\\/div>\\\\r\\\\n\\\\r\\\\n                                <\\\\\\/div>\\\\r\\\\n                            <\\\\\\/td>\\\\r\\\\n                        <\\\\\\/tr>\\\\r\\\\n                    <\\\\\\/table>\\\\r\\\\n                    <table style=\\\\\\\"width: 100%\\\\\\\" role=\\\\\\\"presentation\\\\\\\" cellspacing=\\\\\\\"0\\\\\\\" cellpadding=\\\\\\\"0\\\\\\\" border=\\\\\\\"0\\\\\\\"\\\\r\\\\n                        width=\\\\\\\"100%\\\\\\\">\\\\r\\\\n                        <tr>\\\\r\\\\n                            <td>\\\\r\\\\n                                    <div\\\\r\\\\n        style=\\\\\\\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\\\\\\\">\\\\r\\\\n        <p>Xin ch\\\\u00e0o, HAU Nguyen Thi<\\\\\\/p>\\\\r\\\\n        Recland th\\\\u00f4ng b\\\\u00e1o h\\\\u1ed3 s\\\\u01a1 c\\\\u1ee7a b\\\\u1ea1n \\\\u0111\\\\u00e3 \\\\u0111\\\\u01b0\\\\u1ee3c c\\\\u1eadp nh\\\\u1eadt th\\\\u00e0nh c\\\\u00f4ng.\\\\r\\\\n        <p><b>Tr\\\\u00e2n tr\\\\u1ecdng,<\\\\\\/b><\\\\\\/p>\\\\r\\\\n        <p><i>\\\\u0110\\\\u1ed9i ng\\\\u0169 Recland.<\\\\\\/i><\\\\\\/p>\\\\r\\\\n    <\\\\\\/div>\\\\r\\\\n    <div style=\\\\\\\"border: 5px solid #F7F7F7;\\\\\\\"><\\\\\\/div>\\\\r\\\\n    <div\\\\r\\\\n        style=\\\\\\\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\\\\\\\">\\\\r\\\\n        <p>Hello, HAU Nguyen Thi<\\\\\\/p>\\\\r\\\\n        Recland informs you that your profile has been successfully updated.\\\\r\\\\n        <p><b>Best regards,<\\\\\\/b><\\\\\\/p>\\\\r\\\\n        <p><i>Recland team.<\\\\\\/i><\\\\\\/p>\\\\r\\\\n    <\\\\\\/div>\\\\r\\\\n                            <\\\\\\/td>\\\\r\\\\n                        <\\\\\\/tr>\\\\r\\\\n                        <tr>\\\\r\\\\n                            <td>\\\\r\\\\n                                <div style=\\\\\\\"padding:12px 0\\\\\\\">\\\\r\\\\n                                    <div\\\\r\\\\n                                        style=\\\\\\\"background-image: url(http:\\\\\\/\\\\\\/recland.local\\\\\\/frontend\\\\\\/asset\\\\\\/images\\\\\\/template-email\\\\\\/background-footer.png?v=6890618c6ca88);background-position: bottom left;background-repeat: no-repeat;text-align: center;background-color: #FCFCFE;padding-top: 22px;padding-bottom: 12px\\\\\\\">\\\\r\\\\n                                        <div style=\\\\\\\"margin-bottom: 12px;text-align: center\\\\\\\">\\\\r\\\\n                                            <a style=\\\\\\\"padding-left: 16px;padding-right: 16px\\\\\\\" href=\\\\\\\"#\\\\\\\"><img\\\\r\\\\n                                                    src=\\\\\\\"http:\\\\\\/\\\\\\/recland.local\\\\\\/frontend\\\\\\/asset\\\\\\/images\\\\\\/template-email\\\\\\/icon-twitter.png?v=6890618c6caa3\\\\\\\"><\\\\\\/a>\\\\r\\\\n                                            <a style=\\\\\\\"padding-left: 16px;padding-right: 16px\\\\\\\" href=\\\\\\\"#\\\\\\\"><img\\\\r\\\\n                                                    src=\\\\\\\"http:\\\\\\/\\\\\\/recland.local\\\\\\/frontend\\\\\\/asset\\\\\\/images\\\\\\/template-email\\\\\\/icon-facebook.png?v=6890618c6cabd\\\\\\\"><\\\\\\/a>\\\\r\\\\n                                            <a style=\\\\\\\"padding-left: 16px;padding-right: 16px\\\\\\\" href=\\\\\\\"#\\\\\\\"><img\\\\r\\\\n                                                    src=\\\\\\\"http:\\\\\\/\\\\\\/recland.local\\\\\\/frontend\\\\\\/asset\\\\\\/images\\\\\\/template-email\\\\\\/icon-instagram.png?v=6890618c6cad6\\\\\\\"><\\\\\\/a>\\\\r\\\\n                                        <\\\\\\/div>\\\\r\\\\n                                        <p\\\\r\\\\n                                            style=\\\\\\\"margin-top: 0;margin-bottom: 8px;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #17677B;\\\\\\\">\\\\r\\\\n                                            N\\\\u1ec1n t\\\\u1ea3ng t\\\\u1ea1o ra c\\\\u01a1 h\\\\u1ed9i ki\\\\u1ebfm ti\\\\u1ec1n d\\\\u00e0nh cho HR Freelancer<\\\\\\/p>\\\\r\\\\n                                        <p\\\\r\\\\n                                            style=\\\\\\\"margin-top: 0;margin-bottom: 0;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #3C4455;\\\\\\\">\\\\r\\\\n                                            \\\\u00a9 2022 Recland.co<\\\\\\/p>\\\\r\\\\n                                    <\\\\\\/div>\\\\r\\\\n                                <\\\\\\/div>\\\\r\\\\n\\\\r\\\\n                            <\\\\\\/td>\\\\r\\\\n                        <\\\\\\/tr>\\\\r\\\\n                    <\\\\\\/table>\\\\r\\\\n                <\\\\\\/div>\\\\r\\\\n            <\\\\\\/div>\\\\r\\\\n        <\\\\\\/div>\\\\r\\\\n    <\\\\\\/center>\\\\r\\\\n<\\\\\\/body>\\\\r\\\\n\\\\r\\\\n<\\\\\\/html>\\\\r\\\\n\\\\\\\";i:3;s:5:\\\\\\\"utf-8\\\\\\\";i:4;a:0:{}i:5;a:2:{i:0;O:37:\\\\\\\"Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\Headers\\\\\\\":2:{s:46:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\Headers\\\\u0000headers\\\\\\\";a:4:{s:4:\\\\\\\"from\\\\\\\";a:1:{i:0;O:47:\\\\\\\"Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\MailboxListHeader\\\\\\\":5:{s:50:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\AbstractHeader\\\\u0000name\\\\\\\";s:4:\\\\\\\"From\\\\\\\";s:56:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\AbstractHeader\\\\u0000lineLength\\\\\\\";i:76;s:50:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\AbstractHeader\\\\u0000lang\\\\\\\";N;s:53:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\AbstractHeader\\\\u0000charset\\\\\\\";s:5:\\\\\\\"utf-8\\\\\\\";s:58:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\MailboxListHeader\\\\u0000addresses\\\\\\\";a:1:{i:0;O:30:\\\\\\\"Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Address\\\\\\\":2:{s:39:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Address\\\\u0000address\\\\\\\";s:18:\\\\\\\"<EMAIL>\\\\\\\";s:36:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Address\\\\u0000name\\\\\\\";s:7:\\\\\\\"RECLAND\\\\\\\";}}}}s:2:\\\\\\\"to\\\\\\\";a:1:{i:0;O:47:\\\\\\\"Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\MailboxListHeader\\\\\\\":5:{s:50:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\AbstractHeader\\\\u0000name\\\\\\\";s:2:\\\\\\\"To\\\\\\\";s:56:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\AbstractHeader\\\\u0000lineLength\\\\\\\";i:76;s:50:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\AbstractHeader\\\\u0000lang\\\\\\\";N;s:53:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\AbstractHeader\\\\u0000charset\\\\\\\";s:5:\\\\\\\"utf-8\\\\\\\";s:58:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\MailboxListHeader\\\\u0000addresses\\\\\\\";a:1:{i:0;O:30:\\\\\\\"Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Address\\\\\\\":2:{s:39:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Address\\\\u0000address\\\\\\\";s:21:\\\\\\\"<EMAIL>\\\\\\\";s:36:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Address\\\\u0000name\\\\\\\";s:0:\\\\\\\"\\\\\\\";}}}}s:7:\\\\\\\"subject\\\\\\\";a:1:{i:0;O:48:\\\\\\\"Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\UnstructuredHeader\\\\\\\":5:{s:50:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\AbstractHeader\\\\u0000name\\\\\\\";s:7:\\\\\\\"Subject\\\\\\\";s:56:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\AbstractHeader\\\\u0000lineLength\\\\\\\";i:76;s:50:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\AbstractHeader\\\\u0000lang\\\\\\\";N;s:53:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\AbstractHeader\\\\u0000charset\\\\\\\";s:5:\\\\\\\"utf-8\\\\\\\";s:55:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\UnstructuredHeader\\\\u0000value\\\\\\\";s:56:\\\\\\\"[Recland] [C\\\\u1eacP NH\\\\u1eacT H\\\\u1ed2 S\\\\u01a0 C\\\\u00c1 NH\\\\u00c2N TH\\\\u00c0NH C\\\\u00d4NG]\\\\\\\";}}s:17:\\\\\\\"x-original-emails\\\\\\\";a:1:{i:0;O:48:\\\\\\\"Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\UnstructuredHeader\\\\\\\":5:{s:50:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\AbstractHeader\\\\u0000name\\\\\\\";s:17:\\\\\\\"X-Original-Emails\\\\\\\";s:56:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\AbstractHeader\\\\u0000lineLength\\\\\\\";i:76;s:50:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\AbstractHeader\\\\u0000lang\\\\\\\";N;s:53:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\AbstractHeader\\\\u0000charset\\\\\\\";s:5:\\\\\\\"utf-8\\\\\\\";s:55:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\UnstructuredHeader\\\\u0000value\\\\\\\";s:2:\\\\\\\"[]\\\\\\\";}}}s:49:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\Headers\\\\u0000lineLength\\\\\\\";i:76;}i:1;N;}}}\\\"}}', '2025-08-04 14:30:22', '2025-08-04 14:30:22')\\n-- \",\n    \"Time:\": 1.54\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.18385, "xdebug_link": null, "collector": "log"}, {"message": "[14:30:22] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `email_logs` where `hash` = '948b4d111e340bd35280c60c19d84f38' and `created_at` >= '2025-08-04 14:25:22'\\n-- \",\n    \"Time:\": 262.12\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.44895, "xdebug_link": null, "collector": "log"}, {"message": "[14:30:22] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `job_logs` (`job_id`, `name`, `queue`, `payload`, `updated_at`, `created_at`) values ('', 'App\\\\Notifications\\\\UpdateProfileEmployer', 'sync', '{\\\"uuid\\\":\\\"f5647d93-0b41-42fc-97b9-823a61e22242\\\",\\\"displayName\\\":\\\"App\\\\\\\\Notifications\\\\\\\\UpdateProfileEmployer\\\",\\\"job\\\":\\\"Illuminate\\\\\\\\Queue\\\\\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"failOnTimeout\\\":false,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"Illuminate\\\\\\\\Notifications\\\\\\\\SendQueuedNotifications\\\",\\\"command\\\":\\\"O:48:\\\\\\\"Illuminate\\\\\\\\Notifications\\\\\\\\SendQueuedNotifications\\\\\\\":3:{s:11:\\\\\\\"notifiables\\\\\\\";O:45:\\\\\\\"Illuminate\\\\\\\\Contracts\\\\\\\\Database\\\\\\\\ModelIdentifier\\\\\\\":5:{s:5:\\\\\\\"class\\\\\\\";s:15:\\\\\\\"App\\\\\\\\Models\\\\\\\\User\\\\\\\";s:2:\\\\\\\"id\\\\\\\";a:1:{i:0;i:512;}s:9:\\\\\\\"relations\\\\\\\";a:0:{}s:10:\\\\\\\"connection\\\\\\\";s:5:\\\\\\\"mysql\\\\\\\";s:15:\\\\\\\"collectionClass\\\\\\\";N;}s:12:\\\\\\\"notification\\\\\\\";O:39:\\\\\\\"App\\\\\\\\Notifications\\\\\\\\UpdateProfileEmployer\\\\\\\":2:{s:7:\\\\\\\"\\\\u0000*\\\\u0000user\\\\\\\";O:45:\\\\\\\"Illuminate\\\\\\\\Contracts\\\\\\\\Database\\\\\\\\ModelIdentifier\\\\\\\":5:{s:5:\\\\\\\"class\\\\\\\";s:15:\\\\\\\"App\\\\\\\\Models\\\\\\\\User\\\\\\\";s:2:\\\\\\\"id\\\\\\\";i:512;s:9:\\\\\\\"relations\\\\\\\";a:0:{}s:10:\\\\\\\"connection\\\\\\\";s:5:\\\\\\\"mysql\\\\\\\";s:15:\\\\\\\"collectionClass\\\\\\\";N;}s:2:\\\\\\\"id\\\\\\\";s:36:\\\\\\\"ab09a0fa-017b-44cb-b273-dc7211b47c61\\\\\\\";}s:8:\\\\\\\"channels\\\\\\\";a:1:{i:0;s:8:\\\\\\\"database\\\\\\\";}}\\\"}}', '2025-08-04 14:30:22', '2025-08-04 14:30:22')\\n-- \",\n    \"Time:\": 0.69\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.47475, "xdebug_link": null, "collector": "log"}, {"message": "[14:30:22] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `users`.`id` = '512' limit 1\\n-- \",\n    \"Time:\": 0.46\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.47635, "xdebug_link": null, "collector": "log"}, {"message": "[14:30:22] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `users`.`id` in (512)\\n-- \",\n    \"Time:\": 0.34\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.477921, "xdebug_link": null, "collector": "log"}, {"message": "[14:30:22] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect `value`, `key` from `settings` where `key` like '%settings.vi.notification%'\\n-- \",\n    \"Time:\": 5.03\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.500598, "xdebug_link": null, "collector": "log"}, {"message": "[14:30:22] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect `value`, `key` from `settings` where `key` like '%settings.en.notification%'\\n-- \",\n    \"Time:\": 3.44\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.505624, "xdebug_link": null, "collector": "log"}, {"message": "[14:30:22] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `notifications` (`id`, `type`, `data`, `read_at`, `notifiable_id`, `notifiable_type`, `updated_at`, `created_at`) values ('ab09a0fa-017b-44cb-b273-dc7211b47c61', 'App\\\\Notifications\\\\UpdateProfileEmployer', '{\\\"content_vi\\\":\\\"C\\\\u1ed9ng t\\\\u00e1c vi\\\\u00ean \\\\u0111\\\\u00e3 c\\\\u1eadp nh\\\\u1eadt h\\\\u1ed3 s\\\\u01a1 th\\\\u00e0nh c\\\\u00f4ng.\\\",\\\"content_en\\\":\\\"The collaborator has successfully updated the profile.\\\"}', '', '512', 'App\\\\Models\\\\User', '2025-08-04 14:30:22', '2025-08-04 14:30:22')\\n-- \",\n    \"Time:\": 0.62\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.508463, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 3, "start": **********.09039, "end": **********.522604, "duration": 5.432214021682739, "duration_str": "5.43s", "measures": [{"label": "Booting", "start": **********.09039, "relative_start": 0, "end": **********.452812, "relative_end": **********.452812, "duration": 0.*****************, "duration_str": "362ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.452824, "relative_start": 0.*****************, "end": **********.522606, "relative_end": 1.9073486328125e-06, "duration": 5.**************, "duration_str": "5.07s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.464134, "relative_start": 0.*****************, "end": **********.467396, "relative_end": **********.467396, "duration": 0.003262042999267578, "duration_str": "3.26ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 2, "nb_templates": 2, "templates": [{"name": "1x email.updateProfileRec", "param_count": null, "params": [], "start": **********.355591, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/email/updateProfileRec.blade.phpemail.updateProfileRec", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Femail%2FupdateProfileRec.blade.php&line=1", "ajax": false, "filename": "updateProfileRec.blade.php", "line": "?"}, "render_count": 1, "name_original": "email.updateProfileRec"}, {"name": "1x email.master", "param_count": null, "params": [], "start": **********.387707, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/email/master.blade.phpemail.master", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Femail%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}, "render_count": 1, "name_original": "email.master"}]}, "route": {"uri": "POST rec/update-profile", "middleware": "web, localization, visit-website, check-rec", "controller": "App\\Http\\Controllers\\Frontend\\RecController@profilePost<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FRecController.php&line=169\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Frontend", "prefix": "/rec", "as": "rec-update-profile", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FRecController.php&line=169\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/RecController.php:169-182</a>"}, "queries": {"count": 22, "nb_statements": 22, "nb_visible_statements": 22, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.33379000000000003, "accumulated_duration_str": "334ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `seos` where `key` = 'rec-update-profile' limit 1", "type": "query", "params": [], "bindings": ["rec-update-profile"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/SeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SeoRepository.php", "line": 14}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/SeoService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\SeoService.php", "line": 22}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/RecController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\RecController.php", "line": 104}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 929}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 770}], "start": **********.5018718, "duration": 0.0222, "duration_str": "22.2ms", "memory": 0, "memory_str": null, "filename": "SeoRepository.php:14", "source": {"index": 15, "namespace": null, "name": "app/Repositories/SeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SeoRepository.php", "line": 14}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSeoRepository.php&line=14", "ajax": false, "filename": "SeoRepository.php", "line": "14"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `seos` where `key` = ? limit 1", "hash": "68402713c9a7f9ba4e150acd101ff175ff9c302f4b906d356fb660b7bf2712cd"}, "start_percent": 0, "width_percent": 6.651}, {"sql": "insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\\\"_token\\\":\\\"67FrIiAKRzqdDDYE9lpf1v63S50ahQba2svcMgmp\\\",\\\"avatar\\\":null,\\\"cccd_front_image\\\":null,\\\"cccd_back_image\\\":null,\\\"name\\\":\\\"HAU Nguyen <PERSON>hi\\\",\\\"birthday\\\":\\\"04\\/02\\/2000\\\",\\\"mobile\\\":\\\"097845634555\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"address\\\":\\\"60 D\\u01b0\\u01a1ng Khu\\u00ea M\\u1ef9 \\u0110\\u00ecnh2\\\",\\\"cccd_number\\\":null}', 'http://recland.local/rec/update-profile', 'http://recland.local/rec/profile', '[\\\"en-us\\\",\\\"en\\\",\\\"vi\\\",\\\"nl\\\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\\\"host\\\":[\\\"recland.local\\\"],\\\"connection\\\":[\\\"keep-alive\\\"],\\\"content-length\\\":[\\\"1127\\\"],\\\"cache-control\\\":[\\\"max-age=0\\\"],\\\"upgrade-insecure-requests\\\":[\\\"1\\\"],\\\"user-agent\\\":[\\\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\\\"],\\\"origin\\\":[\\\"http:\\/\\/recland.local\\\"],\\\"content-type\\\":[\\\"multipart\\/form-data; boundary=----WebKitFormBoundary1fIoQC6TQk59Uc2h\\\"],\\\"accept\\\":[\\\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\\\"],\\\"referer\\\":[\\\"http:\\/\\/recland.local\\/rec\\/profile\\\"],\\\"accept-encoding\\\":[\\\"gzip, deflate\\\"],\\\"accept-language\\\":[\\\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\\\"],\\\"cookie\\\":[\\\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImxyWFptaVBGK1BuR2MvQWh4MlZpUWc9PSIsInZhbHVlIjoiSGRqMWdEd3lNSS9ZenRwdlNZQ3hjTmV2ZktDRjczQ0FPaWR3NVFHek5vV3o3Tm1nYU1OQzk1dUIxY0ZSRmwyT1ByRC82d0lXRzBWTWRFUTltUGFNQ29ZNmhuU2RKbncwOWU4bXZxaFNPemtoRE1pNVp6b2hsVWh0RjlwSFYzQnEiLCJtYWMiOiI1OGE0MjY5MjNjOGFiOTdkMWQ3NWNlYmRhNTZmMzYyZmNiMWY4MWRjNjA5NTJiNDkxODU3OWEyZWYwODMxZWJhIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6Ik0zS2V3Q25ZT253VG92bGRzYnRBcVE9PSIsInZhbHVlIjoicy9UZ2NLZFNjYmJQOTd2UG4zWGtlcFU3NHdwWldvbFd0VXVVcVVMNDJLWlZjem1PYXlPVFFrb2hMWTVOYVVBcUh3d0xzc0c3SWI1L09uamR6N1dIMjRMWmhpUE5ESkphY0FOditERGlJVXBFcUlXWFgvbXpaYURGbUJoem1xL0YiLCJtYWMiOiJiNTcyZmE5M2ZlMjlkYTA0NjY2OTcxNWI3MjllMDc1N2M0ZGE2YjQyOTVkZTcxYjU1Njg1NmQ5N2E3ODA0YzBlIiwidGFnIjoiIn0%3D\\\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-04 14:30:17', '2025-08-04 14:30:17')", "type": "query", "params": [], "bindings": ["POST", "{\"_token\":\"67FrIiAKRzqdDDYE9lpf1v63S50ahQba2svcMgmp\",\"avatar\":null,\"cccd_front_image\":null,\"cccd_back_image\":null,\"name\":\"<PERSON><PERSON><PERSON>\",\"birthday\":\"04\\/02\\/2000\",\"mobile\":\"097845634555\",\"email\":\"<EMAIL>\",\"address\":\"60 D\\u01b0\\u01a1ng Khu\\u00ea M\\u1ef9 \\u0110\\u00ecnh2\",\"cccd_number\":null}", "http://recland.local/rec/update-profile", "http://recland.local/rec/profile", "[\"en-us\",\"en\",\"vi\",\"nl\"]", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"1127\"],\"cache-control\":[\"max-age=0\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"origin\":[\"http:\\/\\/recland.local\"],\"content-type\":[\"multipart\\/form-data; boundary=----WebKitFormBoundary1fIoQC6TQk59Uc2h\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/rec\\/profile\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImxyWFptaVBGK1BuR2MvQWh4MlZpUWc9PSIsInZhbHVlIjoiSGRqMWdEd3lNSS9ZenRwdlNZQ3hjTmV2ZktDRjczQ0FPaWR3NVFHek5vV3o3Tm1nYU1OQzk1dUIxY0ZSRmwyT1ByRC82d0lXRzBWTWRFUTltUGFNQ29ZNmhuU2RKbncwOWU4bXZxaFNPemtoRE1pNVp6b2hsVWh0RjlwSFYzQnEiLCJtYWMiOiI1OGE0MjY5MjNjOGFiOTdkMWQ3NWNlYmRhNTZmMzYyZmNiMWY4MWRjNjA5NTJiNDkxODU3OWEyZWYwODMxZWJhIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6Ik0zS2V3Q25ZT253VG92bGRzYnRBcVE9PSIsInZhbHVlIjoicy9UZ2NLZFNjYmJQOTd2UG4zWGtlcFU3NHdwWldvbFd0VXVVcVVMNDJLWlZjem1PYXlPVFFrb2hMWTVOYVVBcUh3d0xzc0c3SWI1L09uamR6N1dIMjRMWmhpUE5ESkphY0FOditERGlJVXBFcUlXWFgvbXpaYURGbUJoem1xL0YiLCJtYWMiOiJiNTcyZmE5M2ZlMjlkYTA0NjY2OTcxNWI3MjllMDc1N2M0ZGE2YjQyOTVkZTcxYjU1Njg1NmQ5N2E3ODA0YzBlIiwidGFnIjoiIn0%3D\"]}", "WebKit", "Windows", "Edge", "127.0.0.1", null, null, "2025-08-04 14:30:17", "2025-08-04 14:30:17"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/shetabit/visitor/src/Visitor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\shetabit\\visitor\\src\\Visitor.php", "line": 245}, {"index": 22, "namespace": "middleware", "name": "visit-website", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\VisitWebsiteMiddleware.php", "line": 20}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 24, "namespace": "middleware", "name": "localization", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\Localization.php", "line": 24}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.552984, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "Visitor.php:245", "source": {"index": 21, "namespace": null, "name": "vendor/shetabit/visitor/src/Visitor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\shetabit\\visitor\\src\\Visitor.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fshetabit%2Fvisitor%2Fsrc%2FVisitor.php&line=245", "ajax": false, "filename": "Visitor.php", "line": "245"}, "connection": "hri_recland_product", "explain": null, "start_percent": 6.651, "width_percent": 0.204}, {"sql": "select * from `users` where `id` = 512 limit 1", "type": "query", "params": [], "bindings": [512], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "check-rec", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckRec.php", "line": 21}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.561789, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `id` = ? limit 1", "hash": "f57aeccc1e0beb29028e5e2a4241e4eed6d22790637d6f4352f9bde1e64d2f30"}, "start_percent": 6.855, "width_percent": 0.204}, {"sql": "select * from `users` where `id` != 512 and `email` = '<EMAIL>' and `type` = 'rec' limit 1", "type": "query", "params": [], "bindings": [512, "<EMAIL>", "rec"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserRepository.php", "line": 81}, {"index": 16, "namespace": null, "name": "app/Rules/Admin/CheckEmailRule.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Rules\\Admin\\CheckEmailRule.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 819}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 610}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 422}], "start": **********.579304, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "UserRepository.php:81", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserRepository.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserRepository.php&line=81", "ajax": false, "filename": "UserRepository.php", "line": "81"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `id` != ? and `email` = ? and `type` = ? limit 1", "hash": "91fceb3dbd981d8bcb47e0b21a06b13d665823cd8e02c8a075ba990e0620911a"}, "start_percent": 7.058, "width_percent": 0.192}, {"sql": "select * from `users` where `users`.`id` = 512 limit 1", "type": "query", "params": [], "bindings": [512], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 118}, {"index": 17, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 139}, {"index": 18, "namespace": null, "name": "app/Services/Frontend/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserService.php", "line": 358}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Frontend/RecController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\RecController.php", "line": 176}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.584013, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:118", "source": {"index": 16, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 118}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FBaseRepository.php&line=118", "ajax": false, "filename": "BaseRepository.php", "line": "118"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `users`.`id` = ? limit 1", "hash": "62b695c7a5937b15a7884c504ebc83827f2a9393d8daceb5a22dceb704d4dceb"}, "start_percent": 7.25, "width_percent": 0.162}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'hri_recland_product' and table_name = 'users'", "type": "query", "params": [], "bindings": ["hri_recland_product", "users"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 143}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserService.php", "line": 358}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/RecController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\RecController.php", "line": 176}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.587648, "duration": 0.018170000000000002, "duration_str": "18.17ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:143", "source": {"index": 15, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 143}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FBaseRepository.php&line=143", "ajax": false, "filename": "BaseRepository.php", "line": "143"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select column_name as `column_name` from information_schema.columns where table_schema = ? and table_name = ?", "hash": "2f932a8ab5559dc2e718e2169d292b7c251235f387a3834f8b6fb4b996f87912"}, "start_percent": 7.412, "width_percent": 5.444}, {"sql": "update `users` set `birthday` = '2000-02-04 14:30:17', `users`.`updated_at` = '2025-08-04 14:30:17' where `id` = 512", "type": "query", "params": [], "bindings": ["2000-02-04 14:30:17", "2025-08-04 14:30:17", 512], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 143}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserService.php", "line": 358}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/RecController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\RecController.php", "line": 176}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.607437, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:143", "source": {"index": 14, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 143}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FBaseRepository.php&line=143", "ajax": false, "filename": "BaseRepository.php", "line": "143"}, "connection": "hri_recland_product", "explain": null, "start_percent": 12.855, "width_percent": 0.237}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": null, "name": "vendor/owen-it/laravel-auditing/src/Resolvers/UserResolver.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\owen-it\\laravel-auditing\\src\\Resolvers\\UserResolver.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/owen-it/laravel-auditing/src/Auditable.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\owen-it\\laravel-auditing\\src\\Auditable.php", "line": 409}], "start": **********.610738, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `id` = ? limit 1", "hash": "ac0ef66d2bf40f3993c8e40de021b0d99da887a13adec01b9fbb0601a2bf69c5"}, "start_percent": 13.092, "width_percent": 0.168}, {"sql": "insert into `audits` (`old_values`, `new_values`, `event`, `auditable_id`, `auditable_type`, `user_id`, `user_type`, `tags`, `ip_address`, `user_agent`, `url`, `updated_at`, `created_at`) values ('{\\\"birthday\\\":\\\"2000-02-04\\\"}', '{\\\"birthday\\\":\\\"2000-02-04T07:30:17.000000Z\\\"}', 'updated', 512, 'App\\Models\\User', 1, 'App\\Models\\User', '', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'http://recland.local/rec/update-profile', '2025-08-04 14:30:17', '2025-08-04 14:30:17')", "type": "query", "params": [], "bindings": ["{\"birthday\":\"2000-02-04\"}", "{\"birthday\":\"2000-02-04T07:30:17.000000Z\"}", "updated", 512, "App\\Models\\User", 1, "App\\Models\\User", null, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "http://recland.local/rec/update-profile", "2025-08-04 14:30:17", "2025-08-04 14:30:17"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/owen-it/laravel-auditing/src/Auditor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\owen-it\\laravel-auditing\\src\\Auditor.php", "line": 83}, {"index": 25, "namespace": null, "name": "vendor/owen-it/laravel-auditing/src/AuditableObserver.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\owen-it\\laravel-auditing\\src\\AuditableObserver.php", "line": 99}, {"index": 26, "namespace": null, "name": "vendor/owen-it/laravel-auditing/src/AuditableObserver.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\owen-it\\laravel-auditing\\src\\AuditableObserver.php", "line": 49}, {"index": 33, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 143}, {"index": 34, "namespace": null, "name": "app/Services/Frontend/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserService.php", "line": 358}], "start": **********.615973, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "Auditor.php:83", "source": {"index": 23, "namespace": null, "name": "vendor/owen-it/laravel-auditing/src/Auditor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\owen-it\\laravel-auditing\\src\\Auditor.php", "line": 83}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fowen-it%2Flaravel-auditing%2Fsrc%2FAuditor.php&line=83", "ajax": false, "filename": "Auditor.php", "line": "83"}, "connection": "hri_recland_product", "explain": null, "start_percent": 13.26, "width_percent": 0.213}, {"sql": "select * from `users` where `users`.`id` = 512 limit 1", "type": "query", "params": [], "bindings": [512], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 118}, {"index": 17, "namespace": null, "name": "app/Services/Frontend/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserService.php", "line": 379}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Frontend/RecController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\RecController.php", "line": 176}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.617899, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:118", "source": {"index": 16, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 118}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FBaseRepository.php&line=118", "ajax": false, "filename": "BaseRepository.php", "line": "118"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `users`.`id` = ? limit 1", "hash": "62b695c7a5937b15a7884c504ebc83827f2a9393d8daceb5a22dceb704d4dceb"}, "start_percent": 13.473, "width_percent": 0.183}, {"sql": "insert into `job_logs` (`job_id`, `name`, `queue`, `payload`, `updated_at`, `created_at`) values ('', 'App\\Notifications\\UpdateProfileEmployer', 'sync', '{\\\"uuid\\\":\\\"b76be44e-ea3b-45b4-a0b2-7a6993223046\\\",\\\"displayName\\\":\\\"App\\\\Notifications\\\\UpdateProfileEmployer\\\",\\\"job\\\":\\\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"failOnTimeout\\\":false,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\",\\\"command\\\":\\\"O:48:\\\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\\\":3:{s:11:\\\\\"notifiables\\\\\";O:45:\\\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\\\":5:{s:5:\\\\\"class\\\\\";s:15:\\\\\"App\\\\Models\\\\User\\\\\";s:2:\\\\\"id\\\\\";a:1:{i:0;i:512;}s:9:\\\\\"relations\\\\\";a:0:{}s:10:\\\\\"connection\\\\\";s:5:\\\\\"mysql\\\\\";s:15:\\\\\"collectionClass\\\\\";N;}s:12:\\\\\"notification\\\\\";O:39:\\\\\"App\\\\Notifications\\\\UpdateProfileEmployer\\\\\":2:{s:7:\\\\\"\\u0000*\\u0000user\\\\\";O:45:\\\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\\\":5:{s:5:\\\\\"class\\\\\";s:15:\\\\\"App\\\\Models\\\\User\\\\\";s:2:\\\\\"id\\\\\";i:512;s:9:\\\\\"relations\\\\\";a:0:{}s:10:\\\\\"connection\\\\\";s:5:\\\\\"mysql\\\\\";s:15:\\\\\"collectionClass\\\\\";N;}s:2:\\\\\"id\\\\\";s:36:\\\\\"ab09a0fa-017b-44cb-b273-dc7211b47c61\\\\\";}s:8:\\\\\"channels\\\\\";a:1:{i:0;s:4:\\\\\"mail\\\\\";}}\\\"}}', '2025-08-04 14:30:19', '2025-08-04 14:30:19')", "type": "query", "params": [], "bindings": ["", "App\\Notifications\\UpdateProfileEmployer", "sync", "{\"uuid\":\"b76be44e-ea3b-45b4-a0b2-7a6993223046\",\"displayName\":\"App\\\\Notifications\\\\UpdateProfileEmployer\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:512;}s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:39:\\\"App\\\\Notifications\\\\UpdateProfileEmployer\\\":2:{s:7:\\\"\\u0000*\\u0000user\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";i:512;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"ab09a0fa-017b-44cb-b273-dc7211b47c61\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:4:\\\"mail\\\";}}\"}}", "2025-08-04 14:30:19", "2025-08-04 14:30:19"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Providers\\AppServiceProvider.php", "line": 75}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SyncQueue.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SyncQueue.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SyncQueue.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SyncQueue.php", "line": 41}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php", "line": 253}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php", "line": 229}], "start": **********.449123, "duration": 0.01106, "duration_str": "11.06ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:75", "source": {"index": 21, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Providers\\AppServiceProvider.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FProviders%2FAppServiceProvider.php&line=75", "ajax": false, "filename": "AppServiceProvider.php", "line": "75"}, "connection": "hri_recland_product", "explain": null, "start_percent": 13.655, "width_percent": 3.313}, {"sql": "select * from `users` where `users`.`id` = 512 limit 1", "type": "query", "params": [], "bindings": [512], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesModels.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesModels.php", "line": 88}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php", "line": 60}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php", "line": 98}], "start": **********.498332, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "SerializesAndRestoresModelIdentifiers.php:108", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FSerializesAndRestoresModelIdentifiers.php&line=108", "ajax": false, "filename": "SerializesAndRestoresModelIdentifiers.php", "line": "108"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `users`.`id` = ? limit 1", "hash": "62b695c7a5937b15a7884c504ebc83827f2a9393d8daceb5a22dceb704d4dceb"}, "start_percent": 16.969, "width_percent": 0.192}, {"sql": "select * from `users` where `users`.`id` in (512)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 60}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesModels.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesModels.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php", "line": 60}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php", "line": 98}], "start": **********.500456, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "SerializesAndRestoresModelIdentifiers.php:80", "source": {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FSerializesAndRestoresModelIdentifiers.php&line=80", "ajax": false, "filename": "SerializesAndRestoresModelIdentifiers.php", "line": "80"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `users`.`id` in (512)", "hash": "1216ff3bf8b8e0ab1053a91cfaebee74f0f91ec9ac571cfdf9656de6355aa46f"}, "start_percent": 17.16, "width_percent": 0.156}, {"sql": "insert into `email_logs` (`from`, `to`, `cc`, `subject`, `html_content`, `hash`, `status`, `updated_at`, `created_at`) values ('[\\\"<EMAIL>\\\"]', '[\\\"<EMAIL>\\\"]', '[]', '[Recland] [CẬP NHẬT HỒ SƠ CÁ NHÂN THÀNH CÔNG]', '<!DOCTYPE html>\\r\\n<html>\\r\\n\\r\\n<body width=\\\"100%\\\"\\r\\n    style=\\\"margin: 0; padding: 0 !important; mso-line-height-rule: exactly; background-color: #f1f1f1;font-family: Arial, \\'Helvetica Neue\\', Helvetica, sans-serif\\\">\\r\\n    <center style=\\\"width: 100%; background-color: #f1f1f1;\\\">\\r\\n        <div style=\\\"max-width: 600px; margin: 0 auto;\\\">\\r\\n            <table align=\\\"center\\\" role=\\\"presentation\\\" cellspacing=\\\"0\\\" cellpadding=\\\"0\\\" border=\\\"0\\\" width=\\\"100%\\\"\\r\\n                style=\\\"margin: auto;\\\">\\r\\n                <tr>\\r\\n                    <td>\\r\\n                        <div style=\\\"text-align: center;padding: 25px 0;background: #FCFCFE;\\\">\\r\\n                            <img style=\\\"max-width: 100%\\\"\\r\\n                                src=\\\"http://recland.local/frontend/asset/images/template-email/logo.png?v=6890618c6ca18\\\">\\r\\n                        </div>\\r\\n                    </td>\\r\\n                </tr>\\r\\n            </table>\\r\\n            <div style=\\\"background-image: linear-gradient(192.45deg, #155969 18.03%, #DAE6E9 93.73%);\\\">\\r\\n                <div\\r\\n                    style=\\\"padding-top: 0;padding-left: 40px;padding-right: 40px;padding-bottom: 32px;\\r\\n                background-image: url(http://recland.local/frontend/asset/images/template-email/background.png?v=6890618c6ca42);\\r\\n                background-repeat: no-repeat;background-size: 100%;\\\">\\r\\n                    <table>\\r\\n                        <tr>\\r\\n                            <td>\\r\\n                                <div\\r\\n                                    style=\\\"padding-top: 80px;height: 194px;text-align: center;font-style: normal;font-weight: 600;font-size: 26px;line-height: 31px;color: #FCFCFE;\\\">\\r\\n                                    <div style=\\\"margin-bottom: 14px\\\">\\r\\n                                                                            </div>\\r\\n                                    <div>\\r\\n                                                                            </div>\\r\\n\\r\\n                                </div>\\r\\n                            </td>\\r\\n                        </tr>\\r\\n                    </table>\\r\\n                    <table style=\\\"width: 100%\\\" role=\\\"presentation\\\" cellspacing=\\\"0\\\" cellpadding=\\\"0\\\" border=\\\"0\\\"\\r\\n                        width=\\\"100%\\\">\\r\\n                        <tr>\\r\\n                            <td>\\r\\n                                    <div\\r\\n        style=\\\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\\\">\\r\\n        <p>Xin chào, HAU Nguyen Thi</p>\\r\\n        Recland thông báo hồ sơ của bạn đã được cập nhật thành công.\\r\\n        <p><b>Trân trọng,</b></p>\\r\\n        <p><i>Đội ngũ Recland.</i></p>\\r\\n    </div>\\r\\n    <div style=\\\"border: 5px solid #F7F7F7;\\\"></div>\\r\\n    <div\\r\\n        style=\\\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\\\">\\r\\n        <p>Hello, HAU Nguyen Thi</p>\\r\\n        Recland informs you that your profile has been successfully updated.\\r\\n        <p><b>Best regards,</b></p>\\r\\n        <p><i>Recland team.</i></p>\\r\\n    </div>\\r\\n                            </td>\\r\\n                        </tr>\\r\\n                        <tr>\\r\\n                            <td>\\r\\n                                <div style=\\\"padding:12px 0\\\">\\r\\n                                    <div\\r\\n                                        style=\\\"background-image: url(http://recland.local/frontend/asset/images/template-email/background-footer.png?v=6890618c6ca88);background-position: bottom left;background-repeat: no-repeat;text-align: center;background-color: #FCFCFE;padding-top: 22px;padding-bottom: 12px\\\">\\r\\n                                        <div style=\\\"margin-bottom: 12px;text-align: center\\\">\\r\\n                                            <a style=\\\"padding-left: 16px;padding-right: 16px\\\" href=\\\"#\\\"><img\\r\\n                                                    src=\\\"http://recland.local/frontend/asset/images/template-email/icon-twitter.png?v=6890618c6caa3\\\"></a>\\r\\n                                            <a style=\\\"padding-left: 16px;padding-right: 16px\\\" href=\\\"#\\\"><img\\r\\n                                                    src=\\\"http://recland.local/frontend/asset/images/template-email/icon-facebook.png?v=6890618c6cabd\\\"></a>\\r\\n                                            <a style=\\\"padding-left: 16px;padding-right: 16px\\\" href=\\\"#\\\"><img\\r\\n                                                    src=\\\"http://recland.local/frontend/asset/images/template-email/icon-instagram.png?v=6890618c6cad6\\\"></a>\\r\\n                                        </div>\\r\\n                                        <p\\r\\n                                            style=\\\"margin-top: 0;margin-bottom: 8px;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #17677B;\\\">\\r\\n                                            Nền tảng tạo ra cơ hội kiếm tiền dành cho HR Freelancer</p>\\r\\n                                        <p\\r\\n                                            style=\\\"margin-top: 0;margin-bottom: 0;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #3C4455;\\\">\\r\\n                                            © 2022 Recland.co</p>\\r\\n                                    </div>\\r\\n                                </div>\\r\\n\\r\\n                            </td>\\r\\n                        </tr>\\r\\n                    </table>\\r\\n                </div>\\r\\n            </div>\\r\\n        </div>\\r\\n    </center>\\r\\n</body>\\r\\n\\r\\n</html>\\r\\n', 'a14feb746f7078d6df8aa7c2bfa18c50', 0, '2025-08-04 14:30:20', '2025-08-04 14:30:20')", "type": "query", "params": [], "bindings": ["[\"<EMAIL>\"]", "[\"<EMAIL>\"]", "[]", "[Recland] [CẬP NHẬT HỒ SƠ CÁ NHÂN THÀNH CÔNG]", "<!DOCTYPE html>\r\n<html>\r\n\r\n<body width=\"100%\"\r\n    style=\"margin: 0; padding: 0 !important; mso-line-height-rule: exactly; background-color: #f1f1f1;font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif\">\r\n    <center style=\"width: 100%; background-color: #f1f1f1;\">\r\n        <div style=\"max-width: 600px; margin: 0 auto;\">\r\n            <table align=\"center\" role=\"presentation\" cellspacing=\"0\" cellpadding=\"0\" border=\"0\" width=\"100%\"\r\n                style=\"margin: auto;\">\r\n                <tr>\r\n                    <td>\r\n                        <div style=\"text-align: center;padding: 25px 0;background: #FCFCFE;\">\r\n                            <img style=\"max-width: 100%\"\r\n                                src=\"http://recland.local/frontend/asset/images/template-email/logo.png?v=6890618c6ca18\">\r\n                        </div>\r\n                    </td>\r\n                </tr>\r\n            </table>\r\n            <div style=\"background-image: linear-gradient(192.45deg, #155969 18.03%, #DAE6E9 93.73%);\">\r\n                <div\r\n                    style=\"padding-top: 0;padding-left: 40px;padding-right: 40px;padding-bottom: 32px;\r\n                background-image: url(http://recland.local/frontend/asset/images/template-email/background.png?v=6890618c6ca42);\r\n                background-repeat: no-repeat;background-size: 100%;\">\r\n                    <table>\r\n                        <tr>\r\n                            <td>\r\n                                <div\r\n                                    style=\"padding-top: 80px;height: 194px;text-align: center;font-style: normal;font-weight: 600;font-size: 26px;line-height: 31px;color: #FCFCFE;\">\r\n                                    <div style=\"margin-bottom: 14px\">\r\n                                                                            </div>\r\n                                    <div>\r\n                                                                            </div>\r\n\r\n                                </div>\r\n                            </td>\r\n                        </tr>\r\n                    </table>\r\n                    <table style=\"width: 100%\" role=\"presentation\" cellspacing=\"0\" cellpadding=\"0\" border=\"0\"\r\n                        width=\"100%\">\r\n                        <tr>\r\n                            <td>\r\n                                    <div\r\n        style=\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\">\r\n        <p>Xin chào, HAU Nguyen Thi</p>\r\n        Recland thông báo hồ sơ của bạn đã được cập nhật thành công.\r\n        <p><b>Trân trọng,</b></p>\r\n        <p><i>Đội ngũ Recland.</i></p>\r\n    </div>\r\n    <div style=\"border: 5px solid #F7F7F7;\"></div>\r\n    <div\r\n        style=\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\">\r\n        <p>Hello, HAU Nguyen Thi</p>\r\n        Recland informs you that your profile has been successfully updated.\r\n        <p><b>Best regards,</b></p>\r\n        <p><i>Recland team.</i></p>\r\n    </div>\r\n                            </td>\r\n                        </tr>\r\n                        <tr>\r\n                            <td>\r\n                                <div style=\"padding:12px 0\">\r\n                                    <div\r\n                                        style=\"background-image: url(http://recland.local/frontend/asset/images/template-email/background-footer.png?v=6890618c6ca88);background-position: bottom left;background-repeat: no-repeat;text-align: center;background-color: #FCFCFE;padding-top: 22px;padding-bottom: 12px\">\r\n                                        <div style=\"margin-bottom: 12px;text-align: center\">\r\n                                            <a style=\"padding-left: 16px;padding-right: 16px\" href=\"#\"><img\r\n                                                    src=\"http://recland.local/frontend/asset/images/template-email/icon-twitter.png?v=6890618c6caa3\"></a>\r\n                                            <a style=\"padding-left: 16px;padding-right: 16px\" href=\"#\"><img\r\n                                                    src=\"http://recland.local/frontend/asset/images/template-email/icon-facebook.png?v=6890618c6cabd\"></a>\r\n                                            <a style=\"padding-left: 16px;padding-right: 16px\" href=\"#\"><img\r\n                                                    src=\"http://recland.local/frontend/asset/images/template-email/icon-instagram.png?v=6890618c6cad6\"></a>\r\n                                        </div>\r\n                                        <p\r\n                                            style=\"margin-top: 0;margin-bottom: 8px;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #17677B;\">\r\n                                            Nền tảng tạo ra cơ hội kiếm tiền dành cho HR Freelancer</p>\r\n                                        <p\r\n                                            style=\"margin-top: 0;margin-bottom: 0;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #3C4455;\">\r\n                                            © 2022 Recland.co</p>\r\n                                    </div>\r\n                                </div>\r\n\r\n                            </td>\r\n                        </tr>\r\n                    </table>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </center>\r\n</body>\r\n\r\n</html>\r\n", "a14feb746f7078d6df8aa7c2bfa18c50", 0, "2025-08-04 14:30:20", "2025-08-04 14:30:20"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Jobs/CreateEmailLog.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Jobs\\CreateEmailLog.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 37}], "start": **********.544422, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "CreateEmailLog.php:68", "source": {"index": 21, "namespace": null, "name": "app/Jobs/CreateEmailLog.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Jobs\\CreateEmailLog.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FJobs%2FCreateEmailLog.php&line=68", "ajax": false, "filename": "CreateEmailLog.php", "line": "68"}, "connection": "hri_recland_product", "explain": null, "start_percent": 17.316, "width_percent": 0.524}, {"sql": "insert into `job_logs` (`job_id`, `name`, `queue`, `payload`, `updated_at`, `created_at`) values ('', 'App\\Jobs\\UpdateEmailLogStatus', 'sync', '{\\\"uuid\\\":\\\"e64860ca-3568-4942-ab79-d8ffdac0c70a\\\",\\\"displayName\\\":\\\"App\\\\Jobs\\\\UpdateEmailLogStatus\\\",\\\"job\\\":\\\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"failOnTimeout\\\":false,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"App\\\\Jobs\\\\UpdateEmailLogStatus\\\",\\\"command\\\":\\\"O:29:\\\\\"App\\\\Jobs\\\\UpdateEmailLogStatus\\\\\":1:{s:5:\\\\\"email\\\\\";O:28:\\\\\"Symfony\\\\Component\\\\Mime\\\\Email\\\\\":6:{i:0;N;i:1;N;i:2;s:5639:\\\\\"<!DOCTYPE html>\\r\\n<html>\\r\\n\\r\\n<body width=\\\\\"100%\\\\\"\\r\\n    style=\\\\\"margin: 0; padding: 0 !important; mso-line-height-rule: exactly; background-color: #f1f1f1;font-family: Arial, \\'Helvetica Neue\\', Helvetica, sans-serif\\\\\">\\r\\n    <center style=\\\\\"width: 100%; background-color: #f1f1f1;\\\\\">\\r\\n        <div style=\\\\\"max-width: 600px; margin: 0 auto;\\\\\">\\r\\n            <table align=\\\\\"center\\\\\" role=\\\\\"presentation\\\\\" cellspacing=\\\\\"0\\\\\" cellpadding=\\\\\"0\\\\\" border=\\\\\"0\\\\\" width=\\\\\"100%\\\\\"\\r\\n                style=\\\\\"margin: auto;\\\\\">\\r\\n                <tr>\\r\\n                    <td>\\r\\n                        <div style=\\\\\"text-align: center;padding: 25px 0;background: #FCFCFE;\\\\\">\\r\\n                            <img style=\\\\\"max-width: 100%\\\\\"\\r\\n                                src=\\\\\"http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/logo.png?v=6890618c6ca18\\\\\">\\r\\n                        <\\/div>\\r\\n                    <\\/td>\\r\\n                <\\/tr>\\r\\n            <\\/table>\\r\\n            <div style=\\\\\"background-image: linear-gradient(192.45deg, #155969 18.03%, #DAE6E9 93.73%);\\\\\">\\r\\n                <div\\r\\n                    style=\\\\\"padding-top: 0;padding-left: 40px;padding-right: 40px;padding-bottom: 32px;\\r\\n                background-image: url(http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/background.png?v=6890618c6ca42);\\r\\n                background-repeat: no-repeat;background-size: 100%;\\\\\">\\r\\n                    <table>\\r\\n                        <tr>\\r\\n                            <td>\\r\\n                                <div\\r\\n                                    style=\\\\\"padding-top: 80px;height: 194px;text-align: center;font-style: normal;font-weight: 600;font-size: 26px;line-height: 31px;color: #FCFCFE;\\\\\">\\r\\n                                    <div style=\\\\\"margin-bottom: 14px\\\\\">\\r\\n                                                                            <\\/div>\\r\\n                                    <div>\\r\\n                                                                            <\\/div>\\r\\n\\r\\n                                <\\/div>\\r\\n                            <\\/td>\\r\\n                        <\\/tr>\\r\\n                    <\\/table>\\r\\n                    <table style=\\\\\"width: 100%\\\\\" role=\\\\\"presentation\\\\\" cellspacing=\\\\\"0\\\\\" cellpadding=\\\\\"0\\\\\" border=\\\\\"0\\\\\"\\r\\n                        width=\\\\\"100%\\\\\">\\r\\n                        <tr>\\r\\n                            <td>\\r\\n                                    <div\\r\\n        style=\\\\\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\\\\\">\\r\\n        <p>Xin ch\\u00e0o, HAU Nguyen Thi<\\/p>\\r\\n        Recland th\\u00f4ng b\\u00e1o h\\u1ed3 s\\u01a1 c\\u1ee7a b\\u1ea1n \\u0111\\u00e3 \\u0111\\u01b0\\u1ee3c c\\u1eadp nh\\u1eadt th\\u00e0nh c\\u00f4ng.\\r\\n        <p><b>Tr\\u00e2n tr\\u1ecdng,<\\/b><\\/p>\\r\\n        <p><i>\\u0110\\u1ed9i ng\\u0169 Recland.<\\/i><\\/p>\\r\\n    <\\/div>\\r\\n    <div style=\\\\\"border: 5px solid #F7F7F7;\\\\\"><\\/div>\\r\\n    <div\\r\\n        style=\\\\\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\\\\\">\\r\\n        <p>Hello, HAU Nguyen Thi<\\/p>\\r\\n        Recland informs you that your profile has been successfully updated.\\r\\n        <p><b>Best regards,<\\/b><\\/p>\\r\\n        <p><i>Recland team.<\\/i><\\/p>\\r\\n    <\\/div>\\r\\n                            <\\/td>\\r\\n                        <\\/tr>\\r\\n                        <tr>\\r\\n                            <td>\\r\\n                                <div style=\\\\\"padding:12px 0\\\\\">\\r\\n                                    <div\\r\\n                                        style=\\\\\"background-image: url(http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/background-footer.png?v=6890618c6ca88);background-position: bottom left;background-repeat: no-repeat;text-align: center;background-color: #FCFCFE;padding-top: 22px;padding-bottom: 12px\\\\\">\\r\\n                                        <div style=\\\\\"margin-bottom: 12px;text-align: center\\\\\">\\r\\n                                            <a style=\\\\\"padding-left: 16px;padding-right: 16px\\\\\" href=\\\\\"#\\\\\"><img\\r\\n                                                    src=\\\\\"http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/icon-twitter.png?v=6890618c6caa3\\\\\"><\\/a>\\r\\n                                            <a style=\\\\\"padding-left: 16px;padding-right: 16px\\\\\" href=\\\\\"#\\\\\"><img\\r\\n                                                    src=\\\\\"http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/icon-facebook.png?v=6890618c6cabd\\\\\"><\\/a>\\r\\n                                            <a style=\\\\\"padding-left: 16px;padding-right: 16px\\\\\" href=\\\\\"#\\\\\"><img\\r\\n                                                    src=\\\\\"http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/icon-instagram.png?v=6890618c6cad6\\\\\"><\\/a>\\r\\n                                        <\\/div>\\r\\n                                        <p\\r\\n                                            style=\\\\\"margin-top: 0;margin-bottom: 8px;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #17677B;\\\\\">\\r\\n                                            N\\u1ec1n t\\u1ea3ng t\\u1ea1o ra c\\u01a1 h\\u1ed9i ki\\u1ebfm ti\\u1ec1n d\\u00e0nh cho HR Freelancer<\\/p>\\r\\n                                        <p\\r\\n                                            style=\\\\\"margin-top: 0;margin-bottom: 0;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #3C4455;\\\\\">\\r\\n                                            \\u00a9 2022 Recland.co<\\/p>\\r\\n                                    <\\/div>\\r\\n                                <\\/div>\\r\\n\\r\\n                            <\\/td>\\r\\n                        <\\/tr>\\r\\n                    <\\/table>\\r\\n                <\\/div>\\r\\n            <\\/div>\\r\\n        <\\/div>\\r\\n    <\\/center>\\r\\n<\\/body>\\r\\n\\r\\n<\\/html>\\r\\n\\\\\";i:3;s:5:\\\\\"utf-8\\\\\";i:4;a:0:{}i:5;a:2:{i:0;O:37:\\\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\Headers\\\\\":2:{s:46:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\Headers\\u0000headers\\\\\";a:4:{s:4:\\\\\"from\\\\\";a:1:{i:0;O:47:\\\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\\\\":5:{s:50:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\\\";s:4:\\\\\"From\\\\\";s:56:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\\\";i:76;s:50:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\\\";N;s:53:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\\\";s:5:\\\\\"utf-8\\\\\";s:58:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\u0000addresses\\\\\";a:1:{i:0;O:30:\\\\\"Symfony\\\\Component\\\\Mime\\\\Address\\\\\":2:{s:39:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000address\\\\\";s:18:\\\\\"<EMAIL>\\\\\";s:36:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000name\\\\\";s:7:\\\\\"RECLAND\\\\\";}}}}s:2:\\\\\"to\\\\\";a:1:{i:0;O:47:\\\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\\\\":5:{s:50:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\\\";s:2:\\\\\"To\\\\\";s:56:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\\\";i:76;s:50:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\\\";N;s:53:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\\\";s:5:\\\\\"utf-8\\\\\";s:58:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\u0000addresses\\\\\";a:1:{i:0;O:30:\\\\\"Symfony\\\\Component\\\\Mime\\\\Address\\\\\":2:{s:39:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000address\\\\\";s:21:\\\\\"<EMAIL>\\\\\";s:36:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000name\\\\\";s:0:\\\\\"\\\\\";}}}}s:7:\\\\\"subject\\\\\";a:1:{i:0;O:48:\\\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\\\\":5:{s:50:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\\\";s:7:\\\\\"Subject\\\\\";s:56:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\\\";i:76;s:50:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\\\";N;s:53:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\\\";s:5:\\\\\"utf-8\\\\\";s:55:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\u0000value\\\\\";s:56:\\\\\"[Recland] [C\\u1eacP NH\\u1eacT H\\u1ed2 S\\u01a0 C\\u00c1 NH\\u00c2N TH\\u00c0NH C\\u00d4NG]\\\\\";}}s:17:\\\\\"x-original-emails\\\\\";a:1:{i:0;O:48:\\\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\\\\":5:{s:50:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\\\";s:17:\\\\\"X-Original-Emails\\\\\";s:56:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\\\";i:76;s:50:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\\\";N;s:53:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\\\";s:5:\\\\\"utf-8\\\\\";s:55:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\u0000value\\\\\";s:2:\\\\\"[]\\\\\";}}}s:49:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\Headers\\u0000lineLength\\\\\";i:76;}i:1;N;}}}\\\"}}', '2025-08-04 14:30:22', '2025-08-04 14:30:22')", "type": "query", "params": [], "bindings": ["", "App\\Jobs\\UpdateEmailLogStatus", "sync", "{\"uuid\":\"e64860ca-3568-4942-ab79-d8ffdac0c70a\",\"displayName\":\"App\\\\Jobs\\\\UpdateEmailLogStatus\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\UpdateEmailLogStatus\",\"command\":\"O:29:\\\"App\\\\Jobs\\\\UpdateEmailLogStatus\\\":1:{s:5:\\\"email\\\";O:28:\\\"Symfony\\\\Component\\\\Mime\\\\Email\\\":6:{i:0;N;i:1;N;i:2;s:5639:\\\"<!DOCTYPE html>\\r\\n<html>\\r\\n\\r\\n<body width=\\\"100%\\\"\\r\\n    style=\\\"margin: 0; padding: 0 !important; mso-line-height-rule: exactly; background-color: #f1f1f1;font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif\\\">\\r\\n    <center style=\\\"width: 100%; background-color: #f1f1f1;\\\">\\r\\n        <div style=\\\"max-width: 600px; margin: 0 auto;\\\">\\r\\n            <table align=\\\"center\\\" role=\\\"presentation\\\" cellspacing=\\\"0\\\" cellpadding=\\\"0\\\" border=\\\"0\\\" width=\\\"100%\\\"\\r\\n                style=\\\"margin: auto;\\\">\\r\\n                <tr>\\r\\n                    <td>\\r\\n                        <div style=\\\"text-align: center;padding: 25px 0;background: #FCFCFE;\\\">\\r\\n                            <img style=\\\"max-width: 100%\\\"\\r\\n                                src=\\\"http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/logo.png?v=6890618c6ca18\\\">\\r\\n                        <\\/div>\\r\\n                    <\\/td>\\r\\n                <\\/tr>\\r\\n            <\\/table>\\r\\n            <div style=\\\"background-image: linear-gradient(192.45deg, #155969 18.03%, #DAE6E9 93.73%);\\\">\\r\\n                <div\\r\\n                    style=\\\"padding-top: 0;padding-left: 40px;padding-right: 40px;padding-bottom: 32px;\\r\\n                background-image: url(http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/background.png?v=6890618c6ca42);\\r\\n                background-repeat: no-repeat;background-size: 100%;\\\">\\r\\n                    <table>\\r\\n                        <tr>\\r\\n                            <td>\\r\\n                                <div\\r\\n                                    style=\\\"padding-top: 80px;height: 194px;text-align: center;font-style: normal;font-weight: 600;font-size: 26px;line-height: 31px;color: #FCFCFE;\\\">\\r\\n                                    <div style=\\\"margin-bottom: 14px\\\">\\r\\n                                                                            <\\/div>\\r\\n                                    <div>\\r\\n                                                                            <\\/div>\\r\\n\\r\\n                                <\\/div>\\r\\n                            <\\/td>\\r\\n                        <\\/tr>\\r\\n                    <\\/table>\\r\\n                    <table style=\\\"width: 100%\\\" role=\\\"presentation\\\" cellspacing=\\\"0\\\" cellpadding=\\\"0\\\" border=\\\"0\\\"\\r\\n                        width=\\\"100%\\\">\\r\\n                        <tr>\\r\\n                            <td>\\r\\n                                    <div\\r\\n        style=\\\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\\\">\\r\\n        <p>Xin ch\\u00e0o, HAU Nguyen Thi<\\/p>\\r\\n        Recland th\\u00f4ng b\\u00e1o h\\u1ed3 s\\u01a1 c\\u1ee7a b\\u1ea1n \\u0111\\u00e3 \\u0111\\u01b0\\u1ee3c c\\u1eadp nh\\u1eadt th\\u00e0nh c\\u00f4ng.\\r\\n        <p><b>Tr\\u00e2n tr\\u1ecdng,<\\/b><\\/p>\\r\\n        <p><i>\\u0110\\u1ed9i ng\\u0169 Recland.<\\/i><\\/p>\\r\\n    <\\/div>\\r\\n    <div style=\\\"border: 5px solid #F7F7F7;\\\"><\\/div>\\r\\n    <div\\r\\n        style=\\\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\\\">\\r\\n        <p>Hello, HAU Nguyen Thi<\\/p>\\r\\n        Recland informs you that your profile has been successfully updated.\\r\\n        <p><b>Best regards,<\\/b><\\/p>\\r\\n        <p><i>Recland team.<\\/i><\\/p>\\r\\n    <\\/div>\\r\\n                            <\\/td>\\r\\n                        <\\/tr>\\r\\n                        <tr>\\r\\n                            <td>\\r\\n                                <div style=\\\"padding:12px 0\\\">\\r\\n                                    <div\\r\\n                                        style=\\\"background-image: url(http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/background-footer.png?v=6890618c6ca88);background-position: bottom left;background-repeat: no-repeat;text-align: center;background-color: #FCFCFE;padding-top: 22px;padding-bottom: 12px\\\">\\r\\n                                        <div style=\\\"margin-bottom: 12px;text-align: center\\\">\\r\\n                                            <a style=\\\"padding-left: 16px;padding-right: 16px\\\" href=\\\"#\\\"><img\\r\\n                                                    src=\\\"http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/icon-twitter.png?v=6890618c6caa3\\\"><\\/a>\\r\\n                                            <a style=\\\"padding-left: 16px;padding-right: 16px\\\" href=\\\"#\\\"><img\\r\\n                                                    src=\\\"http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/icon-facebook.png?v=6890618c6cabd\\\"><\\/a>\\r\\n                                            <a style=\\\"padding-left: 16px;padding-right: 16px\\\" href=\\\"#\\\"><img\\r\\n                                                    src=\\\"http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/icon-instagram.png?v=6890618c6cad6\\\"><\\/a>\\r\\n                                        <\\/div>\\r\\n                                        <p\\r\\n                                            style=\\\"margin-top: 0;margin-bottom: 8px;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #17677B;\\\">\\r\\n                                            N\\u1ec1n t\\u1ea3ng t\\u1ea1o ra c\\u01a1 h\\u1ed9i ki\\u1ebfm ti\\u1ec1n d\\u00e0nh cho HR Freelancer<\\/p>\\r\\n                                        <p\\r\\n                                            style=\\\"margin-top: 0;margin-bottom: 0;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #3C4455;\\\">\\r\\n                                            \\u00a9 2022 Recland.co<\\/p>\\r\\n                                    <\\/div>\\r\\n                                <\\/div>\\r\\n\\r\\n                            <\\/td>\\r\\n                        <\\/tr>\\r\\n                    <\\/table>\\r\\n                <\\/div>\\r\\n            <\\/div>\\r\\n        <\\/div>\\r\\n    <\\/center>\\r\\n<\\/body>\\r\\n\\r\\n<\\/html>\\r\\n\\\";i:3;s:5:\\\"utf-8\\\";i:4;a:0:{}i:5;a:2:{i:0;O:37:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\Headers\\\":2:{s:46:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\Headers\\u0000headers\\\";a:4:{s:4:\\\"from\\\";a:1:{i:0;O:47:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\\":5:{s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\";s:4:\\\"From\\\";s:56:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\";i:76;s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\";N;s:53:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\";s:5:\\\"utf-8\\\";s:58:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\u0000addresses\\\";a:1:{i:0;O:30:\\\"Symfony\\\\Component\\\\Mime\\\\Address\\\":2:{s:39:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000address\\\";s:18:\\\"<EMAIL>\\\";s:36:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000name\\\";s:7:\\\"RECLAND\\\";}}}}s:2:\\\"to\\\";a:1:{i:0;O:47:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\\":5:{s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\";s:2:\\\"To\\\";s:56:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\";i:76;s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\";N;s:53:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\";s:5:\\\"utf-8\\\";s:58:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\u0000addresses\\\";a:1:{i:0;O:30:\\\"Symfony\\\\Component\\\\Mime\\\\Address\\\":2:{s:39:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000address\\\";s:21:\\\"<EMAIL>\\\";s:36:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000name\\\";s:0:\\\"\\\";}}}}s:7:\\\"subject\\\";a:1:{i:0;O:48:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\\":5:{s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\";s:7:\\\"Subject\\\";s:56:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\";i:76;s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\";N;s:53:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\";s:5:\\\"utf-8\\\";s:55:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\u0000value\\\";s:56:\\\"[Recland] [C\\u1eacP NH\\u1eacT H\\u1ed2 S\\u01a0 C\\u00c1 NH\\u00c2N TH\\u00c0NH C\\u00d4NG]\\\";}}s:17:\\\"x-original-emails\\\";a:1:{i:0;O:48:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\\":5:{s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\";s:17:\\\"X-Original-Emails\\\";s:56:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\";i:76;s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\";N;s:53:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\";s:5:\\\"utf-8\\\";s:55:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\u0000value\\\";s:2:\\\"[]\\\";}}}s:49:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\Headers\\u0000lineLength\\\";i:76;}i:1;N;}}}\"}}", "2025-08-04 14:30:22", "2025-08-04 14:30:22"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Providers\\AppServiceProvider.php", "line": 75}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SyncQueue.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SyncQueue.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SyncQueue.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SyncQueue.php", "line": 41}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php", "line": 253}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php", "line": 229}], "start": **********.182482, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:75", "source": {"index": 21, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Providers\\AppServiceProvider.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FProviders%2FAppServiceProvider.php&line=75", "ajax": false, "filename": "AppServiceProvider.php", "line": "75"}, "connection": "hri_recland_product", "explain": null, "start_percent": 17.841, "width_percent": 0.461}, {"sql": "select * from `email_logs` where `hash` = '948b4d111e340bd35280c60c19d84f38' and `created_at` >= '2025-08-04 14:25:22'", "type": "query", "params": [], "bindings": ["948b4d111e340bd35280c60c19d84f38", "2025-08-04 14:25:22"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Jobs/UpdateEmailLogStatus.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Jobs\\UpdateEmailLogStatus.php", "line": 59}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 37}], "start": **********.187018, "duration": 0.26212, "duration_str": "262ms", "memory": 0, "memory_str": null, "filename": "UpdateEmailLogStatus.php:59", "source": {"index": 14, "namespace": null, "name": "app/Jobs/UpdateEmailLogStatus.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Jobs\\UpdateEmailLogStatus.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FJobs%2FUpdateEmailLogStatus.php&line=59", "ajax": false, "filename": "UpdateEmailLogStatus.php", "line": "59"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `email_logs` where `hash` = ? and `created_at` >= ?", "hash": "5aef1b700a226ce621d310171fcb268a5918d0b5b2fc32f4511dce5fd25f44e7"}, "start_percent": 18.302, "width_percent": 78.528}, {"sql": "insert into `job_logs` (`job_id`, `name`, `queue`, `payload`, `updated_at`, `created_at`) values ('', 'App\\Notifications\\UpdateProfileEmployer', 'sync', '{\\\"uuid\\\":\\\"f5647d93-0b41-42fc-97b9-823a61e22242\\\",\\\"displayName\\\":\\\"App\\\\Notifications\\\\UpdateProfileEmployer\\\",\\\"job\\\":\\\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"failOnTimeout\\\":false,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\",\\\"command\\\":\\\"O:48:\\\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\\\":3:{s:11:\\\\\"notifiables\\\\\";O:45:\\\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\\\":5:{s:5:\\\\\"class\\\\\";s:15:\\\\\"App\\\\Models\\\\User\\\\\";s:2:\\\\\"id\\\\\";a:1:{i:0;i:512;}s:9:\\\\\"relations\\\\\";a:0:{}s:10:\\\\\"connection\\\\\";s:5:\\\\\"mysql\\\\\";s:15:\\\\\"collectionClass\\\\\";N;}s:12:\\\\\"notification\\\\\";O:39:\\\\\"App\\\\Notifications\\\\UpdateProfileEmployer\\\\\":2:{s:7:\\\\\"\\u0000*\\u0000user\\\\\";O:45:\\\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\\\":5:{s:5:\\\\\"class\\\\\";s:15:\\\\\"App\\\\Models\\\\User\\\\\";s:2:\\\\\"id\\\\\";i:512;s:9:\\\\\"relations\\\\\";a:0:{}s:10:\\\\\"connection\\\\\";s:5:\\\\\"mysql\\\\\";s:15:\\\\\"collectionClass\\\\\";N;}s:2:\\\\\"id\\\\\";s:36:\\\\\"ab09a0fa-017b-44cb-b273-dc7211b47c61\\\\\";}s:8:\\\\\"channels\\\\\";a:1:{i:0;s:8:\\\\\"database\\\\\";}}\\\"}}', '2025-08-04 14:30:22', '2025-08-04 14:30:22')", "type": "query", "params": [], "bindings": ["", "App\\Notifications\\UpdateProfileEmployer", "sync", "{\"uuid\":\"f5647d93-0b41-42fc-97b9-823a61e22242\",\"displayName\":\"App\\\\Notifications\\\\UpdateProfileEmployer\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:512;}s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:39:\\\"App\\\\Notifications\\\\UpdateProfileEmployer\\\":2:{s:7:\\\"\\u0000*\\u0000user\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";i:512;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"ab09a0fa-017b-44cb-b273-dc7211b47c61\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:8:\\\"database\\\";}}\"}}", "2025-08-04 14:30:22", "2025-08-04 14:30:22"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Providers\\AppServiceProvider.php", "line": 75}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SyncQueue.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SyncQueue.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SyncQueue.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SyncQueue.php", "line": 41}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php", "line": 253}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php", "line": 229}], "start": **********.474134, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:75", "source": {"index": 21, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Providers\\AppServiceProvider.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FProviders%2FAppServiceProvider.php&line=75", "ajax": false, "filename": "AppServiceProvider.php", "line": "75"}, "connection": "hri_recland_product", "explain": null, "start_percent": 96.83, "width_percent": 0.207}, {"sql": "select * from `users` where `users`.`id` = 512 limit 1", "type": "query", "params": [], "bindings": [512], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesModels.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesModels.php", "line": 88}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php", "line": 60}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php", "line": 98}], "start": **********.4759572, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "SerializesAndRestoresModelIdentifiers.php:108", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FSerializesAndRestoresModelIdentifiers.php&line=108", "ajax": false, "filename": "SerializesAndRestoresModelIdentifiers.php", "line": "108"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `users`.`id` = ? limit 1", "hash": "62b695c7a5937b15a7884c504ebc83827f2a9393d8daceb5a22dceb704d4dceb"}, "start_percent": 97.037, "width_percent": 0.138}, {"sql": "select * from `users` where `users`.`id` in (512)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 60}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesModels.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesModels.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php", "line": 60}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php", "line": 98}], "start": **********.477648, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "SerializesAndRestoresModelIdentifiers.php:80", "source": {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FSerializesAndRestoresModelIdentifiers.php&line=80", "ajax": false, "filename": "SerializesAndRestoresModelIdentifiers.php", "line": "80"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `users`.`id` in (512)", "hash": "1216ff3bf8b8e0ab1053a91cfaebee74f0f91ec9ac571cfdf9656de6355aa46f"}, "start_percent": 97.175, "width_percent": 0.102}, {"sql": "select `value`, `key` from `settings` where `key` like '%settings.vi.notification%'", "type": "query", "params": [], "bindings": ["%settings.vi.notification%"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Repositories/SettingRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SettingRepository.php", "line": 23}, {"index": 14, "namespace": null, "name": "app/Services/Frontend/SettingService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\SettingService.php", "line": 49}, {"index": 15, "namespace": null, "name": "app/Notifications/UpdateProfileEmployer.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Notifications\\UpdateProfileEmployer.php", "line": 74}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/Channels/DatabaseChannel.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Channels\\DatabaseChannel.php", "line": 60}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/Channels/DatabaseChannel.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Channels\\DatabaseChannel.php", "line": 38}], "start": **********.4956431, "duration": 0.005030000000000001, "duration_str": "5.03ms", "memory": 0, "memory_str": null, "filename": "SettingRepository.php:23", "source": {"index": 13, "namespace": null, "name": "app/Repositories/SettingRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SettingRepository.php", "line": 23}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSettingRepository.php&line=23", "ajax": false, "filename": "SettingRepository.php", "line": "23"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select `value`, `key` from `settings` where `key` like ?", "hash": "03db4599941fbfcc5d5ce90af08233c929fd5fe673d9c15cc07ef3d9b7c55995"}, "start_percent": 97.277, "width_percent": 1.507}, {"sql": "select `value`, `key` from `settings` where `key` like '%settings.en.notification%'", "type": "query", "params": [], "bindings": ["%settings.en.notification%"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Repositories/SettingRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SettingRepository.php", "line": 23}, {"index": 14, "namespace": null, "name": "app/Services/Frontend/SettingService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\SettingService.php", "line": 49}, {"index": 15, "namespace": null, "name": "app/Notifications/UpdateProfileEmployer.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Notifications\\UpdateProfileEmployer.php", "line": 75}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/Channels/DatabaseChannel.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Channels\\DatabaseChannel.php", "line": 60}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/Channels/DatabaseChannel.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Channels\\DatabaseChannel.php", "line": 38}], "start": **********.5022511, "duration": 0.00344, "duration_str": "3.44ms", "memory": 0, "memory_str": null, "filename": "SettingRepository.php:23", "source": {"index": 13, "namespace": null, "name": "app/Repositories/SettingRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SettingRepository.php", "line": 23}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSettingRepository.php&line=23", "ajax": false, "filename": "SettingRepository.php", "line": "23"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select `value`, `key` from `settings` where `key` like ?", "hash": "0013672069abff7e5ead156adf6ed0e3cdad8f11cd08bce35f44b435e246d43a"}, "start_percent": 98.784, "width_percent": 1.031}, {"sql": "insert into `notifications` (`id`, `type`, `data`, `read_at`, `notifiable_id`, `notifiable_type`, `updated_at`, `created_at`) values ('ab09a0fa-017b-44cb-b273-dc7211b47c61', 'App\\Notifications\\UpdateProfileEmployer', '{\\\"content_vi\\\":\\\"C\\u1ed9ng t\\u00e1c vi\\u00ean \\u0111\\u00e3 c\\u1eadp nh\\u1eadt h\\u1ed3 s\\u01a1 th\\u00e0nh c\\u00f4ng.\\\",\\\"content_en\\\":\\\"The collaborator has successfully updated the profile.\\\"}', '', 512, 'App\\Models\\User', '2025-08-04 14:30:22', '2025-08-04 14:30:22')", "type": "query", "params": [], "bindings": ["ab09a0fa-017b-44cb-b273-dc7211b47c61", "App\\Notifications\\UpdateProfileEmployer", "{\"content_vi\":\"C\\u1ed9ng t\\u00e1c vi\\u00ean \\u0111\\u00e3 c\\u1eadp nh\\u1eadt h\\u1ed3 s\\u01a1 th\\u00e0nh c\\u00f4ng.\",\"content_en\":\"The collaborator has successfully updated the profile.\"}", null, 512, "App\\Models\\User", "2025-08-04 14:30:22", "2025-08-04 14:30:22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/Channels/DatabaseChannel.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Channels\\DatabaseChannel.php", "line": 20}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/NotificationSender.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php", "line": 148}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/NotificationSender.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php", "line": 106}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/NotificationSender.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php", "line": 109}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/ChannelManager.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\ChannelManager.php", "line": 54}], "start": **********.508013, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "DatabaseChannel.php:20", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/Channels/DatabaseChannel.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Channels\\DatabaseChannel.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FNotifications%2FChannels%2FDatabaseChannel.php&line=20", "ajax": false, "filename": "DatabaseChannel.php", "line": "20"}, "connection": "hri_recland_product", "explain": null, "start_percent": 99.814, "width_percent": 0.186}]}, "models": {"data": {"App\\Models\\User": {"retrieved": 8, "updated": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\JobLog": {"created": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FJobLog.php&line=1", "ajax": false, "filename": "JobLog.php", "line": "?"}}, "Shetabit\\Visitor\\Models\\Visit": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fshetabit%2Fvisitor%2Fsrc%2FModels%2FVisit.php&line=1", "ajax": false, "filename": "Visit.php", "line": "?"}}, "OwenIt\\Auditing\\Models\\Audit": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fowen-it%2Flaravel-auditing%2Fsrc%2FModels%2FAudit.php&line=1", "ajax": false, "filename": "Audit.php", "line": "?"}}, "App\\Models\\EmailLog": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FEmailLog.php&line=1", "ajax": false, "filename": "EmailLog.php", "line": "?"}}, "Illuminate\\Notifications\\DatabaseNotification": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FNotifications%2FDatabaseNotification.php&line=1", "ajax": false, "filename": "DatabaseNotification.php", "line": "?"}}}, "count": 16, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"created": 7, "retrieved": 8, "updated": 1}}, "symfonymailer_mails": {"count": 1, "mails": [{"to": ["<EMAIL>"], "subject": "[Recland] [CẬP NHẬT HỒ SƠ CÁ NHÂN THÀNH CÔNG]", "headers": "From: RECLAND <<EMAIL>>\r\nTo: <EMAIL>\r\nSubject: [Recland] =?utf-8?Q?=5BC=E1=BA=ACP_NH=E1=BA=ACT_H=E1=BB=92?=\r\n =?utf-8?Q?_S=C6=A0_C=C3=81_NH=C3=82N_TH=C3=80NH_C=C3=94NG=5D?=\r\nX-Original-Emails: []\r\n", "body": null, "html": null}]}, "gate": {"count": 0, "messages": []}, "session": {"_token": "67FrIiAKRzqdDDYE9lpf1v63S50ahQba2svcMgmp", "_previous": "array:1 [\n  \"url\" => \"http://recland.local/rec/profile\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => array:1 [\n    0 => \"toastr::messages\"\n  ]\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "login_client_59ba36addc2b2f9401580f014c7f58ea4e30989d": "512", "PHPDEBUGBAR_STACK_DATA": "[]", "toastr::messages": "array:1 [\n  0 => array:4 [\n    \"type\" => \"success\"\n    \"title\" => \"Success\"\n    \"message\" => \"<PERSON><PERSON> lưu thành công bản ghi\"\n    \"options\" => []\n  ]\n]"}, "request": {"data": {"status": "302 Found", "full_url": "http://recland.local/rec/update-profile", "action_name": "rec-update-profile", "controller_action": "App\\Http\\Controllers\\Frontend\\RecController@profilePost", "uri": "POST rec/update-profile", "controller": "App\\Http\\Controllers\\Frontend\\RecController@profilePost<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FRecController.php&line=169\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Frontend", "prefix": "/rec", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FRecController.php&line=169\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/RecController.php:169-182</a>", "middleware": "web, localization, visit-website, check-rec", "duration": "5.44s", "peak_memory": "52MB", "response": "Redirect to http://recland.local/rec/profile", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-566751937 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-566751937\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-975897511 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">67FrIiAKRzqdDDYE9lpf1v63S50ahQba2svcMgmp</span>\"\n  \"<span class=sf-dump-key>avatar</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cccd_front_image</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cccd_back_image</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">HAU Nguyen Thi</span>\"\n  \"<span class=sf-dump-key>birthday</span>\" => \"<span class=sf-dump-str title=\"10 characters\">04/02/2000</span>\"\n  \"<span class=sf-dump-key>mobile</span>\" => \"<span class=sf-dump-str title=\"12 characters\">097845634555</span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"24 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>address</span>\" => \"<span class=sf-dump-str title=\"22 characters\">60 D&#432;&#417;ng Khu&#234; M&#7929; &#272;&#236;nh2</span>\"\n  \"<span class=sf-dump-key>cccd_number</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-975897511\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1528178224 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1127</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundary1fIoQC6TQk59Uc2h</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">http://recland.local/rec/profile</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,vi;q=0.8,nl;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1257 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImxyWFptaVBGK1BuR2MvQWh4MlZpUWc9PSIsInZhbHVlIjoiSGRqMWdEd3lNSS9ZenRwdlNZQ3hjTmV2ZktDRjczQ0FPaWR3NVFHek5vV3o3Tm1nYU1OQzk1dUIxY0ZSRmwyT1ByRC82d0lXRzBWTWRFUTltUGFNQ29ZNmhuU2RKbncwOWU4bXZxaFNPemtoRE1pNVp6b2hsVWh0RjlwSFYzQnEiLCJtYWMiOiI1OGE0MjY5MjNjOGFiOTdkMWQ3NWNlYmRhNTZmMzYyZmNiMWY4MWRjNjA5NTJiNDkxODU3OWEyZWYwODMxZWJhIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6Ik0zS2V3Q25ZT253VG92bGRzYnRBcVE9PSIsInZhbHVlIjoicy9UZ2NLZFNjYmJQOTd2UG4zWGtlcFU3NHdwWldvbFd0VXVVcVVMNDJLWlZjem1PYXlPVFFrb2hMWTVOYVVBcUh3d0xzc0c3SWI1L09uamR6N1dIMjRMWmhpUE5ESkphY0FOditERGlJVXBFcUlXWFgvbXpaYURGbUJoem1xL0YiLCJtYWMiOiJiNTcyZmE5M2ZlMjlkYTA0NjY2OTcxNWI3MjllMDc1N2M0ZGE2YjQyOTVkZTcxYjU1Njg1NmQ5N2E3ODA0YzBlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1528178224\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-54155389 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|cm8f4SgFu8wrAd7wPR9b0gsQwQ8YYGlGSWS8Y9hgKQXIsj7qT8VG3KwC3dMx|$2y$10$iTSniY9ReMSnjm6GaX0bRuGWPNsyk9pQat6r8dKHdyoQSgwMkVojS</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">67FrIiAKRzqdDDYE9lpf1v63S50ahQba2svcMgmp</span>\"\n  \"<span class=sf-dump-key>recland_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1VyD9s5I8kaKasZMxJDemZkFPxfq8PRabbTbE7MT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-54155389\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-172233217 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 04 Aug 2025 07:30:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">http://recland.local/rec/profile</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-172233217\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1727057804 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">67FrIiAKRzqdDDYE9lpf1v63S50ahQba2svcMgmp</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">http://recland.local/rec/profile</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">toastr::messages</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>login_client_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>512</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>toastr::messages</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n      \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Success</span>\"\n      \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"25 characters\">&#272;&#227; l&#432;u th&#224;nh c&#244;ng b&#7843;n ghi</span>\"\n      \"<span class=sf-dump-key>options</span>\" => []\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1727057804\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "http://recland.local/rec/update-profile", "action_name": "rec-update-profile", "controller_action": "App\\Http\\Controllers\\Frontend\\RecController@profilePost"}, "badge": "302 Found"}}