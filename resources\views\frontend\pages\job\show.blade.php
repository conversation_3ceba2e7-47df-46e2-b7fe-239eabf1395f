@extends('frontend.layouts.v2')
@push('style')
<style>
    .list-style-type-disc {

        ul,
        li {
            list-style-type: disc !important;
        }
    }

    /** {margin:0; padding:0; text-indent:0; }*/
    /*.s1 { color: black; font-family:"Times New Roman", serif; font-style: normal; font-weight: normal; text-decoration: none; font-size: 11pt; }*/
    /*h1 { color: black; font-family:"Times New Roman", serif; font-style: normal; font-weight: bold; text-decoration: none; font-size: 18pt; }*/
    /*.s2 { color: black; font-family:"Times New Roman", serif; font-style: normal; font-weight: bold; text-decoration: none; font-size: 12pt; }*/
    /*.s3 { color: black; font-family:"Times New Roman", serif; font-style: normal; font-weight: normal; text-decoration: none; font-size: 12pt; }*/
    /*h2 { color: black; font-family:"Times New Roman", serif; font-style: normal; font-weight: bold; text-decoration: none; font-size: 12pt; }*/
    /*p { color: black; font-family:"Times New Roman", serif; font-style: normal; font-weight: normal; text-decoration: none; font-size: 12pt; margin:0pt; }*/
    /*li {display: block; }*/
    /*#l1 {padding-left: 0pt; }*/
    /*#l1> li>*:first-child:before {content: "- "; color: black; font-family:"Times New Roman", serif; font-style: normal; font-weight: normal; text-decoration: none; font-size: 12pt; }*/
    /*#l2 {padding-left: 0pt; }*/
    /*#l2> li>*:first-child:before {content: "- "; color: black; font-family:"Times New Roman", serif; font-style: normal; font-weight: normal; text-decoration: none; font-size: 12pt; }*/
    /*table, tbody {vertical-align: top; overflow: visible; }*/
</style>
{{-- @push('scripts_head') --}}
{{-- <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.9.2/html2pdf.bundle.js"></script> --}}
{{-- @endpush --}}
@endpush
@section('content')
<section class="job-detail-page container clearfix mt30">
    <div class="banner-job">
        @if (!$job->can_submit)
        <!-- show overlay blur on banner -->
        <div class="overlay-banner">
            <h5>Dừng tuyển</h5>
        </div>
        @endif
        <img class="img-banner" style=" max-height: 400px"
            src="{{ gen_url_file_s3($company->path_banner, config('settings.global.banner_default_company')) }}" alt="">
        <span class="logo"><img src="{{ $company->path_logo }}" alt=""></span>
    </div>
    <div class="main-container mb40">
        <div class="job-cty-item">
            <div class="job-cty-item__left">
                <div class="job-cty">
                    <div class="info">
                        <div class="name"> {{ $job->name }}</div>
                        <div class="flexbox align-end space-between">
                            <div>
                                <div class="company">{{ optional($job->company)->name }}</div>
                                <div class="job-requirements mt-3 mb0">
                                    @if ($job->urgent)
                                    <span class="red">
                                        <svg class="icon-svg me-1">
                                            <use
                                                xlink:href="{{ asset('frontend/assets_v2/images/icons/icon.svg#bolt') }}">
                                            </use>
                                        </svg>
                                        Urgent
                                    </span>
                                    @endif
                                    <span class="green">{{ optional($job->jobMeta)->priority }}</span>
                                    @if ($job->remote)
                                    <span class="btn-banner-st4">Remote</span>
                                    @endif

                                    {{-- <span class="green">New</span> --}}
                                    @if ($job->bonus_type == config('constant.bonus_type.onboard'))
                                    <span class="orange">
                                        <svg class="icon-svg me-1">
                                            <use
                                                xlink:href="{{ asset('frontend/assets_v2/images/icons/icon.svg#paid') }}">
                                            </use>
                                        </svg>
                                        Bonus Onboard
                                    </span>
                                    @elseif($job->bonus_type == config('constant.bonus_type.interview'))
                                    <span class="orange">
                                        <svg class="icon-svg me-1">
                                            <use
                                                xlink:href="{{ asset('frontend/assets_v2/images/icons/icon.svg#paid') }}">
                                            </use>
                                        </svg>
                                        Bonus Interview
                                    </span>
                                    @else
                                    <span class="orange">
                                        <svg class="icon-svg me-1">
                                            <use
                                                xlink:href="{{ asset('frontend/assets_v2/images/icons/icon.svg#paid') }}">
                                            </use>
                                        </svg>
                                        Bonus CV
                                    </span>
                                    @endif

                                </div>
                            </div>
                            <Social :detail="{{ $job }}" :url-login="'{{ route('rec-login') }}'" @if (auth('client')->
                                check()) :is_like="{{ \App\Helpers\Common::checkUserJobCare(auth('client')->id(),
                                $job->id, 'like') ? 'true' : 'false' }}"
                                :is_save="{{ \App\Helpers\Common::checkUserJobCare(auth('client')->id(), $job->id,
                                'save') ? 'true' : 'false' }}"
                                :is-login="true"
                                :accept-token="'{{ \Illuminate\Support\Facades\Cache::get('accept_token_api_' .
                                auth('client')->id()) }}'" @endif>

                            </Social>
                        </div>
                    </div>
                </div>
            </div>
            @if (!$is_employer && $job->can_submit)
            <div class="job-cty-item__right d-none d-lg-block">
                <div class="btn-job center">
                    <div class="gioi-thieu mb-3">
                        <a class="btn-default full item-header-job-st1" data-toggle="open-popup" data-type="0">
                            {!! config('settings.' . app()->getLocale() . '.home_job.gioithieuungvien') !!}
                        </a>
                        <div class="mt-2">
                            {!! config('settings.' . app()->getLocale() . '.home_job.hoahong') !!} {{
                            \App\Helpers\Common::formatNumber($job->bonus_for_ctv) }}
                            {{ $job->bonus_currency }}
                        </div>
                    </div>
                    @if ($job->bonus_self_apply > 0 && false)
                    <div class="ung-tuyen  mb-3">
                        <a class="btn-default full none item-header-job-st2" data-toggle="open-popup" data-type="1"> {!!
                            config('settings.' . app()->getLocale() . '.home_job.tuungtuyen') !!}</a>
                        <div class="mt-2">
                            {!! config('settings.' . app()->getLocale() . '.home_job.hoahong') !!}
                            {{ \App\Helpers\Common::formatNumber($job->bonus_self_apply) }}
                            {{ $job->bonus_self_apply_currency }}
                        </div>
                    </div>
                    @endif

                </div>
            </div>
            @endif

        </div>
        <div class="flexbox wrap-mobile">
            <div class="col-left order2 ps-3 pe-0 mb30">
                <div class="job-info-warp mb-3">
                    <h2 class="title-line"> {{ __('frontend/job/message.overview') }}
                    </h2>
                    <div class="padding-detail">
                        <div class="list-tong-quan">
                            @if (!$is_employer)
                            <div class="item">
                                <span class="icon">
                                    <svg class="icon-svg">
                                        <use
                                            xlink:href="{{ asset('frontend/assets_v2/images/icons/icon.svg#local_atm') }}">
                                        </use>
                                    </svg>
                                </span>
                                <div class="mb-1">{{ __('frontend/job/message.rose_gioithieu') }}</div>
                                <div class="fw-bold color-blue">
                                    {{ \App\Helpers\Common::formatNumber($job->bonus_for_ctv) }}
                                    <sup>{{ $job->bonus_currency }}</sup>
                                </div>
                            </div>
                            @endif

                            {{--
                            <div class="item">
                                <span class="icon">
                                    <svg class="icon-svg">
                                        <use
                                            xlink:href="{{ asset('frontend/assets_v2/images/icons/icon.svg#local_atm') }}">
                                        </use>
                                    </svg>
                                </span>
                                <div class="mb-1">{{ __('frontend/job/message.rose_tuungtuyen') }}</div>
                                <div class="fw-bold color-blue">
                                    {{ \App\Helpers\Common::formatNumber($job->bonus_self_apply) }}
                                    <sup>{{ $job->bonus_self_apply_currency }} </sup>
                                </div>
                            </div> --}}
                            <div class="item">
                                <span class="icon">
                                    <svg class="icon-svg">
                                        <use
                                            xlink:href="{{ asset('frontend/assets_v2/images/icons/icon.svg#local_atm') }}">
                                        </use>
                                    </svg>
                                </span>
                                <div class="mb-1"> {!! ucfirst(config('settings.' . app()->getLocale() .
                                    '.home_job.mucluong')) !!}</div>
                                <div class="fw-bold color-blue">
                                    {{ \App\Helpers\Common::formatNumber($job->salary_min) }}
                                    - {{ \App\Helpers\Common::formatNumber($job->salary_max) }}
                                    <sup>{{ $job->salary_currency }}</sup>
                                </div>
                            </div>
                            <div class="item">
                                <span class="icon">
                                    <svg class="icon-svg">
                                        <use
                                            xlink:href="{{ asset('frontend/assets_v2/images/icons/icon.svg#work_outline') }}">
                                        </use>
                                    </svg>
                                </span>
                                <div class="mb-1">{{ __('frontend/job/message.fields') }}</div>
                                <div class="fw-bold color-blue">
                                    {{ implode(' | ', $job->career_value) }}
                                </div>
                            </div>
                            <div class="item">
                                <span class="icon">
                                    <svg class="icon-svg">
                                        <use
                                            xlink:href="{{ asset('frontend/assets_v2/images/icons/icon.svg#business') }}">
                                        </use>
                                    </svg>
                                </span>
                                <div class="mb-1"> {{ __('frontend/job/message.form') }}</div>
                                <div class="fw-bold color-blue">
                                    {{ \Illuminate\Support\Str::ucfirst($job->type_value) }}
                                </div>
                            </div>
                            <div class="item">
                                <span class="icon">
                                    <svg class="icon-svg">
                                        <use
                                            xlink:href="{{ asset('frontend/assets_v2/images/icons/icon.svg#location') }}">
                                        </use>
                                    </svg>
                                </span>
                                <div class="mb-1">
                                    {{ __('frontend/job/message.address') }}
                                </div>
                                @php
                                $count = 0;
                                @endphp
                                @foreach ($job->group_address as $address)
                                @php
                                $count++;
                                $address['address'] = array_filter($address['address']);

                                if (!is_array($address['address']) || empty($address['address'])) {
                                continue;
                                }
                                @endphp
                                <div class="fw-bold color-blue">{{ $address['city'] }}
                                    : {!! implode('|', $address['address']) !!}
                                </div>
                                @endforeach

                            </div>
                            <div class="item">
                                <span class="icon">
                                    <svg class="icon-svg">
                                        <use
                                            xlink:href="{{ asset('frontend/assets_v2/images/icons/icon.svg#bar_chart') }}">
                                        </use>
                                    </svg>
                                </span>
                                <div class="mb-1"> {{ __('frontend/job/message.rank') }}</div>
                                <div class="fw-bold color-blue">
                                    {{ $job->rank_name }}
                                </div>
                            </div>
                            <div class="item">
                                <span class="icon">
                                    <svg class="icon-svg">
                                        <use xlink:href="{{ asset('frontend/assets_v2/images/icons/icon.svg#user') }}">
                                        </use>
                                    </svg>
                                </span>
                                <div class="mb-1">{{ __('frontend/job/message.number_of_recruits') }}</div>
                                <div class="fw-bold color-blue"> {{ $job->vacancies }}</div>
                            </div>
                            <div class="item">
                                <span class="icon">
                                    <svg class="icon-svg">
                                        <use
                                            xlink:href="{{ asset('frontend/assets_v2/images/icons/icon.svg#access_time') }}">
                                        </use>
                                    </svg>
                                </span>
                                <div class="mb-1">{{ __('frontend/job/message.update_at') }}</div>
                                <div class="fw-bold color-blue">
                                    @if (isset($job->updated_at))
                                    {{ $job->updated_at->format('d/m/Y') }}
                                    @endif
                                </div>
                            </div>
                            <div class="item">
                                <span class="icon">
                                    <svg class="icon-svg">
                                        <use
                                            xlink:href="{{ asset('frontend/assets_v2/images/icons/icon.svg#access_time') }}">
                                        </use>
                                    </svg>
                                </span>
                                <div class="mb-1"> {{ __('frontend/job/message.deadline_for_submission') }}</div>

                                <div
                                    class="fw-bold {{ time() > strtotime($job->expire_at . ' 23:59:59') ? 'color-red' : 'color-blue' }}">
                                    @if (isset($job->expire_at))
                                    {{ $job->expire_at_value }}
                                    @endif
                                </div>
                            </div>


                        </div>
                    </div>
                </div>
                <div class="job-info-warp mb-3">
                    <h2 class="title-line">{{ __('frontend/job/message.skill') }}</h2>
                    <div class="padding-detail">
                        <div class="tab-default">
                            @if ($job->skills)
                            @foreach (explode(',', $job->skills) as $skill)
                            <a href="#">{{ $skill }}</a>
                            @endforeach
                            @endif
                        </div>
                    </div>
                </div>

                <job-comments :job-id="{{ $job->id }}" :is-login="{{ auth('client')->check() ? 'true' : 'false' }}"
                    url-login="{{ route('rec-login') }}" @if (auth('client')->check()) :accept-token="'{{
                    \Illuminate\Support\Facades\Cache::get('accept_token_api_' . auth('client')->id()) }}'" @endif>
                </job-comments>
            </div>
            <div class="col-right order1 mb30">
                <div class="job-info-warp">
                    @if (isset($job->jd_description))
                    <div class="padding-line list-style-type-disc" style="display: table; width:100%">
                        <h2 class="fw-bold txt_24 mb-3 color_222"> {!! config('settings.' . app()->getLocale() .
                            '.home_job.motacongviec') !!}</h2>
                        <p>
                            {!! $job->jd_description !!}
                        </p>
                    </div>
                    @endif
                    @if (isset($job->jd_request))
                    <div class="padding-line list-style-type-disc" style="display: table; width:100%">
                        <h2 class="fw-bold txt_24 mb-3 color_222">{!! config('settings.' . app()->getLocale() .
                            '.home_job.yeucau') !!}</h2>
                        <p>
                            {!! $job->jd_request !!}
                        </p>
                    </div>
                    @endif
                    @if (isset($job->jd_welfare))
                    <div class="padding-line list-style-type-disc" style="display: table; width:100%">
                        <h2 class="fw-bold txt_24 mb-3 color_222"> {!! config('settings.' . app()->getLocale() .
                            '.home_job.phucloi') !!}</h2>
                        <p>
                            {!! $job->jd_welfare !!}
                        </p>
                    </div>
                    @endif
                    <div class="padding-line">
                        <h2 class="fw-bold txt_24 mb-3 color_222">{{ __('frontend/job/message.back_to_comity') }}</h2>
                        <div class="info-company">
                            <div class="job-cty">
                                <span class="logo"><img src="{{ $company->path_logo }} " alt=""></span>
                                <div class="info">
                                    <div class="name"> {{ $company->name }}</div>
                                    <ul>
                                        <li>
                                            <svg class="icon-svg">
                                                <use
                                                    xlink:href="{{ asset('frontend/assets_v2/images/icons/icon.svg#web') }}">
                                                </use>
                                            </svg>
                                            <a class="color-orange" target="_blank" href="{{ $company->website }}">{{
                                                $company->website }}</a>
                                        </li>
                                        <li>
                                            <svg class="icon-svg">
                                                <use
                                                    xlink:href="{{ asset('frontend/assets_v2/images/icons/icon.svg#group_add') }}">
                                                </use>
                                            </svg> {!! config('settings.' . app()->getLocale() . '.home_job.quimo') !!}
                                            {{ $company->scale_value }}
                                        </li>
                                        @if (isset($company->address_value))
                                        @foreach ($company->address_value as $item)
                                        @if (isset($cities[$item->area]))
                                        @if (empty($item->address))
                                        @continue
                                        @endif
                                        <li>
                                            <svg class="icon-svg">
                                                <use
                                                    xlink:href="{{ asset('frontend/assets_v2/images/icons/icon.svg#location') }}">
                                                </use>
                                            </svg> {{ $cities[$item->area] }}: {{ $item->address }}
                                        </li>
                                        @endif
                                        @endforeach
                                        @endif
                                    </ul>
                                </div>
                            </div>
                            <div class="des mb-3">
                                {!! strip_tags($company->about) !!}
                            </div>
                            <a class="btn-default min none" target="_blank"
                                href="{{ route('detail-company', $company->slug) }}">
                                {!! config('settings.' . app()->getLocale() . '.home_job.xemthem') !!}
                                <svg class="icon-svg ms-2">
                                    <use xlink:href="{{ asset('frontend/assets_v2/images/icons/icon.svg#right') }}">
                                    </use>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @if (!$is_employer)
        <div class="pin-btn-job d-block d-lg-none">
            <div class="flexbox">

                <div class="gioi-thieu">
                    <a class="btn-default full item-header-job-st1" data-toggle="open-popup" data-type="0">
                        {!! config('settings.' . app()->getLocale() . '.home_job.gioithieuungvien') !!}</a>
                    <div class="mt-2">
                        {!! config('settings.' . app()->getLocale() . '.home_job.hoahong') !!} {{
                        \App\Helpers\Common::formatNumber($job->bonus_for_ctv) }}
                        {{ $job->bonus_currency }}
                    </div>
                </div>
                @if ($job->bonus_self_apply > 0 && false)
                <div class="ung-tuyen">
                    <a class="btn-default full none item-header-job-st2" data-toggle="open-popup" data-type="1">{!!
                        config('settings.' . app()->getLocale() . '.home_job.tuungtuyen') !!}</a>
                    <div class="mt-2">
                        {!! config('settings.' . app()->getLocale() . '.home_job.hoahong') !!}
                        {{ \App\Helpers\Common::formatNumber($job->bonus_self_apply) }}
                        {{ $job->bonus_self_apply_currency }}
                    </div>
                </div>
                @endif
            </div>
        </div>
        @endif
    </div>
    <!-- Việc làm tại Recland -->

    @if (!empty($relateJobs) && !$is_employer)
    <div class="list-job mb60">
        <div class="head-title mb10">
            <h2 class="title-cate"> {!! config('settings.' . app()->getLocale() . '.home_job.vieclamlienqanvoiban') !!}
            </h2>
            <a class="view-all" href="{{ route('job-index') }}">{!! config('settings.' . app()->getLocale() .
                '.home_job.xem_all') !!}</a>
        </div>
        <div class="grid grid__4">
            <?php
                    $count = 0;
                    $countNext = 0; ?>
            @foreach ($relateJobs as $relateJob)
            <div class="item">
                <div class="warp">
                    <div class="job-cty">
                        <span class="logo">
                            <img src="{{ optional($relateJob->company)->path_logo }}" alt="">
                        </span>
                        <div class="info">
                            <div class="name">{{ optional($relateJob->company)->name }}</div>
                            <div class="address">
                                <svg class="icon-svg me-1">
                                    <use xlink:href="{{ asset('frontend/assets_v2/images/icons/icon.svg#location') }}">
                                    </use>
                                </svg>
                                <div class="fw-bold color-blue">
                                    @php
                                    $addressNew = '';
                                    foreach ($relateJob->group_address as $key => $address) {
                                    $addressNew .= $address['city'] . ', ';
                                    }
                                    @endphp
                                    {{ rtrim($addressNew, ', ') }}
                                </div>
                            </div>

                        </div>
                        {{-- <a class="bookmark"> --}}
                            {{-- <svg class="icon-svg me-1"> --}}
                                {{-- <use --}} {{--
                                    xlink:href="{{ asset('frontend/assets_v2/images/icons/icon.svg#bookmark') }}"></use>
                                --}}
                                {{-- </svg> --}}
                            {{-- </a> --}}
                    </div>
                    <a class="job-name" href="{{ route('job-detail', ['slug' => $relateJob->slug]) }}">
                        {{ $relateJob->name }}</a>
                    <div class="job-requirements">
                        @if ($relateJob->urgent)
                        <span class="red">
                            <svg class="icon-svg me-1">
                                <use xlink:href="{{ asset('frontend/assets_v2/images/icons/icon.svg#bolt') }}">
                                </use>
                            </svg>
                            Urgent
                        </span>
                        @endif

                        {{-- <span class="green">New</span> --}}
                        <span class="green">{{ optional($relateJob->jobMeta)->priority }}</span>
                        @if ($relateJob->remote)
                        <span class="btn-banner-st4">Remote</span>
                        @endif
                        @if ($relateJob->bonus_type == config('constant.bonus_type.onboard'))
                        <span class="orange">
                            <svg class="icon-svg me-1">
                                <use xlink:href="{{ asset('frontend/assets_v2/images/icons/icon.svg#paid') }}">
                                </use>
                            </svg>
                            Bonus Onboard
                        </span>
                        @elseif($relateJob->bonus_type == config('constant.bonus_type.interview'))
                        <span class="orange">
                            <svg class="icon-svg me-1">
                                <use xlink:href="{{ asset('frontend/assets_v2/images/icons/icon.svg#paid') }}">
                                </use>
                            </svg>
                            Bonus Interview
                        </span>
                        @else
                        <span class="orange">
                            <svg class="icon-svg me-1">
                                <use xlink:href="{{ asset('frontend/assets_v2/images/icons/icon.svg#paid') }}">
                                </use>
                            </svg>
                            Bonus CV
                        </span>
                        @endif
                    </div>
                </div>
                <div class="job-bonus">Bonus:
                    {{ \App\Helpers\Common::formatNumber($relateJob->bonus_for_ctv) }}
                    ({{ $relateJob->bonus_self_apply_currency }})
                </div>
            </div>
            @endforeach

        </div>
    </div>
    @endif
</section>
<input type="hidden" value="{{ $job->id }}" data-toggle="id-job-detail">
<input type="hidden" value="{{ $job->bonus_type }}" data-toggle="id-job-bonus_type">
<input type="hidden" value="{{ $job->bonus }}" data-toggle="id-job-bonus">
<input type="hidden" value="{{ $job->bonus }}" id="job_detail_bonus">
<input type="hidden" value="{{ $job->bonus_self_apply }}" id="job_detail_bonus_self_apply">
@include('frontend.layouts.modal.modal-introduction', ['hideJob' => true])

@endsection

@section('after_scripts')
<script src="{{ asset2('frontend/asset/js/additional-methods.min.js') }}"></script>
<script src="{{ asset2('frontend/asset/js/introduction.js') }}"></script>
<script>
    $(document).ready(function() {

            $('#job_bonus').val($("#job_detail_bonus").val());
            $('#job_bonus_self_apply').val($("#job_detail_bonus_self_apply").val());

            //popup js
            $('#modal-introduction').modal({
                backdrop: 'static',
                keyboard: false
            });
            $('.select2-modal').select2({
                dropdownParent: "#modal-introduction",
                ajax: {
                    url: urlCv,
                    type: "get",
                    dataType: 'json',
                    delay: 550,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    data: function(params) {
                        return {
                            searchTerm: params.term // search term
                        };
                    },
                    processResults: function(response) {
                        return {
                            results: response
                        };
                    },
                    cache: true
                }
            });

            $('#ware_house_cv_v2').change(function() {
                $('.ware_house_cv-error').hide();
            })
            $(document).on('click', '[data-toggle="open-popup"]', function() {
                @if (!auth('client')->check())
                    <?php
                    $params = [];
                    $params['redirect'] = base64_encode(url()->current());
                    $params = http_build_query($params);
                    ?>
                    window.location.href = '{{ route('rec-login', $params) }}';
                @elseif (auth('client')->user()->type == 'rec')
                    $('.action-group-checkbox').hide();
                    $('#mess-authority-onboard').hide();
                    $('#mess-authority-interview').hide();
                    $('[data-toggle="hide-job-id"]').val($('[data-toggle="id-job-detail"]').val());
                    let type = $(this).data('type');
                    if ($('[name="is_self_apply"]').val() != type) {
                        resetModal();
                    }
                    let bonus_type = $('[data-toggle="id-job-bonus_type"]').val();
                    let money_authority = (parseInt($('[data-toggle="id-job-bonus"]').val()) * parseInt($(
                        "#setting_authority").val())) / 100;
                    if (money_authority == 'NaN') {
                        money_authority = 0;
                    }
                    $("#money_authority").html(formatCurrency(money_authority));
                    if (bonus_type === 'onboard') {
                        $('.action-group-checkbox').show();
                        $('#mess-authority-onboard').show();
                    }
                    if (bonus_type === 'interview') {
                        $('.action-group-checkbox').show();
                        $('#mess-authority-interview').show();
                    }

                    $('[data-toggle="checkbox-tab"]').attr('checked', false);
                    $('[name="is_self_apply"]').val(type);
                    $("#modal-introduction-v2").modal('show');
                @elseif (auth('client')->user()->type == 'employer')
                    $.toast({
                        content: '{{ __('frontend/job/message.job_permission') }}',
                        type: 'error',
                        delay: 5000
                    });
                @endif
            });

            $(document).on('click', '[data-toggle="next"]', function() {
                if (v.form()) {
                    let form = $(this).parents('.form-step');
                    let index = form.data('step');
                    form.removeClass('active');
                    let nextStep = parseInt(index) + 1
                    $('.form-step[data-step="' + nextStep + '"]').addClass('active');

                    let x = nextStep;
                    $('.step-item').removeClass('active');
                    while ($('.step-item[data-step="' + x + '"]').length > 0) {
                        $('.step-item[data-step="' + x + '"]').addClass('active');
                        x--;
                    }

                    $(document).on('click', '.btn-save3', function() {
                        $("#themmoi_cv").validate().settings.ignore = "";
                        $('<input>').attr({
                            type: 'hidden',
                            name: 'status',
                            value: 'pending-review'
                        }).appendTo($('#themmoi_cv'));
                        if (v.form()) {
                            $('#themmoi_cv').submit();
                        }
                    })

                }
            })
            $('[data-toggle="back"]').click(function() {
                let form = $(this).parents('.form-step');
                let index = form.data('step');
                form.removeClass('active');
                let nextStep = parseInt(index) - 1
                $('.form-step[data-step="' + nextStep + '"]').addClass('active');
                $("#themmoi_cv").validate().settings.ignore = ":hidden";
                let x = nextStep;
                $('.step-item').removeClass('active');
                while ($('.step-item[data-step="' + x + '"]').length > 0) {
                    $('.step-item[data-step="' + x + '"]').addClass('active');
                    x--;
                }
            });

            $(document).on('click', '.btn-save', function() {
                $('<input>').attr({
                    type: 'hidden',
                    name: 'status',
                    value: 'pending-review'
                }).appendTo($('#chontukho'));
                if ($("#chontukho").valid()) {
                    $('#chontukho').submit();
                }
            });

            $(document).on('click', '.btn-draft', function() {
                $('<input>').attr({
                    type: 'hidden',
                    name: 'status',
                    value: 'draft'
                }).appendTo($('#chontukho'));
                if ($("#chontukho").valid()) {
                    $('#chontukho').submit();
                }
            });

            $("#chontukho").validate({
                rules: {
                    ware_house_cv: {
                        required: true,
                        check_warehouse_cv: true,
                        check_duplicate_submit_by_cv: true
                    },
                    expected_date: {
                        required: true,
                    },
                    candidate_salary_expect: {
                        // required: true,
                        number: true,
                    },
                },
                messages: {
                    ware_house_cv: {
                        required: '{{ __('frontend/validation.required') }}',
                    },
                    expected_date: {
                        required: '{{ __('frontend/validation.required') }}',
                    },
                    candidate_salary_expect: {
                        required: '{{ __('frontend/validation.required') }}',
                        number: '{{ __('frontend/validation.number') }}',
                    },
                },

                highlight: function(element, errorClass, validClass) {
                    var elem = $(element);
                    if (elem.hasClass("select2-hidden-accessible")) {
                        elem.next().parent().addClass(errorClass);
                    } else {
                        elem.addClass(errorClass);
                    }
                },
                unhighlight: function(element, errorClass, validClass) {
                    var elem = $(element);
                    if (elem.hasClass("select2-hidden-accessible")) {
                        console.log(errorClass);
                        $("#select2-" + elem.attr("id") + "-container").parent().removeClass(
                            errorClass);
                    } else {
                        elem.removeClass(errorClass);
                    }
                },
                errorPlacement: function(error, element) {
                    var elem = $(element);
                    if (elem.hasClass("select2-hidden-accessible")) {
                        element = elem.next();
                        error.insertAfter(element);
                    } else {
                        error.insertAfter(element);
                    }
                }
            });

            var v = $("#themmoi_cv").validate({
                rules: {
                    candidate_name: {
                        required: true,
                    },
                    expected_date: {
                        required: true
                    },
                    candidate_mobile: {
                        required: true,
                        minlength: 10,
                        maxlength: 16,
                        regex: true,
                    },
                    candidate_email: {
                        required: true,
                        email: true,
                        remote: {
                            url: "{{ route('ajax-check-email-candidate') }}",
                            type: "post",
                            headers: {
                                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                            },
                            data: {
                                email: function() {
                                    return $('input[name="candidate_email"]').val();
                                }
                            },
                            dataFilter: function(data) {
                                var json = JSON.parse(data);
                                if (json.success === "true" || json.success === true) {
                                    let message =
                                        '{{ __('frontend/validation.check_candidate_email') }}';
                                    let user = json.email;
                                    let status = json.status;

                                    let message1 = message.replace(':user', user);
                                    let message2 = message1.replace(':status', status);
                                    return '"' + message2 + '"';
                                } else {
                                    return '"true"';
                                }
                            }
                        },
                        check_duplicate_submit_by_info: true,
                    },
                    candidate_job_title: {
                        required: true
                    },
                    candidate_salary_expect: {
                        // required: true,
                        number: true,
                    },
                    cv_public: {
                        required: true,
                        extension: "pdf,docx",
                        filesize: 5, // <- 5 MB
                    },
                    cv_private: {
                        required: true,
                        extension: "pdf,docx",
                        filesize: 5, // <- 5 MB
                    },
                    // candidate_portfolio: {
                    //     required: true
                    // },
                },
                messages: {
                    candidate_name: {
                        required: '{{ __('frontend/validation.required') }}',
                    },
                    expected_date: {
                        required: '{{ __('frontend/validation.required') }}',
                    },
                    candidate_mobile: {
                        required: '{{ __('frontend/validation.required') }}',
                        minlength: '{{ __('frontend/validation.min', ['min' => 10]) }}',
                        maxlength: '{{ __('frontend/validation.max', ['max' => 16]) }}',
                        regex: '{{ __('frontend/validation.regex_phone') }}',
                    },
                    candidate_email: {
                        required: '{{ __('frontend/validation.required') }}',
                        email: '{{ __('frontend/validation.email') }}',
                    },
                    candidate_job_title: {
                        required: '{{ __('frontend/validation.required') }}',
                    },
                    candidate_salary_expect: {
                        required: '{{ __('frontend/validation.required') }}',
                        number: '{{ __('frontend/validation.number') }}',
                    },
                    cv_public: {
                        required: '{{ __('frontend/validation.required') }}',
                        extension: '{{ __('frontend/validation.format_cv') }}',
                        filesize: '{{ __('frontend/validation.file_max') }}',
                    },
                    cv_private: {
                        required: '{{ __('frontend/validation.required') }}',
                        extension: '{{ __('frontend/validation.format_cv') }}',
                        filesize: '{{ __('frontend/validation.file_max') }}',
                    },
                    candidate_portfolio: {
                        required: '{{ __('frontend/validation.required') }}',
                    },
                },
                highlight: function(element, errorClass, validClass) {
                    var elem = $(element);
                    if (elem.hasClass("select2-hidden-accessible")) {
                        elem.next().parent().addClass(errorClass);
                    } else if (elem.hasClass("file-browserinput")) {
                        elem.parents('.file-browser').find('.file-browser-mask').addClass(errorClass)
                    } else {
                        elem.addClass(errorClass);
                    }


                },
                unhighlight: function(element, errorClass, validClass) {
                    var elem = $(element);
                    if (elem.hasClass("select2-hidden-accessible")) {
                        $("#select2-" + elem.attr("id") + "-container").parent().removeClass(
                            errorClass);
                    } else if (elem.hasClass("file-browserinput")) {
                        elem.parents('.file-browser').find('.file-browser-mask').removeClass(errorClass)
                    } else {
                        elem.removeClass(errorClass);
                    }
                },
                errorPlacement: function(error, element) {
                    var elem = $(element);
                    if (elem.hasClass("select2-hidden-accessible")) {
                        element = elem.next();
                        error.insertAfter(element);
                    } else if (elem.hasClass("file-browserinput")) {
                        element = elem.parents('.file-browser').find('.file-browser-mask');
                        error.insertAfter(element);
                    } else {
                        error.insertAfter(element);
                    }
                },
                submitHandler: function(form) {
                    if (!this.beenSubmitted) {
                        this.beenSubmitted = true;
                        form.submit();
                    }
                }
            });

            $.validator.addMethod('regex', function(value) {
                var regex = /^[0-9()+.-]*$/;
                return value.trim().match(regex);
            });

            $.validator.addMethod('filesize', function(value, element, param) {
                return this.optional(element) || (element.files[0].size <= param * 1000000)
            }, 'File size must be less than {0} MB');

            $.validator.addMethod('check_warehouse_cv', function(value, element) {
                var isSuccess = false;
                var user = '';
                var status = '';
                $.ajax({
                    url: '{{ route('ajax-check-candidate') }}',
                    type: 'POST',
                    async: false,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    data: {
                        cv: value
                    },
                    success: function(data) {
                        isSuccess = data.success;
                        user = data.email;
                        status = data.status;
                    }
                });


                if (isSuccess == true) {
                    let message = '{{ __('frontend/validation.check_candidate_email') }}';

                    let message1 = message.replace(':user', user);
                    let message2 = message1.replace(':status', status);

                    $.validator.messages.check_warehouse_cv = message2;
                    return false;
                } else {
                    return true;
                }
            });

            $.validator.addMethod('check_duplicate_submit_by_cv', function(value, element) {
                var isSuccess = false;
                $.ajax({
                    url: '{{ route('ajax-check-duplicate-submit-by-cv') }}',
                    type: 'POST',
                    async: false,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    data: {
                        cv: value,
                        slug: $("input[name='slug']").val(),
                    },
                    success: function(data) {
                        isSuccess = data.success;
                    }
                });

                if (isSuccess == true) {
                    let message = '{{ __('frontend/job/message.error_duplicate_job') }}';
                    $.validator.messages.check_duplicate_submit_by_cv = message;
                    return false;
                } else {
                    return true;
                }
            });

            $.validator.addMethod('check_duplicate_submit_by_info', function(value, element) {
                var isSuccess = false;
                $.ajax({
                    url: '{{ route('check-duplicate-submit-by-new-info') }}',
                    type: 'POST',
                    async: false,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    data: {
                        email: value,
                        phone: $("input[name='candidate_mobile']").val(),
                        slug: $("input[name='slug']").val(),
                    },
                    success: function(data) {
                        isSuccess = data.success;
                    }
                });

                if (isSuccess == true) {
                    let message = '{{ __('frontend/job/message.error_duplicate_job') }}';
                    $.validator.messages.check_duplicate_submit_by_info = message;
                    return false;
                } else {
                    return true;
                }
            });

            var urlCv = '{{ route('get-cv-by-user-submit') }}';
            $("#list_cv").select2({
                ajax: {
                    url: urlCv,
                    type: "post",
                    dataType: 'json',
                    delay: 550,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    data: function(params) {
                        return {
                            searchTerm: params.term // search term
                        };
                    },
                    processResults: function(response) {
                        return {
                            results: response
                        };
                    },
                    cache: true
                }
            });

            $('[ data-toggle="back-job-mobile"]').click(function() {
                $('#col-detail-job').removeClass('show-render-job');
                $('#main-col-job').removeClass('hide-col');
            });

            $(document).on('click', '.title-main-info-company', function() {
                let slug = $(this).data('slug');
                let url = '{{ route('detail-company', ':slug') }}';
                url = url.replace(':slug', slug);
                window.location.href = url;
            });

            var getUrlParameter = function getUrlParameter(sParam) {
                var sPageURL = window.location.search.substring(1),
                    sURLVariables = sPageURL.split('&'),
                    sParameterName,
                    i;

                for (i = 0; i < sURLVariables.length; i++) {
                    sParameterName = sURLVariables[i].split('=');

                    if (sParameterName[0] === sParam) {
                        return sParameterName[1] === undefined ? true : decodeURIComponent(sParameterName[1]);
                    }
                }
                return false;
            };

            var type = getUrlParameter('type');
            if (type === '0') {
                $('.item-header-job-st1').trigger('click');
            } else if (type === '1') {
                $('.item-header-job-st2').trigger('click');
            }
        });
</script>
@endsection

@push('style')
<link rel="stylesheet" href="{{ asset2('frontend/asset/css/side-modals.css') }}">
<link rel="stylesheet" href="{{ asset2('frontend/asset/css/introducation.css') }}">
<style>
    .item-content-job strong {
        font-weight: bolder !important;
    }

    .item-content-job strong em {
        font-style: italic !important;
    }
</style>
@endpush