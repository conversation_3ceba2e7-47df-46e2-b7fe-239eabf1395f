{"__meta": {"id": "01K1SZ3VGB6NP89GEV8F70DBTR", "datetime": "2025-08-04 14:18:11", "utime": **********.724667, "method": "GET", "uri": "/admin/collaborator/7486/edit", "ip": "127.0.0.1"}, "php": {"version": "8.1.6", "interface": "apache2handler"}, "messages": {"count": 5, "messages": [{"message": "[14:18:11] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `id` = '1' limit 1\\n-- \",\n    \"Time:\": 23.72\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.393308, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:11] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)\\n-- \",\n    \"Time:\": 0.59\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.403318, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:11] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `id` = '7486' limit 1\\n-- \",\n    \"Time:\": 0.56\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.406141, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:11] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `user_role` where `user_role`.`user_id` in (7486)\\n-- \",\n    \"Time:\": 0.41\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.409326, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:11] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `user_infos` where `user_infos`.`user_id` = '7486' and `user_infos`.`user_id` is not null limit 1\\n-- \",\n    \"Time:\": 1.7\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.70518, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 3, "start": 1754291890.89938, "end": **********.724694, "duration": 0.8253140449523926, "duration_str": "825ms", "measures": [{"label": "Booting", "start": 1754291890.89938, "relative_start": 0, "end": **********.312905, "relative_end": **********.312905, "duration": 0.****************, "duration_str": "414ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.312916, "relative_start": 0.*****************, "end": **********.724697, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "412ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.326265, "relative_start": 0.*****************, "end": **********.335776, "relative_end": **********.335776, "duration": 0.009510993957519531, "duration_str": "9.51ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "40MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 7, "nb_templates": 7, "templates": [{"name": "1x admin.pages.collaborator.edit", "param_count": null, "params": [], "start": **********.421032, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/pages/collaborator/edit.blade.phpadmin.pages.collaborator.edit", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Fpages%2Fcollaborator%2Fedit.blade.php&line=1", "ajax": false, "filename": "edit.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.pages.collaborator.edit"}, {"name": "1x admin.inc_layouts.datatable.table", "param_count": null, "params": [], "start": **********.709077, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/inc_layouts/datatable/table.blade.phpadmin.inc_layouts.datatable.table", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Finc_layouts%2Fdatatable%2Ftable.blade.php&line=1", "ajax": false, "filename": "table.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.inc_layouts.datatable.table"}, {"name": "1x admin.layouts.app", "param_count": null, "params": [], "start": **********.712327, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/layouts/app.blade.phpadmin.layouts.app", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.layouts.app"}, {"name": "1x admin.inc_layouts.side_bar", "param_count": null, "params": [], "start": **********.715105, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/inc_layouts/side_bar.blade.phpadmin.inc_layouts.side_bar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Finc_layouts%2Fside_bar.blade.php&line=1", "ajax": false, "filename": "side_bar.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.inc_layouts.side_bar"}, {"name": "1x admin.inc_layouts.header", "param_count": null, "params": [], "start": **********.718566, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/inc_layouts/header.blade.phpadmin.inc_layouts.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Finc_layouts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.inc_layouts.header"}, {"name": "1x admin.inc_layouts.footer", "param_count": null, "params": [], "start": **********.719863, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/inc_layouts/footer.blade.phpadmin.inc_layouts.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Finc_layouts%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.inc_layouts.footer"}, {"name": "1x admin.inc_layouts.toast.message", "param_count": null, "params": [], "start": **********.721694, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/inc_layouts/toast/message.blade.phpadmin.inc_layouts.toast.message", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Finc_layouts%2Ftoast%2Fmessage.blade.php&line=1", "ajax": false, "filename": "message.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.inc_layouts.toast.message"}]}, "route": {"uri": "GET admin/collaborator/{user_id}/edit", "middleware": "web, check-admin, check-role", "controller": "App\\Http\\Controllers\\Admin\\CollaboratorController@edit<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FCollaboratorController.php&line=43\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "admin/collaborator", "as": "collaborator.edit", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FCollaboratorController.php&line=43\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Admin/CollaboratorController.php:43-48</a>"}, "queries": {"count": 5, "nb_statements": 5, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02698, "accumulated_duration_str": "26.98ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "check-admin", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckLoginAdmin.php", "line": 20}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.369914, "duration": 0.023719999999999998, "duration_str": "23.72ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `id` = ? limit 1", "hash": "ac0ef66d2bf40f3993c8e40de021b0d99da887a13adec01b9fbb0601a2bf69c5"}, "start_percent": 0, "width_percent": 87.917}, {"sql": "select `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Services/Admin/PermissionService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\PermissionService.php", "line": 19}, {"index": 19, "namespace": null, "name": "app/Services/Admin/PermissionService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\PermissionService.php", "line": 31}, {"index": 20, "namespace": "middleware", "name": "check-role", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckRole.php", "line": 26}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 22, "namespace": "middleware", "name": "check-admin", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckLoginAdmin.php", "line": 21}], "start": **********.402798, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "PermissionService.php:19", "source": {"index": 18, "namespace": null, "name": "app/Services/Admin/PermissionService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\PermissionService.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FServices%2FAdmin%2FPermissionService.php&line=19", "ajax": false, "filename": "PermissionService.php", "line": "19"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)", "hash": "09b63fe1be07508dfb18b73a990c5ee0249dd740fb6501945ccdb8043c3b971c"}, "start_percent": 87.917, "width_percent": 2.187}, {"sql": "select * from `users` where `id` = '7486' limit 1", "type": "query", "params": [], "bindings": ["7486"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserRepository.php", "line": 96}, {"index": 17, "namespace": null, "name": "app/Services/Admin/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\UserService.php", "line": 156}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/CollaboratorController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\CollaboratorController.php", "line": 45}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.405652, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "UserRepository.php:96", "source": {"index": 16, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserRepository.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserRepository.php&line=96", "ajax": false, "filename": "UserRepository.php", "line": "96"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `id` = ? limit 1", "hash": "fb2168ee2da5023c2f7c4a319faef873b5423ca4635b0b48cb56c5581ea005d4"}, "start_percent": 90.104, "width_percent": 2.076}, {"sql": "select * from `user_role` where `user_role`.`user_id` in (7486)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserRepository.php", "line": 96}, {"index": 22, "namespace": null, "name": "app/Services/Admin/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\UserService.php", "line": 156}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Admin/CollaboratorController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\CollaboratorController.php", "line": 45}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.4089859, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "UserRepository.php:96", "source": {"index": 21, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserRepository.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserRepository.php&line=96", "ajax": false, "filename": "UserRepository.php", "line": "96"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `user_role` where `user_role`.`user_id` in (7486)", "hash": "5b985c2ae97cd78d12299b46972768ed274ed442ab1d3fc6fad585f8e33d9ab2"}, "start_percent": 92.179, "width_percent": 1.52}, {"sql": "select * from `user_infos` where `user_infos`.`user_id` = 7486 and `user_infos`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [7486], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "view", "name": "admin.pages.collaborator.edit", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/admin/pages/collaborator/edit.blade.php", "line": 91}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.703644, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "admin.pages.collaborator.edit:91", "source": {"index": 21, "namespace": "view", "name": "admin.pages.collaborator.edit", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/admin/pages/collaborator/edit.blade.php", "line": 91}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Fpages%2Fcollaborator%2Fedit.blade.php&line=91", "ajax": false, "filename": "edit.blade.php", "line": "91"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `user_infos` where `user_infos`.`user_id` = ? and `user_infos`.`user_id` is not null limit 1", "hash": "f94e1b23e05ab1368bcf1413eb41ce6bcacb967cbaf4f618a15307e39d6f1fc8"}, "start_percent": 93.699, "width_percent": 6.301}]}, "models": {"data": {"App\\Models\\User": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Role": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 3, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 3}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "67FrIiAKRzqdDDYE9lpf1v63S50ahQba2svcMgmp", "_previous": "array:1 [\n  \"url\" => \"http://recland.local/rec/profile\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "login_client_59ba36addc2b2f9401580f014c7f58ea4e30989d": "512", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://recland.local/admin/collaborator/7486/edit", "action_name": "collaborator.edit", "controller_action": "App\\Http\\Controllers\\Admin\\CollaboratorController@edit", "uri": "GET admin/collaborator/{user_id}/edit", "controller": "App\\Http\\Controllers\\Admin\\CollaboratorController@edit<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FCollaboratorController.php&line=43\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "admin/collaborator", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FCollaboratorController.php&line=43\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Admin/CollaboratorController.php:43-48</a>", "middleware": "web, check-admin, check-role", "duration": "825ms", "peak_memory": "50MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-2103430950 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2103430950\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1326390405 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1326390405\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"43 characters\">http://recland.local/admin/new-collaborator</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,vi;q=0.8,nl;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1257 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImY3TGRVVHJreEIyNENkanA2eEFBb0E9PSIsInZhbHVlIjoicmlGN1VsWTBwZGUrWmwreHFpMkRyQ3BWNnlDTXFvSmNGOXJMNklTQWo1YW9TQmRTVk9VVUt3aTVqMXdnaGNqbVp2dlZaL0Z3SkFoK0N6QThoSmh6OE8vbVFXK3BFditMTmtiR0wxMmdrRmZ5aklydi9HSUU0SGtRTEpnWGtsVWEiLCJtYWMiOiI2ZGRjYjE0NWM2ZTgxMjRlZTNmMjQwOWIxNzY0MGIxNzkzNWU1ZjBkYzIwNTlkZjliOGEzYjM2MTdiMzU0MTIxIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IlVvTmNXNUYrbGVkdTJKYWIxS0FWRWc9PSIsInZhbHVlIjoidG9QYU5kQVc5SEJuZkFmTTBubnBkdHFzN1hzSjhjcUh0WmdYL2dDVkNCYWQwaUNTK2VEeGFwODE0ZzBiamRCYUNNUWlVZFJOM0UxS2ZqWkhIWGN1SmR0NkJ5Q2JVOWhKcFpiNmViNVJPWnNGMWlaUUtNVGw1Z1F3YkxtU2UwRlkiLCJtYWMiOiI4NGU2N2UyZjU4NTYzYzhjNzYxY2Y5OTFjMjdjZWQwZWM3MWM0OTc1MTU4YTJkZjM4YjcwYWY5YTE0ODQzNWVjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|cm8f4SgFu8wrAd7wPR9b0gsQwQ8YYGlGSWS8Y9hgKQXIsj7qT8VG3KwC3dMx|$2y$10$iTSniY9ReMSnjm6GaX0bRuGWPNsyk9pQat6r8dKHdyoQSgwMkVojS</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">67FrIiAKRzqdDDYE9lpf1v63S50ahQba2svcMgmp</span>\"\n  \"<span class=sf-dump-key>recland_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1VyD9s5I8kaKasZMxJDemZkFPxfq8PRabbTbE7MT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-762908770 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 04 Aug 2025 07:18:11 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-762908770\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1920437094 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">67FrIiAKRzqdDDYE9lpf1v63S50ahQba2svcMgmp</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">http://recland.local/rec/profile</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>login_client_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>512</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1920437094\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://recland.local/admin/collaborator/7486/edit", "action_name": "collaborator.edit", "controller_action": "App\\Http\\Controllers\\Admin\\CollaboratorController@edit"}, "badge": null}}