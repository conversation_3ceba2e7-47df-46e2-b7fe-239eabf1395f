<?php

namespace App\Notifications;

use App\Helpers\Common;
use App\Services\Frontend\SettingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class Register extends Notification implements ShouldQueue
{
    use Queueable;

    protected $user;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($user = null)
    {
        $this->user = $user;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     *
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     *
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $url = config('constant.url') . '/verify/?token=' . $this->user->token;
        return (new MailMessage)

            ->action('Reset Password', url($url))
            ->view('email.register', [
                'name' => $this->user->name,
                'url' => $url,
                'title' => 'XÁC MINH ĐỊA CHỈ EMAIL CỦA BẠN'
            ])
            ->subject('[Re<PERSON>land] [XÁC THỰC TÀI KHOẢN CỦA CTV]');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     *
     * @return array
     */
    public function toArray($notifiable)
    {
        $settings = resolve(SettingService::class);

        $str = 'notification';

        $arrLangVi = $settings->getAllKeyWithQuery($str, config('constant.language.vi'));
        $arrLangEn = $settings->getAllKeyWithQuery($str, config('constant.language.en'));

        $contentVi = Common::transLang($arrLangVi['ctv_email_verify_rec']);
        $contentEn = Common::transLang($arrLangEn['ctv_email_verify_rec']);

        return [
            'content_vi' => $contentVi,
            'content_en' => $contentEn,
        ];
    }
}
