<?php

namespace App\Services\Frontend;

use App\Notifications\AdminReportSystem;
use App\Notifications\BuyCvSuccessSendMailAdmin;
use App\Notifications\BuyCvSuccessSendMailCandidate;
use App\Notifications\ChangeRoleUser;
use App\Notifications\InviteUser;
use App\Notifications\NotificationDeposits;
use App\Notifications\RecEmployerReportSystem;
use App\Notifications\Register;
use App\Notifications\RegisterEmployer;
use App\Notifications\RegisterSuccess;
use App\Notifications\ResetPassword;
use App\Notifications\UpdateProfileEmployer;
use App\Repositories\BonusRepository;
use App\Repositories\DepositRepository;
use App\Repositories\EmployeeRoleRepository;
use App\Repositories\EmployerTypeRepository;
use App\Repositories\PasswordResetRepository;
use App\Repositories\ReportRepository;
use App\Repositories\UserInfoRepository;
use App\Repositories\UserRepository;
use App\Repositories\UserRoleRepository;
use App\Repositories\WalletRepository;
use App\Repositories\CompanyRepository;
use App\Services\FileServiceS3;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Str;
use Laravel\Socialite\Contracts\User as ProviderUser;

class UserService
{
    protected $userRepository;
    protected $userRoleRepository;
    protected $passwordResetRepository;
    protected $userInfoRepository;
    protected $bonusRepository;
    protected $employeeRoleRepository;
    protected $employerTypeRepository;
    protected $walletRepository;
    protected $depositRepository;
    protected $reportRepository;

    public function __construct(
        UserRepository $userRepository,
        UserRoleRepository $userRoleRepository,
        UserInfoRepository $userInfoRepository,
        PasswordResetRepository $passwordResetRepository,
        BonusRepository $bonusRepository,
        EmployeeRoleRepository $employeeRoleRepository,
        EmployerTypeRepository $employerTypeRepository,
        WalletRepository $walletRepository,
        DepositRepository $depositRepository,
        ReportRepository $reportRepository,
    ) {
        $this->userRepository = $userRepository;
        $this->userRoleRepository = $userRoleRepository;
        $this->passwordResetRepository = $passwordResetRepository;
        $this->userInfoRepository = $userInfoRepository;
        $this->bonusRepository = $bonusRepository;
        $this->employeeRoleRepository = $employeeRoleRepository;
        $this->employerTypeRepository = $employerTypeRepository;
        $this->walletRepository = $walletRepository;
        $this->depositRepository = $depositRepository;
        $this->reportRepository = $reportRepository;
    }

    public function login($params, $type = 'admin')
    {
        $user = $this->userRepository->findEmail($type, $params['email']);
        if (!$user) {
            //TODO
            return false;
        }

        //check pass wrong
        if (!Hash::check($params['password'], $user->password)) {
            //TODO
            return false;
        }

        //        if (!$user->email_verified_at) {
        //            if ($type == config('constant.role.rec')) {
        //                //send mail register
        //                $user->notify(new Register($user));
        //            }
        //            return false;
        //        }

        if ($type == config('constant.role.rec') && $user->is_active == config('constant.inActive')) {
            throw new \Exception(__('message.login_fail_is_active'));
        }

        $credentials = [
            'email' => $params['email'],
            'password' => $params['password'],
            'type' => $type,
            'is_active' => config('constant.active'),
        ];
        $remember = isset($params['remember']) ? true : false;
        if (Auth::guard('client')->attempt($credentials, $remember)) {
            $dataUpdate = ['last_login_at' => Carbon::now()];
            if ($type == config('constant.role.rec') && empty($user->referral_define)) {
                $referral_define = $this->buildReferralCode();
                $dataUpdate['referral_define'] = $referral_define;
            }
            // Cập nhật thời gian đăng nhập cuối cùng
            $user->update($dataUpdate);

            $user->tokens()->delete();
            Cache::put('accept_token_api_' . $user->id, $user->createToken('Personal Access Token')->plainTextToken, now()->addMinutes(240));
            return true;
        }

        return false;
    }

    public function register($params, $type = 'admin', $sendMail = true)
    {
        $data = [
            'name'          => $params['name'],
            'email'         => $params['email'],
            'mobile'        => $params['mobile'],
            'company_name'  => $params['company_name'] ?? null,
            'mst'           => $params['mst'] ?? null,
            'work_position' => $params['work_position'] ?? null,
            'address'       => $params['work_location'] ?? null,
            'work_location' => $params['work_location'] ?? null,
            'referral_code' => isset($params['referral_code']) ? $params['referral_code'] : '',
            //    'mst' => isset($params['mst']) ? $params['mst'] : '',
            'password' => isset($params['password']) ? bcrypt($params['password']) : '',
            'type' => $type,
            //            'is_active' => config('constant.active'),
            'token' => Str::random(32),
            'last_login_at' => Carbon::now(),
        ];
        $keys = [
            'company_name',
            'source',
            'referral_code',
            'email_verified_at',
            'provider',
            'provider_id',
        ];
        foreach ($keys as $key) {
            if (isset($params[$key])) {
                $data[$key] = $params[$key];
            }
        }
        //CTV build referral_define
        if ($type == config('constant.role.rec')) {
            $referral_define = $this->buildReferralCode();
            $data['referral_define'] = $referral_define;
            if (!empty($data['referral_code'])) {
                $userRef = $this->userRepository->findByReferralDefine($data['referral_code']);
                if ($userRef) {
                    $data['parent_id'] = $userRef->id;
                }
            }
        }
        if (config('settings.global.auto_create_company') && $type == config('constant.role.employer') && !empty($data['company_name']) && !empty($data['mst'])) {
            $companyRepository = new CompanyRepository();
            $company = $companyRepository->create([
                'name' => $data['company_name'],
                'mst' => $data['mst'],
                'address' => $data['address'],
            ]);
            $data['company_id'] = $company->id;
        }

        $user = $this->userRepository->create($data);
        //ctv
        if ($type == config('constant.role.rec') && !$user->email_verified_at) {
            $user->notify(new Register($user));
        }
        //ntd
        if ($type == config('constant.role.employer') && !$user->email_verified_at) {
            //create employer type
            $this->employerTypeRepository->create([
                'user_id' => $user->id,
                'type' => 'manager',
            ]);
            if ($sendMail) {
                // Bỏ gửi mail thông báo, vì có mail gửi link xác thực khác rồi
                // $user->notify(new RegisterEmployer($user)); [XÁC THỰC TÀI KHOẢN]
            }
        }

        $this->walletRepository->create([
            'user_id'   => $user->id,
            'type'      => $user->type
        ]);

        return $user;
    }
    public function createWallet($user)
    {
        $this->walletRepository->create([
            'user_id'   => $user->id,
            'type'      => $user->type
        ]);
    }


    public function buildReferralCode()
    {
        $userRecLast = $this->userRepository->findByTypeLastId(config('constant.role.rec'));
        if ($userRecLast) {
            return 'RLC' . ((int) str_replace('RLC', '', $userRecLast->referral_define) + 1);
        }

        return 'RLC10000';
    }

    public function verifyEmail($params)
    {
        try {
            if (!isset($params['token'])) {
                throw new \Exception('Token required', 422);
            }
            $user = $this->userRepository->findByToken($params['token']);
            if (!$user) {
                throw new \Exception('User not found', 404);
            }

            $dataUser = [
                'email_verified_at' => Carbon::now(),
                'token' => null,
            ];
            $this->userRepository->update($user->id, [], $dataUser);

            Auth::guard('client')->loginUsingId($user->id);

            if ($user->type == 'rec') {
                $user->notify(new RegisterSuccess($user));
            }
            //send mail and notify

            return true;
        } catch (\Exception $exception) {
            Log::error(__CLASS__ . __FUNCTION__, ['exception' => $exception, 'request' => request()]);
            abort(500);
        }
    }

    public function sendEmailResetPassword($email, $type, $url = null)
    {
        $token = $this->generateToken($email, $type);
        try {
            if ($token) {
                $user = $this->userRepository->findEmail($type, $email);
                $user->notify(new ResetPassword($user, $token, $url));
                return true;
            }
        } catch (\Exception $exception) {
            return false;
        }
        return false;
    }

    public function generateToken($email, $type)
    {
        $passwordReset = $this->passwordResetRepository->updateOrCreate(
            [
                'email' => $email,
            ],
            [
                'token' => Str::random(60),
                'type' => $type,
            ]
        );
        return $passwordReset->token;
    }

    public function resetPassword($request)
    {
        try {
            $passwordReset = $this->passwordResetRepository->findByToken($request['token']);
            if (!$passwordReset) {
                throw new \Exception(__('frontend/login/message.token_expired'), 404);
            }
            if (Carbon::parse($passwordReset->updated_at)->addMinutes(720)->isPast()) {
                $passwordReset->delete();
                throw new \Exception(__('frontend/login/message.token_expired'), 404);
            }
            $type = $passwordReset->type;
            $user = $this->userRepository->findEmail($type, $passwordReset->email);
            if (empty($user->email_verified_at)) {
                $user->email_verified_at = date('Y-m-d H:i:s');
            }
            $user->password = bcrypt($request['password']);
            $user->save();
            $passwordReset->delete();
            return true;
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }

    public function loginSocial(ProviderUser $providerUser, $social)
    {
        try {
            $user = $this->userRepository->findEmail(config('constant.role.rec'), $providerUser->email);

            if ($user) {
                $update['provider'] = $social;
                $update['provider_id'] = $providerUser->id;
                if (!$user->email_verified_at) {
                    $update['email_verified_at'] = Carbon::now();
                }
                $update['last_login_at'] = Carbon::now();
                $user->update($update);
            } else {
                $params = [
                    'name' => $providerUser->name,
                    'email' => isset($providerUser->email) && $providerUser->email ? $providerUser->email : $providerUser->id . '@gmail.com',
                    'email_verified_at' => Carbon::now(),
                    'phone' => '',
                    'provider' => $social,
                    'provider_id' => $providerUser->id,
                    'last_login_at' => Carbon::now(),
                ];
                $user = $this->register($params, config('constant.role.rec'));
            }
            Auth::guard('client')->login($user);
            return true;
        } catch (\Exception $e) {
            throw new \Exception($e);
        }
    }


    public function updateProfile($params, $userId)
    {
        try {
            //update thông tin cá nhân, có 4 field
            $data = [];
            if (isset($params['name']) && $params['name'] != '') $data['name'] = $params['name'];
            if (isset($params['mobile']) && $params['mobile'] != '') $data['mobile'] = $params['mobile'];
            if (isset($params['email']) && $params['email'] != '') $data['email'] = $params['email'];
            if (isset($params['address']) && $params['address'] != '') $data['address'] = $params['address'];
            if (isset($params['birthday']) && $params['birthday'] != '') {
                $birthday = Carbon::createFromFormat('d/m/Y', $params['birthday']);
                $data['birthday'] = $birthday;
            }

            if (isset($params['avatar']) && is_file($params['avatar'])) {
                $data['avatar'] = FileServiceS3::getInstance()->uploadToS3($params['avatar'], config('constant.sub_path_s3.collaborator'));
            }
            $this->userRepository->update($userId, [], $data);

            // Cập nhật thông tin CCCD vào user_infos
            $userInfoData = [];
            if (isset($params['cccd_number']) && $params['cccd_number'] != '') {
                $userInfoData['cccd_number'] = $params['cccd_number'];
            }
            
            if (isset($params['cccd_front_image']) && is_file($params['cccd_front_image'])) {
                $userInfoData['cccd_front_image'] = FileServiceS3::getInstance()->uploadToS3($params['cccd_front_image'], config('constant.sub_path_s3.collaborator'));
            }
            
            if (isset($params['cccd_back_image']) && is_file($params['cccd_back_image'])) {
                $userInfoData['cccd_back_image'] = FileServiceS3::getInstance()->uploadToS3($params['cccd_back_image'], config('constant.sub_path_s3.collaborator'));
            }
            
            if (!empty($userInfoData)) {
                $this->userInfoRepository->updateByUserId($userId, [], $userInfoData);
            }

            //send mail and notify
            $rec = $this->userRepository->find($userId);
            $rec->notify(new UpdateProfileEmployer($rec));

            return true;
        } catch (\Exception $exception) {
            Log::error(__CLASS__ . __FUNCTION__, ['exception' => $exception, 'request' => request()]);
            abort(500);
        }
    }

    public function updateBankInfo($params, $userId)
    {
        $dataInfo = [];
        if (isset($params['bank_account']) && $params['bank_account'] != '') $dataInfo['bank_account'] = $params['bank_account'];
        if (isset($params['bank_name']) && $params['bank_name'] != '') $dataInfo['bank_name'] = $params['bank_name'];
        if (isset($params['bank_account_number']) && $params['bank_account_number'] != '') $dataInfo['bank_account_number'] = $params['bank_account_number'];
        if (isset($params['bank_branch']) && $params['bank_branch'] != '') $dataInfo['bank_branch'] = $params['bank_branch'];
        $this->userInfoRepository->updateByUserId($userId, [], $dataInfo);

        //send mail and notify
        $rec = $this->userRepository->find($userId);
        $rec->notify(new UpdateProfileEmployer($rec));

        return $this->userInfoRepository->findByUserId($userId);
    }

    public function changePasswordById($id, $password)
    {
        $data = $this->userRepository->update($id, [], ['password' => Hash::make($password)]);
        if ($data) {
            $user = $this->userRepository->find($id);

            $user->notify(new UpdateProfileEmployer($user));
        }

        return $data;
    }

    public function changeUserById($id, $data)
    {
        $data = $this->userRepository->update($id, [], $data);
        if ($data) {
            $user = $this->userRepository->find($id);

            $user->notify(new UpdateProfileEmployer($user));
        }
        return $data;
    }

    public function teams($params)
    {
        $userId = auth('client')->user()->id;
        return $this->userRepository->getTeams($userId, $params);
    }

    public function countTeams($params)
    {
        $userId = auth('client')->user()->id;
        return $this->userRepository->countTeams($userId, $params);
    }

    public function detailUserTeam($id)
    {
        $months = array();
        for ($i = 1; $i <= 12; $i++) {
            $months[] = date('m', mktime(0, 0, 0, $i, '01'));
        }

        $data = $this->bonusRepository->getAllWithUserId($id);
        $years = $this->getYearOfUser($id);

        $result = [];
        if (count($years)) {
            foreach ($years as $year) {
                foreach ($months as $month) {
                    foreach ($data as $item) {
                        if ($item['month'] == $month && $item['year'] == $year) {
                            $result[$year][$month]['money'] = $item['money'];
                            $result[$year][$month]['money_kpi'] = $item['money_kpi'];
                            break;
                        } else {
                            $result[$year][$month]['money'] = 0;
                            $result[$year][$month]['money_kpi'] = 0;
                        }
                    }
                }
            }
        }

        return $result;
    }

    public function getYearOfUser($userId)
    {
        return $this->bonusRepository->getYearWithUserId($userId);
    }

    public function statisticalUser($userId)
    {
        return $this->userRepository->statisticalUser($userId);
    }

    public function checkReferralCode($referralCode)
    {
        return $this->userRepository->findByReferralDefine($referralCode);
    }

    public function getUserCompany($companyId, $params)
    {
        $params['is_invalid'] = config('constant.isInvalid');
        return $this->userRepository->getUserWithCompanyId($companyId, $params);
    }

    public function getCompanyRole($companyId)
    {
        return $this->employeeRoleRepository->getListRole($companyId);
    }

    public function getUserRoleCompany($companyId)
    {
        return $this->employeeRoleRepository->getRoleWithCompany($companyId);
    }

    public function changeRole($id, $params)
    {
        //send mail
        $employerType = $this->employerTypeRepository->find($id);
        $employerRole = $this->employeeRoleRepository->find($params['roleId']);
        $user = $this->userRepository->find($employerType->user_id);

        $user->notify(new ChangeRoleUser($user, $employerRole->name));

        return $this->employerTypeRepository->update($id, [], [
            'employee_role_id' => $params['roleId']
        ]);
    }

    public function changeActive($id, $params)
    {
        $checkType = $this->employerTypeRepository->findWithUserId($id);

        if ($checkType->type == 'manager') {
            return false;
        }

        return $this->userRepository->update($id, [], [
            'is_active' => $params['is_active'] == 'true' ? 1 : 0
        ]);
    }

    public function deleteRole($id)
    {
        $this->employerTypeRepository->updateWithEmployeeRole($id);
        return $this->employeeRoleRepository->delete($id);
    }

    public function addRole($params)
    {
        $permissionRequest = isset($params['role']) ? $params['role'] : [];
        $permissions = $this->getGroupPermission($permissionRequest);
        $data = [
            'name' => $params['name'],
            'company_id' => auth('client')->user()->company_id,
            'permission' => json_encode($permissions, JSON_FORCE_OBJECT)
        ];
        return $this->employeeRoleRepository->create($data);
    }

    protected function getGroupPermission(array $permissionRequest)
    {
        $rolePermission = config('constant.role_employee_permission');
        $permissions = [];
        foreach ($rolePermission as $items) {
            foreach ($permissionRequest as $value) {
                $permissions[] = $value;
                if (!empty($items[$value]['action_save'])) {
                    $permissions = array_merge($permissions, $items[$value]['action_save']);
                }
            }
        }
        $permissions = array_unique($permissions);
        return $permissions;
    }

    public function getRole($id)
    {
        return $this->employeeRoleRepository->find($id);
    }

    public function updateRole($id, $params)
    {
        $permissionRequest = isset($params['edit-role']) ? $params['edit-role'] : [];
        $permissions = $this->getGroupPermission($permissionRequest);
        $data = [
            'name' => $params['edit-name'],
            'company_id' => auth('client')->user()->company_id,
            'permission' => json_encode($permissions, JSON_FORCE_OBJECT)
        ];
        return $this->employeeRoleRepository->update($id, [], $data);
    }

    public function createUser($params)
    {
        $userLogin = auth('client')->user();

        $arrUser = [];
        foreach ($params['name'] as $k => $email) {
            if (!in_array($email, $arrUser)) {
                $user = $this->userRepository->create([
                    'company_id' => $userLogin->company_id,
                    'email' => $email,
                    'password' => bcrypt($email),
                    'name' => 'N/A',
                    'type' => config('constant.role.employer'),
                    'is_active' => config('constant.inActive'),
                    'token' => Str::random(32),
                    'mst' => $userLogin->company->mst,
                ]);

                $this->employerTypeRepository->create([
                    'user_id' => $user->id,
                    'employee_role_id' => $params['role'][$k],
                    'type' => 'employee',
                ]);

                $role = $this->employeeRoleRepository->find($params['role'][$k]);

                //send mail
                $user->notify(new InviteUser($user, $email, $role->name));

                array_push($arrUser, $email);
            }
        }
    }

    public function getUserInvite($companyId)
    {
        $params['is_invalid'] = config('constant.invalid');
        return $this->userRepository->getUserWithCompanyId($companyId, $params, false);
    }

    public function verifyEmailInvite($params)
    {
        if (empty($params['token'])) {
            return false;
        }

        $user = $this->userRepository->findByToken($params['token']);

        if (!$user) {
            return false;
        }

        return $user;
    }

    public function checkEmailType($params)
    {
        return $this->userRepository->findEmailType($params['email'], 'employer');
    }

    public function resendMailInvite($params)
    {
        $userLogin = auth('client')->user();

        $user = $this->userRepository->find($params['user_id']);

        $roleName = $user->userEmployerType->employeeRole ? $user->userEmployerType->employeeRole->name : '';

        if (!$user) {
            return false;
        }

        $executed = RateLimiter::attempt(
            'resend-mail:' . $user->id,
            $perMinute = 1,
            function () {
                // Send message...
            }
        );

        if ($executed) {
            $user->notify(new InviteUser($user, $user->email, $roleName));
            return true;
        } else {
            return false;
        }
    }

    public function updateUserInvite($params)
    {
        if (!$params['token']) {
            return false;
        }

        $user = $this->userRepository->findByToken($params['token']);

        $this->userRepository->update($user->id, [], [
            'name'  => $params['name'],
            'mobile' => $params['phone'],
            'password' => bcrypt($params['password']),
            'email_verified_at' => Carbon::now(),
            'is_active' => config('constant.active'),
            'token' => null,
        ]);

        $this->employerTypeRepository->updateWithUserId($user->id, [
            'is_invalid' => config('constant.isInvalid')
        ]);

        return true;
    }

    public function getWalletUser($id)
    {
        return $this->walletRepository->getWalletUser($id);
    }

    public function getDeposit($params)
    {
        $user = auth('client')->user();
        return $this->depositRepository->getListDepositByUser($params, $user->id);
    }

    public function statisticalDeposit()
    {
        $user = auth('client')->user();

        $start_month = new Carbon('first day of this month');
        $start_month = $start_month->startOfMonth()->toDateTimeString();
        $end_month = new Carbon('last day of this month');
        $end_month = $end_month->endOfMonth()->toDateTimeString();

        $start_year = new Carbon('first day of this year');
        $start_year = $start_year->startOfYear()->toDateTimeString();
        $end_year = new Carbon('last day of this year');
        $end_year = $end_year->endOfYear()->toDateTimeString();

        $statistical = [];
        $statistical['this_month'] = $this->depositRepository->statisticalDeposit($user->id, $start_month, $end_month);
        $statistical['this_year'] = $this->depositRepository->statisticalDeposit($user->id, $start_year, $end_year);
        return $statistical;
    }

    public function deposit($params)
    {
        $denominations = config('constant.deposit_price_and_point')[$params['denominations']];
        $method = config('constant.deposit_method_value')[$params['payment_method']];
        Mail::to(config('settings.global.email_admin'))->send(new NotificationDeposits(auth('client')->user(), $denominations));
        return ['point' => $denominations['point']];
    }

    public function report($params)
    {
        if (isset($params['file'])) {
            $params['file'] = FileServiceS3::getInstance()->uploadToS3($params['file'], config('constant.sub_path_s3.report'));
        }

        $user = auth('client')->user();
        if (!$user) {
            return false;
        }

        $data = [
            'user_id'       => $user->id,
            'role'          => $user->type,
            'type_issue'    => $params['type_issue'],
            'description'   => $params['description'],
            'file'          => isset($params['file']) ? $params['file'] : null,
        ];
        $report = $this->reportRepository->create($data);

        //send mail ctv ntd
        $user->notify(new RecEmployerReportSystem($user->name, $report));

        //send mail admin
        Mail::to(config('settings.global.email_admin'))->send(new AdminReportSystem($report));

        return true;
    }
    // function to set confirmed_at to current time to meta data
    public function setConfirmed($user)
    {
        $metaData = $user->metaData()->where('key', 'employer_confirmed_at')->first();
        if ($metaData) {
            $metaData->value = Carbon::now();
            $metaData->save();
        } else {
            $user->metaData()->create([
                'key' => 'employer_confirmed_at',
                'value' => Carbon::now(),
            ]);
        }
    }
    public function getStatusEmployerIsValid()
    {
        return $this->userRepository->statusEmployerIsValid();
    }
    public function getUserIsActive()
    {
        return $this->userRepository->employerIsActive();
    }

    public function checkEmpoyerHasBonusByEmail($email)
    {
        $user = $this->userRepository->findByEmail($email, 'employer');
        if (!$user) {
            return false;
        }
        $bonus = $user->getMeta('employer_bonus_amount_refer');
        if ($bonus) {
            return true;
        }
        return false;
    }
    public function addBonusEmployerRefer($email)
    {
        $user = $this->userRepository->findByEmail($email, 'employer');
        if (!$user) {
            return false;
        }
        $walletService = resolve(WalletService::class);
        $walletService->addAmount($user->id, config('constant.bonus_employer_amount_refer'), 'Nạp tiền từ việc giới thiệu', 'employer_bonus_amount_refer');
        $user->setMeta('employer_bonus_amount_refer', config('constant.bonus_employer_amount_refer'));
        return true;
    }
}
