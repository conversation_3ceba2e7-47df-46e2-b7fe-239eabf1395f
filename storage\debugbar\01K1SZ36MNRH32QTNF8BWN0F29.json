{"__meta": {"id": "01K1SZ36MNRH32QTNF8BWN0F29", "datetime": "2025-08-04 14:17:50", "utime": **********.358854, "method": "GET", "uri": "/rec?show_intro=market", "ip": "127.0.0.1"}, "php": {"version": "8.1.6", "interface": "apache2handler"}, "messages": {"count": 28, "messages": [{"message": "[14:17:48] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `seos` where `key` = 'rec-dashboard' limit 1\\n-- \",\n    \"Time:\": 16.23\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.55737, "xdebug_link": null, "collector": "log"}, {"message": "[14:17:48] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '{\\\"show_intro\\\":\\\"market\\\"}', 'http:\\/\\/recland.local\\/rec?show_intro=market', 'http:\\/\\/recland.local\\/login', '[\\\"en-us\\\",\\\"en\\\",\\\"vi\\\",\\\"nl\\\"]', 'Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********', '{\\\"host\\\":[\\\"recland.local\\\"],\\\"connection\\\":[\\\"keep-alive\\\"],\\\"cache-control\\\":[\\\"max-age=0\\\"],\\\"upgrade-insecure-requests\\\":[\\\"1\\\"],\\\"user-agent\\\":[\\\"Mozilla\\\\\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\\\\\/537.36 (KHTML, like Gecko) Chrome\\\\\\/********* Safari\\\\\\/537.36 Edg\\\\\\/*********\\\"],\\\"accept\\\":[\\\"text\\\\\\/html,application\\\\\\/xhtml+xml,application\\\\\\/xml;q=0.9,image\\\\\\/avif,image\\\\\\/webp,image\\\\\\/apng,*\\\\\\/*;q=0.8,application\\\\\\/signed-exchange;v=b3;q=0.7\\\"],\\\"referer\\\":[\\\"http:\\\\\\/\\\\\\/recland.local\\\\\\/login\\\"],\\\"accept-encoding\\\":[\\\"gzip, deflate\\\"],\\\"accept-language\\\":[\\\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\\\"],\\\"cookie\\\":[\\\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkZrVnltWHJNS2ZzN242RStVUHVwWHc9PSIsInZhbHVlIjoiOTY1ZTRSSEE0WkM3aG9vTlpTOWF1TGNPUVA4WElQYUdVc0F5VGVLQnd4STg4TzQyWDJGSHQ4U2gvUDlOYzYyWnlTcyt4YlhJU21XcmdXT3AvUm4yT3cvdk1RSjZZYkk1THM1eTJNQkhqYVAyOUNRdG5HdW05NXJGbGovQWphaGgiLCJtYWMiOiJiZDVlYTk4OGQ3ODliYzJjZjRjZTIxNDkyMmFjNDUwYjBiOGJjOTkwNWE1ODNiMTdhMGUxYjg2Nzg0YWI4MzhkIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IkVRejFKaHhLTkQ5L2g4RU4rdlBLWUE9PSIsInZhbHVlIjoiaUMwNExYK1VYcWMrdHdEYUpacGdIR2t0SEhDWmxRZFZLTUl1Ui9zakRZTGdXT1hJeHVlL1BpbVJhcVZnVUJISXZtNi84UjJFT0hibWMzQmZXR1FoSTFmMGlkRVFZMDlsT1IyREcvYmFnYzVYdkRhdDJ0VHVZb1ppd05uZkdtT0QiLCJtYWMiOiJhZWE2OTVhMGQ2MTJhYzlkOTc5Nzc1NTAwMTM5OWFkYWI4N2Y4MzkxNWUyM2VlMDQwNzA1ZTBlYjAxMDc0OTNjIiwidGFnIjoiIn0%3D\\\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-04 14:17:48', '2025-08-04 14:17:48')\\n-- \",\n    \"Time:\": 0.63\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.752591, "xdebug_link": null, "collector": "log"}, {"message": "[14:17:48] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `id` = '512' limit 1\\n-- \",\n    \"Time:\": 0.64\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.759237, "xdebug_link": null, "collector": "log"}, {"message": "[14:17:48] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `seos` where `key` = 'rec-dashboard' limit 1\\n-- \",\n    \"Time:\": 0.46\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.761548, "xdebug_link": null, "collector": "log"}, {"message": "[14:17:48] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `warehouse_cvs` where `user_id` = '512' and `is_active` = '1' and `is_real` = '1'\\n-- \",\n    \"Time:\": 65.7\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.830902, "xdebug_link": null, "collector": "log"}, {"message": "[14:17:48] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `submit_cvs` where `user_id` = '512' and `is_active` = '1'\\n-- \",\n    \"Time:\": 1.76\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.83842, "xdebug_link": null, "collector": "log"}, {"message": "[14:17:48] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `submit_cvs` where `user_id` = '512' and `status` = '5' and `is_active` = '1'\\n-- \",\n    \"Time:\": 1.6\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.842825, "xdebug_link": null, "collector": "log"}, {"message": "[14:17:48] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `submit_cvs` where `user_id` = '512' order by `created_at` desc limit 20\\n-- \",\n    \"Time:\": 2.03\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.847454, "xdebug_link": null, "collector": "log"}, {"message": "[14:17:48] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `warehouse_cvs` where `warehouse_cvs`.`id` in (565, 2847)\\n-- \",\n    \"Time:\": 0.54\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.854389, "xdebug_link": null, "collector": "log"}, {"message": "[14:17:48] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `companies` where `companies`.`id` in (77, 102, 286)\\n-- \",\n    \"Time:\": 0.57\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.858843, "xdebug_link": null, "collector": "log"}, {"message": "[14:17:48] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect `job`.* from `job` where `job`.`is_active` = '1' and `job`.`status` = '1' and `job`.`expire_at` >= '2025-08-04' order by `job`.`created_at` desc limit 5\\n-- \",\n    \"Time:\": 4.35\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.865742, "xdebug_link": null, "collector": "log"}, {"message": "[14:17:48] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect `id`, `name`, `slug` from `companies` where `companies`.`id` in (217, 286, 487, 491, 696)\\n-- \",\n    \"Time:\": 0.32\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.867732, "xdebug_link": null, "collector": "log"}, {"message": "[14:17:48] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect `job_id`, `priority` from `job_meta` where `job_meta`.`job_id` in (1640, 1641, 1642, 1643, 1644)\\n-- \",\n    \"Time:\": 0.77\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.871593, "xdebug_link": null, "collector": "log"}, {"message": "[14:17:48] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where `is_active` = '1' and `user_id` = '512' and `status` = '9' and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc\\n-- \",\n    \"Time:\": 1.83\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.877065, "xdebug_link": null, "collector": "log"}, {"message": "[14:17:48] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where `is_active` = '1' and `user_id` = '512' and `status` = '0' and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc\\n-- \",\n    \"Time:\": 1.74\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.88021, "xdebug_link": null, "collector": "log"}, {"message": "[14:17:48] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where `is_active` = '1' and `user_id` = '512' and `status` = '1' and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc\\n-- \",\n    \"Time:\": 1.84\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.883652, "xdebug_link": null, "collector": "log"}, {"message": "[14:17:48] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where `is_active` = '1' and `user_id` = '512' and `status` = '2' and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc\\n-- \",\n    \"Time:\": 1.79\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.888459, "xdebug_link": null, "collector": "log"}, {"message": "[14:17:48] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where `is_active` = '1' and `user_id` = '512' and `status` = '3' and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc\\n-- \",\n    \"Time:\": 1.73\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.89161, "xdebug_link": null, "collector": "log"}, {"message": "[14:17:48] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where `is_active` = '1' and `user_id` = '512' and `status` = '4' and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc\\n-- \",\n    \"Time:\": 1.75\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.894745, "xdebug_link": null, "collector": "log"}, {"message": "[14:17:48] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where `is_active` = '1' and `user_id` = '512' and `status` = '8' and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc\\n-- \",\n    \"Time:\": 1.76\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.897977, "xdebug_link": null, "collector": "log"}, {"message": "[14:17:48] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where `is_active` = '1' and `user_id` = '512' and `status` = '5' and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc\\n-- \",\n    \"Time:\": 1.75\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.901117, "xdebug_link": null, "collector": "log"}, {"message": "[14:17:48] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where `is_active` = '1' and `user_id` = '512' and `status` = '6' and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc\\n-- \",\n    \"Time:\": 1.74\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.904294, "xdebug_link": null, "collector": "log"}, {"message": "[14:17:48] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where `is_active` = '1' and `user_id` = '512' and `status` = '12' and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc\\n-- \",\n    \"Time:\": 1.76\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.907572, "xdebug_link": null, "collector": "log"}, {"message": "[14:17:49] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `bonus` where `bonus`.`user_id` = '512' and `bonus`.`user_id` is not null and `month` = '8' and `year` = '2025' limit 1\\n-- \",\n    \"Time:\": 35.67\n}", "message_html": null, "is_string": false, "label": "debug", "time": 1754291869.138256, "xdebug_link": null, "collector": "log"}, {"message": "[14:17:49] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `wallets` where `wallets`.`user_id` = '512' and `wallets`.`user_id` is not null limit 1\\n-- \",\n    \"Time:\": 3.52\n}", "message_html": null, "is_string": false, "label": "debug", "time": 1754291869.26579, "xdebug_link": null, "collector": "log"}, {"message": "[14:17:49] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `meta` where `meta`.`owner_type` = 'App\\\\Models\\\\User' and `meta`.`owner_id` = '512' and `meta`.`owner_id` is not null\\n-- \",\n    \"Time:\": 29.57\n}", "message_html": null, "is_string": false, "label": "debug", "time": 1754291869.452616, "xdebug_link": null, "collector": "log"}, {"message": "[14:17:50] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `notifications` where `notifications`.`notifiable_type` = 'App\\\\Models\\\\User' and `notifications`.`notifiable_id` = '512' and `notifications`.`notifiable_id` is not null order by `created_at` desc\\n-- \",\n    \"Time:\": 9.53\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.115984, "xdebug_link": null, "collector": "log"}, {"message": "[14:17:50] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `notifications` where `notifications`.`notifiable_type` = 'App\\\\Models\\\\User' and `notifications`.`notifiable_id` = '512' and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc\\n-- \",\n    \"Time:\": 0.65\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.13149, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 3, "start": 1754291867.889914, "end": **********.358946, "duration": 2.469032049179077, "duration_str": "2.47s", "measures": [{"label": "Booting", "start": 1754291867.889914, "relative_start": 0, "end": **********.247927, "relative_end": **********.247927, "duration": 0.****************, "duration_str": "358ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.247938, "relative_start": 0.*****************, "end": **********.35895, "relative_end": 3.814697265625e-06, "duration": 2.****************, "duration_str": "2.11s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.259036, "relative_start": 0.****************, "end": **********.263179, "relative_end": **********.263179, "duration": 0.0041429996490478516, "duration_str": "4.14ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "64MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 16, "nb_templates": 16, "templates": [{"name": "1x frontend.pages.collaborator.dashboard", "param_count": null, "params": [], "start": **********.918398, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/pages/collaborator/dashboard.blade.phpfrontend.pages.collaborator.dashboard", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Fpages%2Fcollaborator%2Fdashboard.blade.php&line=1", "ajax": false, "filename": "dashboard.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.pages.collaborator.dashboard"}, {"name": "1x frontend.layouts.collaborator.app", "param_count": null, "params": [], "start": 1754291869.16291, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/collaborator/app.blade.phpfrontend.layouts.collaborator.app", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Fcollaborator%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.layouts.collaborator.app"}, {"name": "2x frontend.layouts.user.avatar", "param_count": null, "params": [], "start": 1754291869.247755, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/user/avatar.blade.phpfrontend.layouts.user.avatar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Fuser%2Favatar.blade.php&line=1", "ajax": false, "filename": "avatar.blade.php", "line": "?"}, "render_count": 2, "name_original": "frontend.layouts.user.avatar"}, {"name": "2x frontend.layouts.collaborator.menu", "param_count": null, "params": [], "start": 1754291869.26695, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/collaborator/menu.blade.phpfrontend.layouts.collaborator.menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Fcollaborator%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}, "render_count": 2, "name_original": "frontend.layouts.collaborator.menu"}, {"name": "1x frontend.layouts.modal.collaborator_survey", "param_count": null, "params": [], "start": 1754291869.316319, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/modal/collaborator_survey.blade.phpfrontend.layouts.modal.collaborator_survey", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Fmodal%2Fcollaborator_survey.blade.php&line=1", "ajax": false, "filename": "collaborator_survey.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.layouts.modal.collaborator_survey"}, {"name": "1x frontend.layouts.login.app", "param_count": null, "params": [], "start": 1754291869.455813, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/login/app.blade.phpfrontend.layouts.login.app", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Flogin%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.layouts.login.app"}, {"name": "1x frontend.inc_layouts.v2.header_script", "param_count": null, "params": [], "start": 1754291869.457651, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/v2/header_script.blade.phpfrontend.inc_layouts.v2.header_script", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Fv2%2Fheader_script.blade.php&line=1", "ajax": false, "filename": "header_script.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.inc_layouts.v2.header_script"}, {"name": "1x frontend.inc_layouts.login.header", "param_count": null, "params": [], "start": 1754291869.458771, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/header.blade.phpfrontend.inc_layouts.login.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Flogin%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.inc_layouts.login.header"}, {"name": "2x frontend.inc_layouts.notification.drop-notification", "param_count": null, "params": [], "start": **********.132649, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/notification/drop-notification.blade.phpfrontend.inc_layouts.notification.drop-notification", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Fnotification%2Fdrop-notification.blade.php&line=1", "ajax": false, "filename": "drop-notification.blade.php", "line": "?"}, "render_count": 2, "name_original": "frontend.inc_layouts.notification.drop-notification"}, {"name": "1x frontend.inc_layouts.login.footer_v2", "param_count": null, "params": [], "start": **********.272094, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/footer_v2.blade.phpfrontend.inc_layouts.login.footer_v2", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Flogin%2Ffooter_v2.blade.php&line=1", "ajax": false, "filename": "footer_v2.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.inc_layouts.login.footer_v2"}, {"name": "1x frontend.inc_layouts.login.modal_report", "param_count": null, "params": [], "start": **********.273016, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/modal_report.blade.phpfrontend.inc_layouts.login.modal_report", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Flogin%2Fmodal_report.blade.php&line=1", "ajax": false, "filename": "modal_report.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.inc_layouts.login.modal_report"}, {"name": "1x frontend.partials.bug_report_modal", "param_count": null, "params": [], "start": **********.350186, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/partials/bug_report_modal.blade.phpfrontend.partials.bug_report_modal", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Fpartials%2Fbug_report_modal.blade.php&line=1", "ajax": false, "filename": "bug_report_modal.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.partials.bug_report_modal"}, {"name": "1x admin.inc_layouts.toast.message", "param_count": null, "params": [], "start": **********.353356, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/inc_layouts/toast/message.blade.phpadmin.inc_layouts.toast.message", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Finc_layouts%2Ftoast%2Fmessage.blade.php&line=1", "ajax": false, "filename": "message.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.inc_layouts.toast.message"}]}, "route": {"uri": "GET rec", "middleware": "web, localization, visit-website, check-rec", "controller": "App\\Http\\Controllers\\Frontend\\RecController@index<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FRecController.php&line=107\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Frontend", "prefix": "/rec", "as": "rec-dashboard", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FRecController.php&line=107\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/RecController.php:107-144</a>"}, "queries": {"count": 28, "nb_statements": 28, "nb_visible_statements": 28, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.19223, "accumulated_duration_str": "192ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `seos` where `key` = 'rec-dashboard' limit 1", "type": "query", "params": [], "bindings": ["rec-dashboard"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/SeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SeoRepository.php", "line": 14}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/SeoService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\SeoService.php", "line": 22}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/RecController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\RecController.php", "line": 104}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 929}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 770}], "start": **********.5414028, "duration": 0.01623, "duration_str": "16.23ms", "memory": 0, "memory_str": null, "filename": "SeoRepository.php:14", "source": {"index": 15, "namespace": null, "name": "app/Repositories/SeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SeoRepository.php", "line": 14}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSeoRepository.php&line=14", "ajax": false, "filename": "SeoRepository.php", "line": "14"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `seos` where `key` = ? limit 1", "hash": "9301d0afdeaf812ceb8408dafe279ff9ec408c37073ce4beab06d25d1f2f6326"}, "start_percent": 0, "width_percent": 8.443}, {"sql": "insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '{\\\"show_intro\\\":\\\"market\\\"}', 'http://recland.local/rec?show_intro=market', 'http://recland.local/login', '[\\\"en-us\\\",\\\"en\\\",\\\"vi\\\",\\\"nl\\\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\\\"host\\\":[\\\"recland.local\\\"],\\\"connection\\\":[\\\"keep-alive\\\"],\\\"cache-control\\\":[\\\"max-age=0\\\"],\\\"upgrade-insecure-requests\\\":[\\\"1\\\"],\\\"user-agent\\\":[\\\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\\\"],\\\"accept\\\":[\\\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\\\"],\\\"referer\\\":[\\\"http:\\/\\/recland.local\\/login\\\"],\\\"accept-encoding\\\":[\\\"gzip, deflate\\\"],\\\"accept-language\\\":[\\\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\\\"],\\\"cookie\\\":[\\\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkZrVnltWHJNS2ZzN242RStVUHVwWHc9PSIsInZhbHVlIjoiOTY1ZTRSSEE0WkM3aG9vTlpTOWF1TGNPUVA4WElQYUdVc0F5VGVLQnd4STg4TzQyWDJGSHQ4U2gvUDlOYzYyWnlTcyt4YlhJU21XcmdXT3AvUm4yT3cvdk1RSjZZYkk1THM1eTJNQkhqYVAyOUNRdG5HdW05NXJGbGovQWphaGgiLCJtYWMiOiJiZDVlYTk4OGQ3ODliYzJjZjRjZTIxNDkyMmFjNDUwYjBiOGJjOTkwNWE1ODNiMTdhMGUxYjg2Nzg0YWI4MzhkIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IkVRejFKaHhLTkQ5L2g4RU4rdlBLWUE9PSIsInZhbHVlIjoiaUMwNExYK1VYcWMrdHdEYUpacGdIR2t0SEhDWmxRZFZLTUl1Ui9zakRZTGdXT1hJeHVlL1BpbVJhcVZnVUJISXZtNi84UjJFT0hibWMzQmZXR1FoSTFmMGlkRVFZMDlsT1IyREcvYmFnYzVYdkRhdDJ0VHVZb1ppd05uZkdtT0QiLCJtYWMiOiJhZWE2OTVhMGQ2MTJhYzlkOTc5Nzc1NTAwMTM5OWFkYWI4N2Y4MzkxNWUyM2VlMDQwNzA1ZTBlYjAxMDc0OTNjIiwidGFnIjoiIn0%3D\\\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-04 14:17:48', '2025-08-04 14:17:48')", "type": "query", "params": [], "bindings": ["GET", "{\"show_intro\":\"market\"}", "http://recland.local/rec?show_intro=market", "http://recland.local/login", "[\"en-us\",\"en\",\"vi\",\"nl\"]", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"cache-control\":[\"max-age=0\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/login\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkZrVnltWHJNS2ZzN242RStVUHVwWHc9PSIsInZhbHVlIjoiOTY1ZTRSSEE0WkM3aG9vTlpTOWF1TGNPUVA4WElQYUdVc0F5VGVLQnd4STg4TzQyWDJGSHQ4U2gvUDlOYzYyWnlTcyt4YlhJU21XcmdXT3AvUm4yT3cvdk1RSjZZYkk1THM1eTJNQkhqYVAyOUNRdG5HdW05NXJGbGovQWphaGgiLCJtYWMiOiJiZDVlYTk4OGQ3ODliYzJjZjRjZTIxNDkyMmFjNDUwYjBiOGJjOTkwNWE1ODNiMTdhMGUxYjg2Nzg0YWI4MzhkIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IkVRejFKaHhLTkQ5L2g4RU4rdlBLWUE9PSIsInZhbHVlIjoiaUMwNExYK1VYcWMrdHdEYUpacGdIR2t0SEhDWmxRZFZLTUl1Ui9zakRZTGdXT1hJeHVlL1BpbVJhcVZnVUJISXZtNi84UjJFT0hibWMzQmZXR1FoSTFmMGlkRVFZMDlsT1IyREcvYmFnYzVYdkRhdDJ0VHVZb1ppd05uZkdtT0QiLCJtYWMiOiJhZWE2OTVhMGQ2MTJhYzlkOTc5Nzc1NTAwMTM5OWFkYWI4N2Y4MzkxNWUyM2VlMDQwNzA1ZTBlYjAxMDc0OTNjIiwidGFnIjoiIn0%3D\"]}", "WebKit", "Windows", "Edge", "127.0.0.1", null, null, "2025-08-04 14:17:48", "2025-08-04 14:17:48"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/shetabit/visitor/src/Visitor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\shetabit\\visitor\\src\\Visitor.php", "line": 245}, {"index": 22, "namespace": "middleware", "name": "visit-website", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\VisitWebsiteMiddleware.php", "line": 20}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 24, "namespace": "middleware", "name": "localization", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\Localization.php", "line": 24}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.7520442, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Visitor.php:245", "source": {"index": 21, "namespace": null, "name": "vendor/shetabit/visitor/src/Visitor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\shetabit\\visitor\\src\\Visitor.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fshetabit%2Fvisitor%2Fsrc%2FVisitor.php&line=245", "ajax": false, "filename": "Visitor.php", "line": "245"}, "connection": "hri_recland_product", "explain": null, "start_percent": 8.443, "width_percent": 0.328}, {"sql": "select * from `users` where `id` = 512 limit 1", "type": "query", "params": [], "bindings": [512], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "check-rec", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckRec.php", "line": 21}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.7586682, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `id` = ? limit 1", "hash": "f57aeccc1e0beb29028e5e2a4241e4eed6d22790637d6f4352f9bde1e64d2f30"}, "start_percent": 8.771, "width_percent": 0.333}, {"sql": "select * from `seos` where `key` = 'rec-dashboard' limit 1", "type": "query", "params": [], "bindings": ["rec-dashboard"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/SeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SeoRepository.php", "line": 14}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/SeoService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\SeoService.php", "line": 22}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/RecController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\RecController.php", "line": 110}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.7611592, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "SeoRepository.php:14", "source": {"index": 15, "namespace": null, "name": "app/Repositories/SeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SeoRepository.php", "line": 14}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSeoRepository.php&line=14", "ajax": false, "filename": "SeoRepository.php", "line": "14"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `seos` where `key` = ? limit 1", "hash": "9301d0afdeaf812ceb8408dafe279ff9ec408c37073ce4beab06d25d1f2f6326"}, "start_percent": 9.104, "width_percent": 0.239}, {"sql": "select count(*) as aggregate from `warehouse_cvs` where `user_id` = 512 and `is_active` = 1 and `is_real` = 1", "type": "query", "params": [], "bindings": [512, 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/WareHouseCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\WareHouseCvRepository.php", "line": 73}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/WareHouseSubmitCvService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WareHouseSubmitCvService.php", "line": 616}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/RecController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\RecController.php", "line": 111}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.765393, "duration": 0.*****************, "duration_str": "65.7ms", "memory": 0, "memory_str": null, "filename": "WareHouseCvRepository.php:73", "source": {"index": 15, "namespace": null, "name": "app/Repositories/WareHouseCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\WareHouseCvRepository.php", "line": 73}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FWareHouseCvRepository.php&line=73", "ajax": false, "filename": "WareHouseCvRepository.php", "line": "73"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `warehouse_cvs` where `user_id` = ? and `is_active` = ? and `is_real` = ?", "hash": "29a1afdd4d662f200116eedfebd63d019196b26c87df80745d12578c9ac37371"}, "start_percent": 9.343, "width_percent": 34.178}, {"sql": "select count(*) as aggregate from `submit_cvs` where `user_id` = 512 and `is_active` = 1", "type": "query", "params": [], "bindings": [512, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 75}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/WareHouseSubmitCvService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WareHouseSubmitCvService.php", "line": 627}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/RecController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\RecController.php", "line": 112}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.83681, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "SubmitCvRepository.php:75", "source": {"index": 15, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSubmitCvRepository.php&line=75", "ajax": false, "filename": "SubmitCvRepository.php", "line": "75"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `submit_cvs` where `user_id` = ? and `is_active` = ?", "hash": "31529b8215a9a4d79d0a2dfa23e808674633f490ed4ffcbf3b12ab7e61c00bff"}, "start_percent": 43.521, "width_percent": 0.916}, {"sql": "select count(*) as aggregate from `submit_cvs` where `user_id` = 512 and `status` = 5 and `is_active` = 1", "type": "query", "params": [], "bindings": [512, 5, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 75}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/WareHouseSubmitCvService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WareHouseSubmitCvService.php", "line": 627}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/RecController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\RecController.php", "line": 115}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.841366, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "SubmitCvRepository.php:75", "source": {"index": 15, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSubmitCvRepository.php&line=75", "ajax": false, "filename": "SubmitCvRepository.php", "line": "75"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `submit_cvs` where `user_id` = ? and `status` = ? and `is_active` = ?", "hash": "38616f46488db66fb4bdb1b9722c4a4bed3afbee3d2f474a38d9b3a06ac4f876"}, "start_percent": 44.436, "width_percent": 0.832}, {"sql": "select * from `submit_cvs` where `user_id` = 512 order by `created_at` desc limit 20", "type": "query", "params": [], "bindings": [512], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 38}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/WareHouseSubmitCvService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WareHouseSubmitCvService.php", "line": 639}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/RecController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\RecController.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8455632, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "SubmitCvRepository.php:38", "source": {"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSubmitCvRepository.php&line=38", "ajax": false, "filename": "SubmitCvRepository.php", "line": "38"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `submit_cvs` where `user_id` = ? order by `created_at` desc limit 20", "hash": "c5ea7fa066e1e5c20fb134167bb216bf012534c168a55107037c404d1e6532c5"}, "start_percent": 45.269, "width_percent": 1.056}, {"sql": "select * from `warehouse_cvs` where `warehouse_cvs`.`id` in (565, 2847)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Services/Frontend/WareHouseSubmitCvService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WareHouseSubmitCvService.php", "line": 641}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/RecController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\RecController.php", "line": 116}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.854002, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "WareHouseSubmitCvService.php:641", "source": {"index": 19, "namespace": null, "name": "app/Services/Frontend/WareHouseSubmitCvService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WareHouseSubmitCvService.php", "line": 641}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FServices%2FFrontend%2FWareHouseSubmitCvService.php&line=641", "ajax": false, "filename": "WareHouseSubmitCvService.php", "line": "641"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `warehouse_cvs` where `warehouse_cvs`.`id` in (565, 2847)", "hash": "931208ff2727de2e7e7229171af3b8c1b72fb6f3d8c1a8476ef39965fc8a9893"}, "start_percent": 46.325, "width_percent": 0.281}, {"sql": "select * from `companies` where `companies`.`id` in (77, 102, 286)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Services/Frontend/WareHouseSubmitCvService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WareHouseSubmitCvService.php", "line": 641}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/RecController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\RecController.php", "line": 116}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.858424, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "WareHouseSubmitCvService.php:641", "source": {"index": 19, "namespace": null, "name": "app/Services/Frontend/WareHouseSubmitCvService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WareHouseSubmitCvService.php", "line": 641}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FServices%2FFrontend%2FWareHouseSubmitCvService.php&line=641", "ajax": false, "filename": "WareHouseSubmitCvService.php", "line": "641"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `companies` where `companies`.`id` in (77, 102, 286)", "hash": "42f2c7605fdba33be9be12929ef118a4d9b62820c5950f91371eb0d21cb6f67d"}, "start_percent": 46.606, "width_percent": 0.297}, {"sql": "select `job`.* from `job` where `job`.`is_active` = 1 and `job`.`status` = 1 and `job`.`expire_at` >= '2025-08-04' order by `job`.`created_at` desc limit 5", "type": "query", "params": [], "bindings": [1, 1, "2025-08-04"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/JobRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobRepository.php", "line": 458}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/JobService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\JobService.php", "line": 107}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/RecController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\RecController.php", "line": 119}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.861463, "duration": 0.00435, "duration_str": "4.35ms", "memory": 0, "memory_str": null, "filename": "JobRepository.php:458", "source": {"index": 14, "namespace": null, "name": "app/Repositories/JobRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobRepository.php", "line": 458}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FJobRepository.php&line=458", "ajax": false, "filename": "JobRepository.php", "line": "458"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select `job`.* from `job` where `job`.`is_active` = ? and `job`.`status` = ? and `job`.`expire_at` >= ? order by `job`.`created_at` desc limit 5", "hash": "1690fa226adc9e4f95e425ac8451fae3a0650ce1f92f3107fb4b112e9bbf3fa7"}, "start_percent": 46.902, "width_percent": 2.263}, {"sql": "select `id`, `name`, `slug` from `companies` where `companies`.`id` in (217, 286, 487, 491, 696)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Repositories/JobRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobRepository.php", "line": 458}, {"index": 20, "namespace": null, "name": "app/Services/Frontend/JobService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\JobService.php", "line": 107}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/RecController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\RecController.php", "line": 119}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.867481, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "JobRepository.php:458", "source": {"index": 19, "namespace": null, "name": "app/Repositories/JobRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobRepository.php", "line": 458}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FJobRepository.php&line=458", "ajax": false, "filename": "JobRepository.php", "line": "458"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select `id`, `name`, `slug` from `companies` where `companies`.`id` in (217, 286, 487, 491, 696)", "hash": "081f36538c6bb65c737f7b485d3f06a4aa0d189ab9d9a7451e65d7fa1c16df7e"}, "start_percent": 49.165, "width_percent": 0.166}, {"sql": "select `job_id`, `priority` from `job_meta` where `job_meta`.`job_id` in (1640, 1641, 1642, 1643, 1644)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Repositories/JobRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobRepository.php", "line": 458}, {"index": 20, "namespace": null, "name": "app/Services/Frontend/JobService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\JobService.php", "line": 107}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/RecController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\RecController.php", "line": 119}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8709838, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "JobRepository.php:458", "source": {"index": 19, "namespace": null, "name": "app/Repositories/JobRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobRepository.php", "line": 458}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FJobRepository.php&line=458", "ajax": false, "filename": "JobRepository.php", "line": "458"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select `job_id`, `priority` from `job_meta` where `job_meta`.`job_id` in (1640, 1641, 1642, 1643, 1644)", "hash": "e9c85096b5bdc0468d2deee66a77f2c557de1b5ebbf964be9829d497207d086d"}, "start_percent": 49.332, "width_percent": 0.401}, {"sql": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where `is_active` = 1 and `user_id` = 512 and `status` = 9 and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "type": "query", "params": [], "bindings": [1, 512, 9, "2025-08-01 00:00:00", "2025-08-31 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 95}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/WareHouseSubmitCvService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WareHouseSubmitCvService.php", "line": 670}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/RecController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\RecController.php", "line": 128}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8753078, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "SubmitCvRepository.php:95", "source": {"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 95}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSubmitCvRepository.php&line=95", "ajax": false, "filename": "SubmitCvRepository.php", "line": "95"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where `is_active` = ? and `user_id` = ? and `status` = ? and `created_at` >= ? and `created_at` <= ? group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "hash": "798d58df479de7977290e737ae689cba5567e8d49e409899b95b8e5b8f0dcf65"}, "start_percent": 49.732, "width_percent": 0.952}, {"sql": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where `is_active` = 1 and `user_id` = 512 and `status` = 0 and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "type": "query", "params": [], "bindings": [1, 512, 0, "2025-08-01 00:00:00", "2025-08-31 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 95}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/WareHouseSubmitCvService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WareHouseSubmitCvService.php", "line": 670}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/RecController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\RecController.php", "line": 128}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.87854, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "SubmitCvRepository.php:95", "source": {"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 95}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSubmitCvRepository.php&line=95", "ajax": false, "filename": "SubmitCvRepository.php", "line": "95"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where `is_active` = ? and `user_id` = ? and `status` = ? and `created_at` >= ? and `created_at` <= ? group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "hash": "6c3cc50b763bfee0229a3188b94170064018feb4574bdc04f9ded836c0075e01"}, "start_percent": 50.684, "width_percent": 0.905}, {"sql": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where `is_active` = 1 and `user_id` = 512 and `status` = 1 and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "type": "query", "params": [], "bindings": [1, 512, 1, "2025-08-01 00:00:00", "2025-08-31 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 95}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/WareHouseSubmitCvService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WareHouseSubmitCvService.php", "line": 670}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/RecController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\RecController.php", "line": 128}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8819768, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "SubmitCvRepository.php:95", "source": {"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 95}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSubmitCvRepository.php&line=95", "ajax": false, "filename": "SubmitCvRepository.php", "line": "95"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where `is_active` = ? and `user_id` = ? and `status` = ? and `created_at` >= ? and `created_at` <= ? group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "hash": "8820c5254916b37773c31181682e0460918f3e8e55541dd32df4ec58ac065273"}, "start_percent": 51.589, "width_percent": 0.957}, {"sql": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where `is_active` = 1 and `user_id` = 512 and `status` = 2 and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "type": "query", "params": [], "bindings": [1, 512, 2, "2025-08-01 00:00:00", "2025-08-31 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 95}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/WareHouseSubmitCvService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WareHouseSubmitCvService.php", "line": 670}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/RecController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\RecController.php", "line": 128}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.88674, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "SubmitCvRepository.php:95", "source": {"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 95}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSubmitCvRepository.php&line=95", "ajax": false, "filename": "SubmitCvRepository.php", "line": "95"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where `is_active` = ? and `user_id` = ? and `status` = ? and `created_at` >= ? and `created_at` <= ? group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "hash": "78d86505cc0c79f07572f121d013e27bf3c1b05fa5247c981edbfae076d6b1e8"}, "start_percent": 52.546, "width_percent": 0.931}, {"sql": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where `is_active` = 1 and `user_id` = 512 and `status` = 3 and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "type": "query", "params": [], "bindings": [1, 512, 3, "2025-08-01 00:00:00", "2025-08-31 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 95}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/WareHouseSubmitCvService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WareHouseSubmitCvService.php", "line": 670}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/RecController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\RecController.php", "line": 128}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.889949, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "SubmitCvRepository.php:95", "source": {"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 95}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSubmitCvRepository.php&line=95", "ajax": false, "filename": "SubmitCvRepository.php", "line": "95"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where `is_active` = ? and `user_id` = ? and `status` = ? and `created_at` >= ? and `created_at` <= ? group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "hash": "419e886587a8524145e2a08c94a1075b3e12a5cb334ebd82e52c05dfd06e9eef"}, "start_percent": 53.478, "width_percent": 0.9}, {"sql": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where `is_active` = 1 and `user_id` = 512 and `status` = 4 and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "type": "query", "params": [], "bindings": [1, 512, 4, "2025-08-01 00:00:00", "2025-08-31 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 95}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/WareHouseSubmitCvService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WareHouseSubmitCvService.php", "line": 670}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/RecController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\RecController.php", "line": 128}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.893066, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "SubmitCvRepository.php:95", "source": {"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 95}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSubmitCvRepository.php&line=95", "ajax": false, "filename": "SubmitCvRepository.php", "line": "95"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where `is_active` = ? and `user_id` = ? and `status` = ? and `created_at` >= ? and `created_at` <= ? group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "hash": "eb1bcd7bdf642fa328f267b0688906b01c41e0d82bad8a046f8c1a6910c6a974"}, "start_percent": 54.378, "width_percent": 0.91}, {"sql": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where `is_active` = 1 and `user_id` = 512 and `status` = 8 and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "type": "query", "params": [], "bindings": [1, 512, 8, "2025-08-01 00:00:00", "2025-08-31 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 95}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/WareHouseSubmitCvService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WareHouseSubmitCvService.php", "line": 670}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/RecController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\RecController.php", "line": 128}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.896287, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "SubmitCvRepository.php:95", "source": {"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 95}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSubmitCvRepository.php&line=95", "ajax": false, "filename": "SubmitCvRepository.php", "line": "95"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where `is_active` = ? and `user_id` = ? and `status` = ? and `created_at` >= ? and `created_at` <= ? group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "hash": "b62f3f5abc3772b8964360308aa6f7d379f21bed024a4369989b805acb91d1f4"}, "start_percent": 55.288, "width_percent": 0.916}, {"sql": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where `is_active` = 1 and `user_id` = 512 and `status` = 5 and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "type": "query", "params": [], "bindings": [1, 512, 5, "2025-08-01 00:00:00", "2025-08-31 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 95}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/WareHouseSubmitCvService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WareHouseSubmitCvService.php", "line": 670}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/RecController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\RecController.php", "line": 128}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.899436, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "SubmitCvRepository.php:95", "source": {"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 95}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSubmitCvRepository.php&line=95", "ajax": false, "filename": "SubmitCvRepository.php", "line": "95"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where `is_active` = ? and `user_id` = ? and `status` = ? and `created_at` >= ? and `created_at` <= ? group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "hash": "d3dd047719cf005327fff457f4ea726031825318ef3138877123abcc80ea5946"}, "start_percent": 56.204, "width_percent": 0.91}, {"sql": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where `is_active` = 1 and `user_id` = 512 and `status` = 6 and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "type": "query", "params": [], "bindings": [1, 512, 6, "2025-08-01 00:00:00", "2025-08-31 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 95}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/WareHouseSubmitCvService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WareHouseSubmitCvService.php", "line": 670}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/RecController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\RecController.php", "line": 128}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.902635, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "SubmitCvRepository.php:95", "source": {"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 95}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSubmitCvRepository.php&line=95", "ajax": false, "filename": "SubmitCvRepository.php", "line": "95"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where `is_active` = ? and `user_id` = ? and `status` = ? and `created_at` >= ? and `created_at` <= ? group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "hash": "bbfbed52517c3881f4e0f08212e1cb185126c5dbbffbeb1450023d2fb9abb500"}, "start_percent": 57.114, "width_percent": 0.905}, {"sql": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where `is_active` = 1 and `user_id` = 512 and `status` = 12 and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "type": "query", "params": [], "bindings": [1, 512, 12, "2025-08-01 00:00:00", "2025-08-31 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 95}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/WareHouseSubmitCvService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WareHouseSubmitCvService.php", "line": 670}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/RecController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\RecController.php", "line": 128}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.905885, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "SubmitCvRepository.php:95", "source": {"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 95}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSubmitCvRepository.php&line=95", "ajax": false, "filename": "SubmitCvRepository.php", "line": "95"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where `is_active` = ? and `user_id` = ? and `status` = ? and `created_at` >= ? and `created_at` <= ? group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "hash": "b1d5f73715eca0af450587e0dc5debf989ba99ba2110182d69220928767b31d4"}, "start_percent": 58.019, "width_percent": 0.916}, {"sql": "select * from `bonus` where `bonus`.`user_id` = 512 and `bonus`.`user_id` is not null and `month` = 8 and `year` = 2025 limit 1", "type": "query", "params": [], "bindings": [512, 8, 2025], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Models\\User.php", "line": 132}, {"index": 27, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 28, "namespace": "view", "name": "frontend.pages.collaborator.dashboard", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/pages/collaborator/dashboard.blade.php", "line": 67}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1754291869.102778, "duration": 0.03567, "duration_str": "35.67ms", "memory": 0, "memory_str": null, "filename": "User.php:132", "source": {"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Models\\User.php", "line": 132}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FUser.php&line=132", "ajax": false, "filename": "User.php", "line": "132"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `bonus` where `bonus`.`user_id` = ? and `bonus`.`user_id` is not null and `month` = ? and `year` = ? limit 1", "hash": "6e6f709a0d95ff99eea810b3ed77926c528121afd9b54912ecfcc7e816c99795"}, "start_percent": 58.935, "width_percent": 18.556}, {"sql": "select * from `wallets` where `wallets`.`user_id` = 512 and `wallets`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [512], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 21, "namespace": "view", "name": "frontend.layouts.collaborator.app", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/collaborator/app.blade.php", "line": 17}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": 1754291869.262351, "duration": 0.00352, "duration_str": "3.52ms", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `wallets` where `wallets`.`user_id` = ? and `wallets`.`user_id` is not null limit 1", "hash": "18921aae726f612c9bedcf556fdf1d2c6d2afc74ce1f207a81152bd383f78462"}, "start_percent": 77.491, "width_percent": 1.831}, {"sql": "select * from `meta` where `meta`.`owner_type` = 'App\\Models\\User' and `meta`.`owner_id` = 512 and `meta`.`owner_id` is not null", "type": "query", "params": [], "bindings": ["App\\Models\\User", 512], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 118}, {"index": 21, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/GetMeta.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\GetMeta.php", "line": 24}, {"index": 22, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 163}, {"index": 23, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 54}], "start": 1754291869.42323, "duration": 0.02957, "duration_str": "29.57ms", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 19, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `meta` where `meta`.`owner_type` = ? and `meta`.`owner_id` = ? and `meta`.`owner_id` is not null", "hash": "637cce98cd7bf57dd876d579837b6e71f1f6531874425d0665e4a96fdd51bcdd"}, "start_percent": 79.322, "width_percent": 15.383}, {"sql": "select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = 512 and `notifications`.`notifiable_id` is not null order by `created_at` desc", "type": "query", "params": [], "bindings": ["App\\Models\\User", 512], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "frontend.inc_layouts.login.header", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/header.blade.php", "line": 98}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.1065469, "duration": 0.009529999999999999, "duration_str": "9.53ms", "memory": 0, "memory_str": null, "filename": "frontend.inc_layouts.login.header:98", "source": {"index": 20, "namespace": "view", "name": "frontend.inc_layouts.login.header", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/header.blade.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Flogin%2Fheader.blade.php&line=98", "ajax": false, "filename": "header.blade.php", "line": "98"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `notifications` where `notifications`.`notifiable_type` = ? and `notifications`.`notifiable_id` = ? and `notifications`.`notifiable_id` is not null order by `created_at` desc", "hash": "cd6e0cac1208e1fa9f47a57d7e9fc29637e1a16b336edbcb38d6c43a0124575a"}, "start_percent": 94.704, "width_percent": 4.958}, {"sql": "select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = 512 and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["App\\Models\\User", 512], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "frontend.inc_layouts.login.header", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/header.blade.php", "line": 99}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.1309102, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "frontend.inc_layouts.login.header:99", "source": {"index": 20, "namespace": "view", "name": "frontend.inc_layouts.login.header", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/header.blade.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Flogin%2Fheader.blade.php&line=99", "ajax": false, "filename": "header.blade.php", "line": "99"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `notifications` where `notifications`.`notifiable_type` = ? and `notifications`.`notifiable_id` = ? and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc", "hash": "06715f653aa30b7ea62b1280b8e80d9ad410ede326edb45152870df64dff1b2b"}, "start_percent": 99.662, "width_percent": 0.338}]}, "models": {"data": {"App\\Models\\Company": {"retrieved": 8, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FCompany.php&line=1", "ajax": false, "filename": "Company.php", "line": "?"}}, "App\\Models\\Job": {"retrieved": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FJob.php&line=1", "ajax": false, "filename": "Job.php", "line": "?"}}, "App\\Models\\JobMeta": {"retrieved": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FJobMeta.php&line=1", "ajax": false, "filename": "JobMeta.php", "line": "?"}}, "App\\Models\\SubmitCv": {"retrieved": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FSubmitCv.php&line=1", "ajax": false, "filename": "SubmitCv.php", "line": "?"}}, "App\\Models\\Seo": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FSeo.php&line=1", "ajax": false, "filename": "Seo.php", "line": "?"}}, "Illuminate\\Notifications\\DatabaseNotification": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FNotifications%2FDatabaseNotification.php&line=1", "ajax": false, "filename": "DatabaseNotification.php", "line": "?"}}, "Shetabit\\Visitor\\Models\\Visit": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fshetabit%2Fvisitor%2Fsrc%2FModels%2FVisit.php&line=1", "ajax": false, "filename": "Visit.php", "line": "?"}}, "App\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\WareHouseCv": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FWareHouseCv.php&line=1", "ajax": false, "filename": "WareHouseCv.php", "line": "?"}}, "App\\Models\\Wallet": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FWallet.php&line=1", "ajax": false, "filename": "Wallet.php", "line": "?"}}, "Zoha\\Meta\\Models\\Meta": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FModels%2FMeta.php&line=1", "ajax": false, "filename": "Meta.php", "line": "?"}}}, "count": 30, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 29, "created": 1}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "67FrIiAKRzqdDDYE9lpf1v63S50ahQba2svcMgmp", "_previous": "array:1 [\n  \"url\" => \"http://recland.local/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "PHPDEBUGBAR_STACK_DATA": "array:1 [\n  \"01K1SZ346V5SG8GF6AJCV3V5Q9\" => null\n]", "login_client_59ba36addc2b2f9401580f014c7f58ea4e30989d": "512"}, "request": {"data": {"status": "200 OK", "full_url": "http://recland.local/rec?show_intro=market", "action_name": "rec-dashboard", "controller_action": "App\\Http\\Controllers\\Frontend\\RecController@index", "uri": "GET rec", "controller": "App\\Http\\Controllers\\Frontend\\RecController@index<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FRecController.php&line=107\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Frontend", "prefix": "/rec", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FRecController.php&line=107\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/RecController.php:107-144</a>", "middleware": "web, localization, visit-website, check-rec", "duration": "2.48s", "peak_memory": "66MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1780591286 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>show_intro</span>\" => \"<span class=sf-dump-str title=\"6 characters\">market</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1780591286\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-355015382 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-355015382\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://recland.local/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,vi;q=0.8,nl;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1257 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkZrVnltWHJNS2ZzN242RStVUHVwWHc9PSIsInZhbHVlIjoiOTY1ZTRSSEE0WkM3aG9vTlpTOWF1TGNPUVA4WElQYUdVc0F5VGVLQnd4STg4TzQyWDJGSHQ4U2gvUDlOYzYyWnlTcyt4YlhJU21XcmdXT3AvUm4yT3cvdk1RSjZZYkk1THM1eTJNQkhqYVAyOUNRdG5HdW05NXJGbGovQWphaGgiLCJtYWMiOiJiZDVlYTk4OGQ3ODliYzJjZjRjZTIxNDkyMmFjNDUwYjBiOGJjOTkwNWE1ODNiMTdhMGUxYjg2Nzg0YWI4MzhkIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IkVRejFKaHhLTkQ5L2g4RU4rdlBLWUE9PSIsInZhbHVlIjoiaUMwNExYK1VYcWMrdHdEYUpacGdIR2t0SEhDWmxRZFZLTUl1Ui9zakRZTGdXT1hJeHVlL1BpbVJhcVZnVUJISXZtNi84UjJFT0hibWMzQmZXR1FoSTFmMGlkRVFZMDlsT1IyREcvYmFnYzVYdkRhdDJ0VHVZb1ppd05uZkdtT0QiLCJtYWMiOiJhZWE2OTVhMGQ2MTJhYzlkOTc5Nzc1NTAwMTM5OWFkYWI4N2Y4MzkxNWUyM2VlMDQwNzA1ZTBlYjAxMDc0OTNjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-96399047 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|cm8f4SgFu8wrAd7wPR9b0gsQwQ8YYGlGSWS8Y9hgKQXIsj7qT8VG3KwC3dMx|$2y$10$iTSniY9ReMSnjm6GaX0bRuGWPNsyk9pQat6r8dKHdyoQSgwMkVojS</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">67FrIiAKRzqdDDYE9lpf1v63S50ahQba2svcMgmp</span>\"\n  \"<span class=sf-dump-key>recland_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1VyD9s5I8kaKasZMxJDemZkFPxfq8PRabbTbE7MT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-96399047\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1665156878 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 04 Aug 2025 07:17:48 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1665156878\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-169083758 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">67FrIiAKRzqdDDYE9lpf1v63S50ahQba2svcMgmp</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://recland.local/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01K1SZ346V5SG8GF6AJCV3V5Q9</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>login_client_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>512</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-169083758\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://recland.local/rec?show_intro=market", "action_name": "rec-dashboard", "controller_action": "App\\Http\\Controllers\\Frontend\\RecController@index"}, "badge": null}}