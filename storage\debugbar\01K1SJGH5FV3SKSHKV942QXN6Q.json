{"__meta": {"id": "01K1SJGH5FV3SKSHKV942QXN6Q", "datetime": "2025-08-04 10:37:55", "utime": **********.632693, "method": "GET", "uri": "/admin/dashboard", "ip": "127.0.0.1"}, "php": {"version": "8.1.6", "interface": "apache2handler"}, "messages": {"count": 41, "messages": [{"message": "[10:37:55] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `id` = '1' limit 1\\n-- \",\n    \"Time:\": 19.24\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.347542, "xdebug_link": null, "collector": "log"}, {"message": "[10:37:55] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)\\n-- \",\n    \"Time:\": 0.64\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.356385, "xdebug_link": null, "collector": "log"}, {"message": "[10:37:55] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `users` where `type` = 'employer' and `is_active` = '1' and `is_real` = '1'\\n-- \",\n    \"Time:\": 13.67\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.372357, "xdebug_link": null, "collector": "log"}, {"message": "[10:37:55] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `users` where `type` = 'rec' and `is_active` = '1' and `is_real` = '1'\\n-- \",\n    \"Time:\": 8.46\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.382191, "xdebug_link": null, "collector": "log"}, {"message": "[10:37:55] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `warehouse_cvs` where `is_active` = '1' and `is_real` = '1'\\n-- \",\n    \"Time:\": 39.72\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.423861, "xdebug_link": null, "collector": "log"}, {"message": "[10:37:55] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `job` where `is_active` = '1' and `is_real` = '1'\\n-- \",\n    \"Time:\": 6.59\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.43273, "xdebug_link": null, "collector": "log"}, {"message": "[10:37:55] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `companies` where `is_active` = '1' and `is_real` = '1'\\n-- \",\n    \"Time:\": 1.78\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.436238, "xdebug_link": null, "collector": "log"}, {"message": "[10:37:55] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect COUNT(id) as total, DATE_FORMAT(created_at, \\\"%m\\\") as month from `warehouse_cvs` where `is_active` = '1' and `is_real` = '1' and `created_at` between '2025-01-01 00:00:00' and '2025-12-31 23:59:59' group by DATE_FORMAT(created_at, \\\"%m\\\") order by DATE_FORMAT(created_at, \\\"%m\\\")\\n-- \",\n    \"Time:\": 48.67\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.486959, "xdebug_link": null, "collector": "log"}, {"message": "[10:37:55] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect (count(id)) as total, (DATE_FORMAT(created_at, '%m')) as month from `users` where `type` = 'rec' and `is_active` = '1' and `is_real` = '1' and `created_at` >= '2025-01-01 00:00:00' and `created_at` <= '2025-12-31 23:59:59' group by DATE_FORMAT(created_at, '%m') order by DATE_FORMAT(created_at, '%m') asc\\n-- \",\n    \"Time:\": 9.11\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.502792, "xdebug_link": null, "collector": "log"}, {"message": "[10:37:55] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect COUNT(id) as total, DATE_FORMAT(created_at, \\\"%m\\\") as month from `warehouse_cv_sellings` where `status` = '0' and `is_real` = '1' and `created_at` between '2025-01-01 00:00:00' and '2025-12-31 23:59:59' and `warehouse_cv_sellings`.`deleted_at` is null group by DATE_FORMAT(created_at, \\\"%m\\\") order by DATE_FORMAT(created_at, \\\"%m\\\")\\n-- \",\n    \"Time:\": 51.17\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.563649, "xdebug_link": null, "collector": "log"}, {"message": "[10:37:55] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php on line 138", "message_html": null, "is_string": false, "label": "warning", "time": **********.577988, "xdebug_link": null, "collector": "log"}, {"message": "[10:37:55] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php on line 138", "message_html": null, "is_string": false, "label": "warning", "time": **********.578913, "xdebug_link": null, "collector": "log"}, {"message": "[10:37:55] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php on line 138", "message_html": null, "is_string": false, "label": "warning", "time": **********.579722, "xdebug_link": null, "collector": "log"}, {"message": "[10:37:55] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php on line 138", "message_html": null, "is_string": false, "label": "warning", "time": **********.580249, "xdebug_link": null, "collector": "log"}, {"message": "[10:37:55] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php on line 138", "message_html": null, "is_string": false, "label": "warning", "time": **********.58059, "xdebug_link": null, "collector": "log"}, {"message": "[10:37:55] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php on line 138", "message_html": null, "is_string": false, "label": "warning", "time": **********.580927, "xdebug_link": null, "collector": "log"}, {"message": "[10:37:55] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php on line 138", "message_html": null, "is_string": false, "label": "warning", "time": **********.58126, "xdebug_link": null, "collector": "log"}, {"message": "[10:37:55] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect COUNT(submit_cvs.id) as total, DATE_FORMAT(submit_cvs.created_at, \\\"%m\\\") as month from `submit_cvs` inner join `job` on `submit_cvs`.`job_id` = `job`.`id` where `submit_cvs`.`is_active` = '1' and `job`.`is_real` = '1' and `submit_cvs`.`created_at` between '2025-01-01 00:00:00' and '2025-12-31 23:59:59' group by DATE_FORMAT(submit_cvs.created_at, \\\"%m\\\") order by DATE_FORMAT(submit_cvs.created_at, \\\"%m\\\")\\n-- \",\n    \"Time:\": 9.1\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.592112, "xdebug_link": null, "collector": "log"}, {"message": "[10:37:55] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in D:\\Projects\\HRI\\RecLand\\app\\Models\\SubmitCv.php on line 354", "message_html": null, "is_string": false, "label": "warning", "time": **********.59396, "xdebug_link": null, "collector": "log"}, {"message": "[10:37:55] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in D:\\Projects\\HRI\\RecLand\\app\\Models\\SubmitCv.php on line 349", "message_html": null, "is_string": false, "label": "warning", "time": **********.594088, "xdebug_link": null, "collector": "log"}, {"message": "[10:37:55] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in D:\\Projects\\HRI\\RecLand\\app\\Models\\SubmitCv.php on line 377", "message_html": null, "is_string": false, "label": "warning", "time": **********.594231, "xdebug_link": null, "collector": "log"}, {"message": "[10:37:55] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in D:\\Projects\\HRI\\RecLand\\app\\Models\\SubmitCv.php on line 354", "message_html": null, "is_string": false, "label": "warning", "time": **********.594426, "xdebug_link": null, "collector": "log"}, {"message": "[10:37:55] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in D:\\Projects\\HRI\\RecLand\\app\\Models\\SubmitCv.php on line 349", "message_html": null, "is_string": false, "label": "warning", "time": **********.594525, "xdebug_link": null, "collector": "log"}, {"message": "[10:37:55] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in D:\\Projects\\HRI\\RecLand\\app\\Models\\SubmitCv.php on line 377", "message_html": null, "is_string": false, "label": "warning", "time": **********.594638, "xdebug_link": null, "collector": "log"}, {"message": "[10:37:55] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in D:\\Projects\\HRI\\RecLand\\app\\Models\\SubmitCv.php on line 354", "message_html": null, "is_string": false, "label": "warning", "time": **********.594792, "xdebug_link": null, "collector": "log"}, {"message": "[10:37:55] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in D:\\Projects\\HRI\\RecLand\\app\\Models\\SubmitCv.php on line 349", "message_html": null, "is_string": false, "label": "warning", "time": **********.594888, "xdebug_link": null, "collector": "log"}, {"message": "[10:37:55] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in D:\\Projects\\HRI\\RecLand\\app\\Models\\SubmitCv.php on line 377", "message_html": null, "is_string": false, "label": "warning", "time": **********.594996, "xdebug_link": null, "collector": "log"}, {"message": "[10:37:55] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in D:\\Projects\\HRI\\RecLand\\app\\Models\\SubmitCv.php on line 354", "message_html": null, "is_string": false, "label": "warning", "time": **********.595146, "xdebug_link": null, "collector": "log"}, {"message": "[10:37:55] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in D:\\Projects\\HRI\\RecLand\\app\\Models\\SubmitCv.php on line 349", "message_html": null, "is_string": false, "label": "warning", "time": **********.59524, "xdebug_link": null, "collector": "log"}, {"message": "[10:37:55] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in D:\\Projects\\HRI\\RecLand\\app\\Models\\SubmitCv.php on line 377", "message_html": null, "is_string": false, "label": "warning", "time": **********.595346, "xdebug_link": null, "collector": "log"}, {"message": "[10:37:55] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in D:\\Projects\\HRI\\RecLand\\app\\Models\\SubmitCv.php on line 354", "message_html": null, "is_string": false, "label": "warning", "time": **********.595495, "xdebug_link": null, "collector": "log"}, {"message": "[10:37:55] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in D:\\Projects\\HRI\\RecLand\\app\\Models\\SubmitCv.php on line 349", "message_html": null, "is_string": false, "label": "warning", "time": **********.59559, "xdebug_link": null, "collector": "log"}, {"message": "[10:37:55] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in D:\\Projects\\HRI\\RecLand\\app\\Models\\SubmitCv.php on line 377", "message_html": null, "is_string": false, "label": "warning", "time": **********.595697, "xdebug_link": null, "collector": "log"}, {"message": "[10:37:55] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in D:\\Projects\\HRI\\RecLand\\app\\Models\\SubmitCv.php on line 354", "message_html": null, "is_string": false, "label": "warning", "time": **********.595847, "xdebug_link": null, "collector": "log"}, {"message": "[10:37:55] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in D:\\Projects\\HRI\\RecLand\\app\\Models\\SubmitCv.php on line 349", "message_html": null, "is_string": false, "label": "warning", "time": **********.59594, "xdebug_link": null, "collector": "log"}, {"message": "[10:37:55] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in D:\\Projects\\HRI\\RecLand\\app\\Models\\SubmitCv.php on line 377", "message_html": null, "is_string": false, "label": "warning", "time": **********.596046, "xdebug_link": null, "collector": "log"}, {"message": "[10:37:55] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in D:\\Projects\\HRI\\RecLand\\app\\Models\\SubmitCv.php on line 354", "message_html": null, "is_string": false, "label": "warning", "time": **********.596193, "xdebug_link": null, "collector": "log"}, {"message": "[10:37:55] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in D:\\Projects\\HRI\\RecLand\\app\\Models\\SubmitCv.php on line 349", "message_html": null, "is_string": false, "label": "warning", "time": **********.596285, "xdebug_link": null, "collector": "log"}, {"message": "[10:37:55] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in D:\\Projects\\HRI\\RecLand\\app\\Models\\SubmitCv.php on line 377", "message_html": null, "is_string": false, "label": "warning", "time": **********.59639, "xdebug_link": null, "collector": "log"}, {"message": "[10:37:55] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(id) as total, DATE_FORMAT(created_at, \\\"%m\\\") as month from `users` where `is_active` = '1' and `is_real` = '1' and `type` = 'employer' and `created_at` between '2025-01-01 00:00:00' and '2025-12-31 23:59:59' group by DATE_FORMAT(created_at, \\\"%m\\\") order by DATE_FORMAT(created_at, \\\"%m\\\")\\n-- \",\n    \"Time:\": 7.17\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.603968, "xdebug_link": null, "collector": "log"}, {"message": "[10:37:55] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(id) as total, DATE_FORMAT(created_at, \\\"%m\\\") as month from `job` where `is_active` = '1' and `is_real` = '1' and `created_at` between '2025-01-01 00:00:00' and '2025-12-31 23:59:59' group by DATE_FORMAT(created_at, \\\"%m\\\") order by DATE_FORMAT(created_at, \\\"%m\\\")\\n-- \",\n    \"Time:\": 6.8\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.612488, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 3, "start": 1754278674.91694, "end": **********.632761, "duration": 0.7158210277557373, "duration_str": "716ms", "measures": [{"label": "Booting", "start": 1754278674.91694, "relative_start": 0, "end": **********.262429, "relative_end": **********.262429, "duration": 0.****************, "duration_str": "345ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.262442, "relative_start": 0.****************, "end": **********.632763, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "370ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.274007, "relative_start": 0.****************, "end": **********.280381, "relative_end": **********.280381, "duration": 0.006373882293701172, "duration_str": "6.37ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 6, "nb_templates": 6, "templates": [{"name": "1x admin.pages.dashboard.index", "param_count": null, "params": [], "start": **********.624017, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/pages/dashboard/index.blade.phpadmin.pages.dashboard.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Fpages%2Fdashboard%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.pages.dashboard.index"}, {"name": "1x admin.layouts.app", "param_count": null, "params": [], "start": **********.625406, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/layouts/app.blade.phpadmin.layouts.app", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.layouts.app"}, {"name": "1x admin.inc_layouts.side_bar", "param_count": null, "params": [], "start": **********.626687, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/inc_layouts/side_bar.blade.phpadmin.inc_layouts.side_bar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Finc_layouts%2Fside_bar.blade.php&line=1", "ajax": false, "filename": "side_bar.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.inc_layouts.side_bar"}, {"name": "1x admin.inc_layouts.header", "param_count": null, "params": [], "start": **********.628154, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/inc_layouts/header.blade.phpadmin.inc_layouts.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Finc_layouts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.inc_layouts.header"}, {"name": "1x admin.inc_layouts.footer", "param_count": null, "params": [], "start": **********.628748, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/inc_layouts/footer.blade.phpadmin.inc_layouts.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Finc_layouts%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.inc_layouts.footer"}, {"name": "1x admin.inc_layouts.toast.message", "param_count": null, "params": [], "start": **********.630151, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/inc_layouts/toast/message.blade.phpadmin.inc_layouts.toast.message", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Finc_layouts%2Ftoast%2Fmessage.blade.php&line=1", "ajax": false, "filename": "message.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.inc_layouts.toast.message"}]}, "route": {"uri": "GET admin/dashboard", "middleware": "web, check-admin, check-role", "controller": "App\\Http\\Controllers\\Admin\\DashboardController@index<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=40\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "admin", "as": "dashboard", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=40\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Admin/DashboardController.php:40-67</a>"}, "queries": {"count": 13, "nb_statements": 13, "nb_visible_statements": 13, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.22212, "accumulated_duration_str": "222ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "check-admin", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckLoginAdmin.php", "line": 20}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.3285592, "duration": 0.01924, "duration_str": "19.24ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `id` = ? limit 1", "hash": "ac0ef66d2bf40f3993c8e40de021b0d99da887a13adec01b9fbb0601a2bf69c5"}, "start_percent": 0, "width_percent": 8.662}, {"sql": "select `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Services/Admin/PermissionService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\PermissionService.php", "line": 19}, {"index": 19, "namespace": null, "name": "app/Services/Admin/PermissionService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\PermissionService.php", "line": 31}, {"index": 20, "namespace": "middleware", "name": "check-role", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckRole.php", "line": 26}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 22, "namespace": "middleware", "name": "check-admin", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckLoginAdmin.php", "line": 21}], "start": **********.355817, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "PermissionService.php:19", "source": {"index": 18, "namespace": null, "name": "app/Services/Admin/PermissionService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\PermissionService.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FServices%2FAdmin%2FPermissionService.php&line=19", "ajax": false, "filename": "PermissionService.php", "line": "19"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)", "hash": "09b63fe1be07508dfb18b73a990c5ee0249dd740fb6501945ccdb8043c3b971c"}, "start_percent": 8.662, "width_percent": 0.288}, {"sql": "select count(*) as aggregate from `users` where `type` = 'employer' and `is_active` = 1 and `is_real` = 1", "type": "query", "params": [], "bindings": ["employer", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserRepository.php", "line": 124}, {"index": 16, "namespace": null, "name": "app/Services/Admin/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\UserService.php", "line": 280}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 42}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.358759, "duration": 0.01367, "duration_str": "13.67ms", "memory": 0, "memory_str": null, "filename": "UserRepository.php:124", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserRepository.php", "line": 124}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserRepository.php&line=124", "ajax": false, "filename": "UserRepository.php", "line": "124"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `users` where `type` = ? and `is_active` = ? and `is_real` = ?", "hash": "5400da6f53b3a1746523bcbdccc5e32c7c33edb381f87e0422999bc39fa406b5"}, "start_percent": 8.95, "width_percent": 6.154}, {"sql": "select count(*) as aggregate from `users` where `type` = 'rec' and `is_active` = 1 and `is_real` = 1", "type": "query", "params": [], "bindings": ["rec", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserRepository.php", "line": 124}, {"index": 16, "namespace": null, "name": "app/Services/Admin/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\UserService.php", "line": 383}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.373801, "duration": 0.00846, "duration_str": "8.46ms", "memory": 0, "memory_str": null, "filename": "UserRepository.php:124", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserRepository.php", "line": 124}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserRepository.php&line=124", "ajax": false, "filename": "UserRepository.php", "line": "124"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `users` where `type` = ? and `is_active` = ? and `is_real` = ?", "hash": "0a9d3bc47f3a62568ebf01eeb515e51f679b9a19dd1e720a6500ce23fc0eaf3e"}, "start_percent": 15.104, "width_percent": 3.809}, {"sql": "select count(*) as aggregate from `warehouse_cvs` where `is_active` = 1 and `is_real` = 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/WareHouseCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\WareHouseCvRepository.php", "line": 73}, {"index": 16, "namespace": null, "name": "app/Services/Admin/WareHouseCvService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\WareHouseCvService.php", "line": 125}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3842468, "duration": 0.03972, "duration_str": "39.72ms", "memory": 0, "memory_str": null, "filename": "WareHouseCvRepository.php:73", "source": {"index": 15, "namespace": null, "name": "app/Repositories/WareHouseCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\WareHouseCvRepository.php", "line": 73}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FWareHouseCvRepository.php&line=73", "ajax": false, "filename": "WareHouseCvRepository.php", "line": "73"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `warehouse_cvs` where `is_active` = ? and `is_real` = ?", "hash": "57ea448671ca7a8a9b3e531931b7f0449abc9c00005f3c47886e381b1c7f17df"}, "start_percent": 18.913, "width_percent": 17.882}, {"sql": "select count(*) as aggregate from `job` where `is_active` = 1 and `is_real` = 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/JobRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobRepository.php", "line": 28}, {"index": 16, "namespace": null, "name": "app/Services/Admin/JobService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\JobService.php", "line": 49}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 45}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.426207, "duration": 0.0065899999999999995, "duration_str": "6.59ms", "memory": 0, "memory_str": null, "filename": "JobRepository.php:28", "source": {"index": 15, "namespace": null, "name": "app/Repositories/JobRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobRepository.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FJobRepository.php&line=28", "ajax": false, "filename": "JobRepository.php", "line": "28"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `job` where `is_active` = ? and `is_real` = ?", "hash": "2eaefb3cde0ef01211b543109a63b6da313eafd2cc3d10d3c8a97cde6bdb9843"}, "start_percent": 36.795, "width_percent": 2.967}, {"sql": "select count(*) as aggregate from `companies` where `is_active` = 1 and `is_real` = 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\CompanyRepository.php", "line": 70}, {"index": 16, "namespace": null, "name": "app/Services/Admin/CompanyService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\CompanyService.php", "line": 132}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.434522, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:70", "source": {"index": 15, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\CompanyRepository.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FCompanyRepository.php&line=70", "ajax": false, "filename": "CompanyRepository.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `companies` where `is_active` = ? and `is_real` = ?", "hash": "cfc944edf95325b5bba5ff075197e15e88fd5c0f74bba07a1a361ba653255007"}, "start_percent": 39.762, "width_percent": 0.801}, {"sql": "select COUNT(id) as total, DATE_FORMAT(created_at, \"%m\") as month from `warehouse_cvs` where `is_active` = 1 and `is_real` = 1 and `created_at` between '2025-01-01 00:00:00' and '2025-12-31 23:59:59' group by DATE_FORMAT(created_at, \"%m\") order by DATE_FORMAT(created_at, \"%m\")", "type": "query", "params": [], "bindings": [1, 1, "2025-01-01 00:00:00", "2025-12-31 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/WareHouseCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\WareHouseCvRepository.php", "line": 91}, {"index": 15, "namespace": null, "name": "app/Services/Admin/WareHouseCvService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\WareHouseCvService.php", "line": 176}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.43847, "duration": 0.048670000000000005, "duration_str": "48.67ms", "memory": 0, "memory_str": null, "filename": "WareHouseCvRepository.php:91", "source": {"index": 14, "namespace": null, "name": "app/Repositories/WareHouseCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\WareHouseCvRepository.php", "line": 91}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FWareHouseCvRepository.php&line=91", "ajax": false, "filename": "WareHouseCvRepository.php", "line": "91"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select COUNT(id) as total, DATE_FORMAT(created_at, \"%m\") as month from `warehouse_cvs` where `is_active` = ? and `is_real` = ? and `created_at` between ? and ? group by DATE_FORMAT(created_at, \"%m\") order by DATE_FORMAT(created_at, \"%m\")", "hash": "3ccc963020b3ddffdf2dbd1bae35dbf12cf824fbf14924dc4b492d8389de0a9c"}, "start_percent": 40.564, "width_percent": 21.912}, {"sql": "select (count(id)) as total, (DATE_FORMAT(created_at, '%m')) as month from `users` where `type` = 'rec' and `is_active` = 1 and `is_real` = 1 and `created_at` >= '2025-01-01 00:00:00' and `created_at` <= '2025-12-31 23:59:59' group by DATE_FORMAT(created_at, '%m') order by DATE_FORMAT(created_at, '%m') asc", "type": "query", "params": [], "bindings": ["rec", 1, 1, "2025-01-01 00:00:00", "2025-12-31 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserRepository.php", "line": 153}, {"index": 15, "namespace": null, "name": "app/Services/Admin/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\UserService.php", "line": 394}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.493881, "duration": 0.00911, "duration_str": "9.11ms", "memory": 0, "memory_str": null, "filename": "UserRepository.php:153", "source": {"index": 14, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserRepository.php", "line": 153}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserRepository.php&line=153", "ajax": false, "filename": "UserRepository.php", "line": "153"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select (count(id)) as total, (DATE_FORMAT(created_at, '%m')) as month from `users` where `type` = ? and `is_active` = ? and `is_real` = ? and `created_at` >= ? and `created_at` <= ? group by DATE_FORMAT(created_at, '%m') order by DATE_FORMAT(created_at, '%m') asc", "hash": "e8fbc957f2c49fd77940a16134d99639318bd13dcfa9c5d03de8be6e43769dd8"}, "start_percent": 62.475, "width_percent": 4.101}, {"sql": "select COUNT(id) as total, DATE_FORMAT(created_at, \"%m\") as month from `warehouse_cv_sellings` where `status` = 0 and `is_real` = 1 and `created_at` between '2025-01-01 00:00:00' and '2025-12-31 23:59:59' and `warehouse_cv_sellings`.`deleted_at` is null group by DATE_FORMAT(created_at, \"%m\") order by DATE_FORMAT(created_at, \"%m\")", "type": "query", "params": [], "bindings": [0, 1, "2025-01-01 00:00:00", "2025-12-31 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/WareHouseCvSellingRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\WareHouseCvSellingRepository.php", "line": 441}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/WareHouseCvSellingService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WareHouseCvSellingService.php", "line": 274}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 49}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.512662, "duration": 0.05117, "duration_str": "51.17ms", "memory": 0, "memory_str": null, "filename": "WareHouseCvSellingRepository.php:441", "source": {"index": 14, "namespace": null, "name": "app/Repositories/WareHouseCvSellingRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\WareHouseCvSellingRepository.php", "line": 441}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FWareHouseCvSellingRepository.php&line=441", "ajax": false, "filename": "WareHouseCvSellingRepository.php", "line": "441"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select COUNT(id) as total, DATE_FORMAT(created_at, \"%m\") as month from `warehouse_cv_sellings` where `status` = ? and `is_real` = ? and `created_at` between ? and ? and `warehouse_cv_sellings`.`deleted_at` is null group by DATE_FORMAT(created_at, \"%m\") order by DATE_FORMAT(created_at, \"%m\")", "hash": "b77c5651fcfac558d034374a4481f9090d8abcc07fe96585d4e6d29e6fe3143d"}, "start_percent": 66.577, "width_percent": 23.037}, {"sql": "select COUNT(submit_cvs.id) as total, DATE_FORMAT(submit_cvs.created_at, \"%m\") as month from `submit_cvs` inner join `job` on `submit_cvs`.`job_id` = `job`.`id` where `submit_cvs`.`is_active` = 1 and `job`.`is_real` = 1 and `submit_cvs`.`created_at` between '2025-01-01 00:00:00' and '2025-12-31 23:59:59' group by DATE_FORMAT(submit_cvs.created_at, \"%m\") order by DATE_FORMAT(submit_cvs.created_at, \"%m\")", "type": "query", "params": [], "bindings": [1, 1, "2025-01-01 00:00:00", "2025-12-31 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 1012}, {"index": 15, "namespace": null, "name": "app/Services/Admin/SubmitCvService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\SubmitCvService.php", "line": 1178}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 50}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.583081, "duration": 0.0091, "duration_str": "9.1ms", "memory": 0, "memory_str": null, "filename": "SubmitCvRepository.php:1012", "source": {"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 1012}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSubmitCvRepository.php&line=1012", "ajax": false, "filename": "SubmitCvRepository.php", "line": "1012"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select COUNT(submit_cvs.id) as total, DATE_FORMAT(submit_cvs.created_at, \"%m\") as month from `submit_cvs` inner join `job` on `submit_cvs`.`job_id` = `job`.`id` where `submit_cvs`.`is_active` = ? and `job`.`is_real` = ? and `submit_cvs`.`created_at` between ? and ? group by DATE_FORMAT(submit_cvs.created_at, \"%m\") order by DATE_FORMAT(submit_cvs.created_at, \"%m\")", "hash": "facd1c91a2ae9480269547cfae091dd3a486ef2d97c0999c0588862ed02894be"}, "start_percent": 89.614, "width_percent": 4.097}, {"sql": "select count(id) as total, DATE_FORMAT(created_at, \"%m\") as month from `users` where `is_active` = 1 and `is_real` = 1 and `type` = 'employer' and `created_at` between '2025-01-01 00:00:00' and '2025-12-31 23:59:59' group by DATE_FORMAT(created_at, \"%m\") order by DATE_FORMAT(created_at, \"%m\")", "type": "query", "params": [], "bindings": [1, 1, "employer", "2025-01-01 00:00:00", "2025-12-31 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserRepository.php", "line": 454}, {"index": 15, "namespace": null, "name": "app/Services/Admin/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\UserService.php", "line": 658}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 51}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.596858, "duration": 0.00717, "duration_str": "7.17ms", "memory": 0, "memory_str": null, "filename": "UserRepository.php:454", "source": {"index": 14, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserRepository.php", "line": 454}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserRepository.php&line=454", "ajax": false, "filename": "UserRepository.php", "line": "454"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(id) as total, DATE_FORMAT(created_at, \"%m\") as month from `users` where `is_active` = ? and `is_real` = ? and `type` = ? and `created_at` between ? and ? group by DATE_FORMAT(created_at, \"%m\") order by DATE_FORMAT(created_at, \"%m\")", "hash": "9ed55b7d3d274c4a57761558a1e3da4c5538164793b08f878d9dd2be0f20c0ef"}, "start_percent": 93.711, "width_percent": 3.228}, {"sql": "select count(id) as total, DATE_FORMAT(created_at, \"%m\") as month from `job` where `is_active` = 1 and `is_real` = 1 and `created_at` between '2025-01-01 00:00:00' and '2025-12-31 23:59:59' group by DATE_FORMAT(created_at, \"%m\") order by DATE_FORMAT(created_at, \"%m\")", "type": "query", "params": [], "bindings": [1, 1, "2025-01-01 00:00:00", "2025-12-31 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/JobRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobRepository.php", "line": 490}, {"index": 15, "namespace": null, "name": "app/Services/Admin/JobService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\JobService.php", "line": 536}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 52}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.605752, "duration": 0.0068, "duration_str": "6.8ms", "memory": 0, "memory_str": null, "filename": "JobRepository.php:490", "source": {"index": 14, "namespace": null, "name": "app/Repositories/JobRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobRepository.php", "line": 490}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FJobRepository.php&line=490", "ajax": false, "filename": "JobRepository.php", "line": "490"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(id) as total, DATE_FORMAT(created_at, \"%m\") as month from `job` where `is_active` = ? and `is_real` = ? and `created_at` between ? and ? group by DATE_FORMAT(created_at, \"%m\") order by DATE_FORMAT(created_at, \"%m\")", "hash": "55ea592fb7c3d49e55ce4d297842a0c9ff093677cce6ed6042a98ec54963e10b"}, "start_percent": 96.939, "width_percent": 3.061}]}, "models": {"data": {"App\\Models\\User": {"retrieved": 15, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\WareHouseCv": {"retrieved": 7, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FWareHouseCv.php&line=1", "ajax": false, "filename": "WareHouseCv.php", "line": "?"}}, "App\\Models\\WareHouseCvSelling": {"retrieved": 7, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FWareHouseCvSelling.php&line=1", "ajax": false, "filename": "WareHouseCvSelling.php", "line": "?"}}, "App\\Models\\SubmitCv": {"retrieved": 7, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FSubmitCv.php&line=1", "ajax": false, "filename": "SubmitCv.php", "line": "?"}}, "App\\Models\\Job": {"retrieved": 7, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FJob.php&line=1", "ajax": false, "filename": "Job.php", "line": "?"}}, "App\\Models\\Role": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 44, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 44}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "67FrIiAKRzqdDDYE9lpf1v63S50ahQba2svcMgmp", "PHPDEBUGBAR_STACK_DATA": "array:1 [\n  \"01K1SJGGEHMNGSX6X27JF4M3CN\" => null\n]", "_previous": "array:1 [\n  \"url\" => \"http://recland.local/admin\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1"}, "request": {"data": {"status": "200 OK", "full_url": "http://recland.local/admin/dashboard", "action_name": "dashboard", "controller_action": "App\\Http\\Controllers\\Admin\\DashboardController@index", "uri": "GET admin/dashboard", "controller": "App\\Http\\Controllers\\Admin\\DashboardController@index<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=40\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "admin", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=40\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Admin/DashboardController.php:40-67</a>", "middleware": "web, check-admin, check-role", "duration": "718ms", "peak_memory": "44MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-995986999 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-995986999\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-133303768 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-133303768\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,vi;q=0.8,nl;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1257 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IitBOXN1Q0gzM3JhT3VZb21ZeTl4RlE9PSIsInZhbHVlIjoiajdRdjJiR05jLzBidEEwQTBVb1YvNjdPU0xKc2VQenJCS2FmRXdYK3h2ZXhma2NoaW14RW5UWlZJTnBYVnRpak40b21DRklYVFA4M1ptUFoySStpRUhYWEYrQ3hRNksrYXJjK0ZhNVcyNitZNHE5N3czeXc2dytzcjJ6M3JkNHQiLCJtYWMiOiI2MWE5YjM4MzJhZTJhOGE5ZTA5M2ViYTU2Mzk3NmM5OGQxODE0NDhkOTk1YzdiMDM2ZGI5NzYxZTc4Y2UxYzVmIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6ImwzVkxoQlRjYjdvdTRpbHFxYVVxZ3c9PSIsInZhbHVlIjoieU5PQ3lHNmprVHdJcTJnZHJMNVFhY1FuRERnUEdDQ2dnQnFXcUhub1VpWXNEc2QvMldjSnNRRm9YN2M2clo4L256S0hOMFB2ZjJEbS90V1V4T2MwSXNQS2ZOMEphOC9ra1AwbGMrbDNXY255dW43OHllRzFTMUJpN0pZS1VpU2EiLCJtYWMiOiI5OTY0NDFjZmE2ZjQ2ZTAxN2RlMGYxYzZjNTFhOWNjN2I3YzYyZTk3ZWNkNTQ4N2M5YjRjNDBhYWY2NWY4MzQ3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-*********7 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|cm8f4SgFu8wrAd7wPR9b0gsQwQ8YYGlGSWS8Y9hgKQXIsj7qT8VG3KwC3dMx|$2y$10$iTSniY9ReMSnjm6GaX0bRuGWPNsyk9pQat6r8dKHdyoQSgwMkVojS</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">67FrIiAKRzqdDDYE9lpf1v63S50ahQba2svcMgmp</span>\"\n  \"<span class=sf-dump-key>recland_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aK6CrmwILvnr50B6VrqyQAI9QjCWvYUdKGChzrVE</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********7\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1112766246 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 04 Aug 2025 03:37:55 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1112766246\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2025135952 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">67FrIiAKRzqdDDYE9lpf1v63S50ahQba2svcMgmp</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01K1SJGGEHMNGSX6X27JF4M3CN</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://recland.local/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2025135952\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://recland.local/admin/dashboard", "action_name": "dashboard", "controller_action": "App\\Http\\Controllers\\Admin\\DashboardController@index"}, "badge": null}}