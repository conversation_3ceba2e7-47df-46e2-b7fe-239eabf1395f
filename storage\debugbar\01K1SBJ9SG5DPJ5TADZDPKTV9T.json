{"__meta": {"id": "01K1SBJ9SG5DPJ5TADZDPKTV9T", "datetime": "2025-08-04 08:36:33", "utime": **********.585866, "method": "GET", "uri": "/admin/employers", "ip": "127.0.0.1"}, "php": {"version": "8.1.6", "interface": "apache2handler"}, "messages": {"count": 3, "messages": [{"message": "[08:36:31] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `users` where `type` = 'employer'\\n-- \",\n    \"Time:\": 12.18\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.961732, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:31] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `users` where `type` = 'employer' and month(`created_at`) = '08' and year(`created_at`) = '2025'\\n-- \",\n    \"Time:\": 8.06\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.972743, "xdebug_link": null, "collector": "log"}, {"message": "[08:36:33] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `id` = '1' limit 1\\n-- \",\n    \"Time:\": 0.58\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.416815, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 3, "start": 1754271390.683145, "end": **********.585908, "duration": 2.9027628898620605, "duration_str": "2.9s", "measures": [{"label": "Booting", "start": 1754271390.683145, "relative_start": 0, "end": **********.083779, "relative_end": **********.083779, "duration": 0.****************, "duration_str": "401ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.083789, "relative_start": 0.*****************, "end": **********.585911, "relative_end": 3.0994415283203125e-06, "duration": 2.***************, "duration_str": "2.5s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.096969, "relative_start": 0.*****************, "end": **********.101458, "relative_end": **********.101458, "duration": 0.*****************, "duration_str": "4.49ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "40MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 31, "nb_templates": 31, "templates": [{"name": "1x crud::list", "param_count": null, "params": [], "start": **********.161463, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/list.blade.phpcrud::list", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Fcrud%2Flist.blade.php&line=1", "ajax": false, "filename": "list.blade.php", "line": "?"}, "render_count": 1, "name_original": "crud::list"}, {"name": "1x crud::inc.button_stack", "param_count": null, "params": [], "start": **********.417719, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/inc/button_stack.blade.phpcrud::inc.button_stack", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Fcrud%2Finc%2Fbutton_stack.blade.php&line=1", "ajax": false, "filename": "button_stack.blade.php", "line": "?"}, "render_count": 1, "name_original": "crud::inc.button_stack"}, {"name": "1x crud::buttons.employer.create_employer", "param_count": null, "params": [], "start": **********.451163, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/buttons/employer/create_employer.blade.phpcrud::buttons.employer.create_employer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Fcrud%2Fbuttons%2Femployer%2Fcreate_employer.blade.php&line=1", "ajax": false, "filename": "create_employer.blade.php", "line": "?"}, "render_count": 1, "name_original": "crud::buttons.employer.create_employer"}, {"name": "1x crud::inc.filters_navbar", "param_count": null, "params": [], "start": **********.468723, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/inc/filters_navbar.blade.phpcrud::inc.filters_navbar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Fcrud%2Finc%2Ffilters_navbar.blade.php&line=1", "ajax": false, "filename": "filters_navbar.blade.php", "line": "?"}, "render_count": 1, "name_original": "crud::inc.filters_navbar"}, {"name": "3x backpack.pro::filters.dropdown", "param_count": null, "params": [], "start": **********.536113, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\packages\\backpack-pro\\src/../resources/views/filters/dropdown.blade.phpbackpack.pro::filters.dropdown", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fpackages%2Fbackpack-pro%2Fresources%2Fviews%2Ffilters%2Fdropdown.blade.php&line=1", "ajax": false, "filename": "dropdown.blade.php", "line": "?"}, "render_count": 3, "name_original": "backpack.pro::filters.dropdown"}, {"name": "1x backpack.pro::filters.date_range", "param_count": null, "params": [], "start": **********.63183, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\packages\\backpack-pro\\src/../resources/views/filters/date_range.blade.phpbackpack.pro::filters.date_range", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fpackages%2Fbackpack-pro%2Fresources%2Fviews%2Ffilters%2Fdate_range.blade.php&line=1", "ajax": false, "filename": "date_range.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.pro::filters.date_range"}, {"name": "1x crud::inc.datatables_logic", "param_count": null, "params": [], "start": **********.724499, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/inc/datatables_logic.blade.phpcrud::inc.datatables_logic", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Fcrud%2Finc%2Fdatatables_logic.blade.php&line=1", "ajax": false, "filename": "datatables_logic.blade.php", "line": "?"}, "render_count": 1, "name_original": "crud::inc.datatables_logic"}, {"name": "1x crud::inc.export_buttons", "param_count": null, "params": [], "start": **********.888358, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/inc/export_buttons.blade.phpcrud::inc.export_buttons", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Fcrud%2Finc%2Fexport_buttons.blade.php&line=1", "ajax": false, "filename": "export_buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "crud::inc.export_buttons"}, {"name": "1x crud::inc.details_row_logic", "param_count": null, "params": [], "start": **********.916764, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/inc/details_row_logic.blade.phpcrud::inc.details_row_logic", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Fcrud%2Finc%2Fdetails_row_logic.blade.php&line=1", "ajax": false, "filename": "details_row_logic.blade.php", "line": "?"}, "render_count": 1, "name_original": "crud::inc.details_row_logic"}, {"name": "1x admin.layouts.app_backpack", "param_count": null, "params": [], "start": **********.946978, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/layouts/app_backpack.blade.phpadmin.layouts.app_backpack", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Flayouts%2Fapp_backpack.blade.php&line=1", "ajax": false, "filename": "app_backpack.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.layouts.app_backpack"}, {"name": "1x backpack::inc.head", "param_count": null, "params": [], "start": **********.12411, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\resources\\views\\base/inc/head.blade.phpbackpack::inc.head", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fbase%2Finc%2Fhead.blade.php&line=1", "ajax": false, "filename": "head.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack::inc.head"}, {"name": "5x backpack::inc.widgets", "param_count": null, "params": [], "start": **********.213182, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\resources\\views\\base/inc/widgets.blade.phpbackpack::inc.widgets", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fbase%2Finc%2Fwidgets.blade.php&line=1", "ajax": false, "filename": "widgets.blade.php", "line": "?"}, "render_count": 5, "name_original": "backpack::inc.widgets"}, {"name": "1x backpack::widgets.div", "param_count": null, "params": [], "start": **********.24905, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\resources\\views\\base/widgets/div.blade.phpbackpack::widgets.div", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fbase%2Fwidgets%2Fdiv.blade.php&line=1", "ajax": false, "filename": "div.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack::widgets.div"}, {"name": "2x backpack::widgets.progress", "param_count": null, "params": [], "start": **********.293918, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\resources\\views\\base/widgets/progress.blade.phpbackpack::widgets.progress", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fbase%2Fwidgets%2Fprogress.blade.php&line=1", "ajax": false, "filename": "progress.blade.php", "line": "?"}, "render_count": 2, "name_original": "backpack::widgets.progress"}, {"name": "2x backpack::widgets.inc.wrapper_start", "param_count": null, "params": [], "start": **********.350257, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\resources\\views\\base/widgets/inc/wrapper_start.blade.phpbackpack::widgets.inc.wrapper_start", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fbase%2Fwidgets%2Finc%2Fwrapper_start.blade.php&line=1", "ajax": false, "filename": "wrapper_start.blade.php", "line": "?"}, "render_count": 2, "name_original": "backpack::widgets.inc.wrapper_start"}, {"name": "2x backpack::widgets.inc.wrapper_end", "param_count": null, "params": [], "start": **********.387138, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\resources\\views\\base/widgets/inc/wrapper_end.blade.phpbackpack::widgets.inc.wrapper_end", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fbase%2Fwidgets%2Finc%2Fwrapper_end.blade.php&line=1", "ajax": false, "filename": "wrapper_end.blade.php", "line": "?"}, "render_count": 2, "name_original": "backpack::widgets.inc.wrapper_end"}, {"name": "1x admin.inc_layouts.side_bar", "param_count": null, "params": [], "start": **********.409477, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/inc_layouts/side_bar.blade.phpadmin.inc_layouts.side_bar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Finc_layouts%2Fside_bar.blade.php&line=1", "ajax": false, "filename": "side_bar.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.inc_layouts.side_bar"}, {"name": "1x admin.inc_layouts.header", "param_count": null, "params": [], "start": **********.438315, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/inc_layouts/header.blade.phpadmin.inc_layouts.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Finc_layouts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.inc_layouts.header"}, {"name": "1x admin.inc_layouts.footer", "param_count": null, "params": [], "start": **********.438933, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/inc_layouts/footer.blade.phpadmin.inc_layouts.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Finc_layouts%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.inc_layouts.footer"}, {"name": "1x backpack::inc.scripts", "param_count": null, "params": [], "start": **********.439776, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\resources\\views\\base/inc/scripts.blade.phpbackpack::inc.scripts", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fbase%2Finc%2Fscripts.blade.php&line=1", "ajax": false, "filename": "scripts.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack::inc.scripts"}, {"name": "1x backpack::inc.alerts", "param_count": null, "params": [], "start": **********.512136, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\resources\\views\\base/inc/alerts.blade.phpbackpack::inc.alerts", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fbase%2Finc%2Falerts.blade.php&line=1", "ajax": false, "filename": "alerts.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack::inc.alerts"}, {"name": "1x crud::inc.ajax_error_frame", "param_count": null, "params": [], "start": **********.556534, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/inc/ajax_error_frame.blade.phpcrud::inc.ajax_error_frame", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Fcrud%2Finc%2Fajax_error_frame.blade.php&line=1", "ajax": false, "filename": "ajax_error_frame.blade.php", "line": "?"}, "render_count": 1, "name_original": "crud::inc.ajax_error_frame"}]}, "route": {"uri": "GET admin/employers", "middleware": "web, admin, Closure", "as": "employers.index", "operation": "list", "controller": "App\\Http\\Controllers\\Admin\\EmployerCrudController@index<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FHttp%2FControllers%2FOperations%2FListOperation.php&line=54\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "admin", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FHttp%2FControllers%2FOperations%2FListOperation.php&line=54\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/backpack/crud/src/app/Http/Controllers/Operations/ListOperation.php:54-63</a>"}, "queries": {"count": 4, "nb_statements": 4, "nb_visible_statements": 4, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.03647, "accumulated_duration_str": "36.47ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select count(*) as aggregate from `users` where `type` = 'employer'", "type": "query", "params": [], "bindings": ["employer"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 612}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 82}, {"index": 17, "namespace": null, "name": "vendor/backpack/crud/src/app/Http/Controllers/CrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Http\\Controllers\\CrudController.php", "line": 121}, {"index": 18, "namespace": null, "name": "vendor/backpack/crud/src/app/Http/Controllers/CrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Http\\Controllers\\CrudController.php", "line": 42}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 162}], "start": **********.9499178, "duration": 0.01218, "duration_str": "12.18ms", "memory": 0, "memory_str": null, "filename": "EmployerCrudController.php:612", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 612}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FEmployerCrudController.php&line=612", "ajax": false, "filename": "EmployerCrudController.php", "line": "612"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `users` where `type` = ?", "hash": "e7098d5a40a0f6a86f190494613469e3f8705ca86dc150dec271de79e327bf78"}, "start_percent": 0, "width_percent": 33.397}, {"sql": "select count(*) as aggregate from `users` where `type` = 'employer' and month(`created_at`) = '08' and year(`created_at`) = 2025", "type": "query", "params": [], "bindings": ["employer", "08", 2025], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 619}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 91}, {"index": 17, "namespace": null, "name": "vendor/backpack/crud/src/app/Http/Controllers/CrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Http\\Controllers\\CrudController.php", "line": 121}, {"index": 18, "namespace": null, "name": "vendor/backpack/crud/src/app/Http/Controllers/CrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Http\\Controllers\\CrudController.php", "line": 42}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 162}], "start": **********.96477, "duration": 0.008060000000000001, "duration_str": "8.06ms", "memory": 0, "memory_str": null, "filename": "EmployerCrudController.php:619", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 619}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FEmployerCrudController.php&line=619", "ajax": false, "filename": "EmployerCrudController.php", "line": "619"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `users` where `type` = ? and month(`created_at`) = ? and year(`created_at`) = ?", "hash": "2294555b25919f5dd19d1816f6ae207b0af8ab057e4667afe0bf02d0b2d141a6"}, "start_percent": 33.397, "width_percent": 22.1}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'hri_recland_product' and table_name = 'users'", "type": "query", "params": [], "bindings": ["hri_recland_product", "users"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/ColumnsProtectedMethods.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\ColumnsProtectedMethods.php", "line": 312}, {"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Columns.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Columns.php", "line": 382}, {"index": 12, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/CrudColumn.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\CrudColumn.php", "line": 53}, {"index": 13, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Columns.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Columns.php", "line": 423}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/EmployerCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\EmployerCrudController.php", "line": 110}], "start": **********.109616, "duration": 0.01565, "duration_str": "15.65ms", "memory": 0, "memory_str": null, "filename": "ColumnsProtectedMethods.php:312", "source": {"index": 10, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/ColumnsProtectedMethods.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\ColumnsProtectedMethods.php", "line": 312}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FLibrary%2FCrudPanel%2FTraits%2FColumnsProtectedMethods.php&line=312", "ajax": false, "filename": "ColumnsProtectedMethods.php", "line": "312"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select column_name as `column_name` from information_schema.columns where table_schema = ? and table_name = ?", "hash": "2f932a8ab5559dc2e718e2169d292b7c251235f387a3834f8b6fb4b996f87912"}, "start_percent": 55.498, "width_percent": 42.912}, {"sql": "select * from `users` where `id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 75}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 197}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 167}, {"index": 18, "namespace": "view", "name": "admin.inc_layouts.side_bar", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/admin/inc_layouts/side_bar.blade.php", "line": 19}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}], "start": **********.4163148, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:75", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=75", "ajax": false, "filename": "EloquentUserProvider.php", "line": "75"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `id` = ? limit 1", "hash": "9891e48e5284a49fe540ade6a7da8b85a61d520ac7f27f7f97c49ad59654655b"}, "start_percent": 98.41, "width_percent": 1.59}]}, "models": {"data": {"App\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 1}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "67FrIiAKRzqdDDYE9lpf1v63S50ahQba2svcMgmp", "PHPDEBUGBAR_STACK_DATA": "[]", "_previous": "array:1 [\n  \"url\" => \"http://recland.local\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1"}, "request": {"data": {"status": "200 OK", "full_url": "http://recland.local/admin/employers", "action_name": "employers.index", "controller_action": "App\\Http\\Controllers\\Admin\\EmployerCrudController@index", "uri": "GET admin/employers", "operation": "list", "controller": "App\\Http\\Controllers\\Admin\\EmployerCrudController@index<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FHttp%2FControllers%2FOperations%2FListOperation.php&line=54\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "admin", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FHttp%2FControllers%2FOperations%2FListOperation.php&line=54\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/backpack/crud/src/app/Http/Controllers/Operations/ListOperation.php:54-63</a>", "middleware": "web, admin", "duration": "2.91s", "peak_memory": "50MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-489792001 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-489792001\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-989281418 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-989281418\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1531603380 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://recland.local/admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,vi;q=0.8,nl;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1257 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjAxamM1bUNlSmhDaWJyWHh2bHkyTUE9PSIsInZhbHVlIjoiS1p5OUxvb3JnVldSUHhHYmtPVE1SYUhwTHBZbktTaWZhMTdZMGlheXNadGpBMmwzTDNRNVRsejFSWlVESmpTRjRqUHZrMEZTd1hLczVKYytrcndxMUpjWmtJZ3FEUEZ5TmNvUTJmbytYazVlWGNSK2NLN3d4elE1b05VVWJUSUkiLCJtYWMiOiI5NGRkNWEwODAwZDRmOTMwOWQ0YWRjZDkwOTUwODI3MGQ3NTc5NTk1MzFlMmY4ZDI5MGY4MGY4MTBhYzIzZDhiIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6InhIOGNlOU11bVgzajdFYkM0SisyVUE9PSIsInZhbHVlIjoiWkdDNDAzSWxraU9UZ3pSYjhEZmZNa2hXUm9kNVorZFo3bVA5TU9KRGxIT3hiZGtRUTNGTUZiREFLTzdhU0NOZ04rMFNDblIySVhpMERFckNEN3ZVLzNOMzJNdC9YWVRqZDF6RC81Q25FekZsR24xVFN6SkJMTVhxekQ0OUdXUFQiLCJtYWMiOiJiN2EyOWUzOTNhZjFkMDUyYzNjNjJmZGI5ZjE5MmQxMWYwMTQ4NDYwN2FiNDFhMzMyMDZlZWMyZDYyOGM5MzI0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1531603380\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-875898475 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|cm8f4SgFu8wrAd7wPR9b0gsQwQ8YYGlGSWS8Y9hgKQXIsj7qT8VG3KwC3dMx|$2y$10$iTSniY9ReMSnjm6GaX0bRuGWPNsyk9pQat6r8dKHdyoQSgwMkVojS</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">67FrIiAKRzqdDDYE9lpf1v63S50ahQba2svcMgmp</span>\"\n  \"<span class=sf-dump-key>recland_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">v1riCItHM0lP1ly6DcovIBSGnsbUL8yYQ8j9OzIc</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-875898475\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-321218618 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 04 Aug 2025 01:36:32 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-321218618\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1339632274 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">67FrIiAKRzqdDDYE9lpf1v63S50ahQba2svcMgmp</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1339632274\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://recland.local/admin/employers", "action_name": "employers.index", "controller_action": "App\\Http\\Controllers\\Admin\\EmployerCrudController@index"}, "badge": null}}