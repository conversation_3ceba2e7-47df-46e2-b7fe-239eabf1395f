<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class CvRejectedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $submitCv;
    protected $assessment;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($submitCv, $assessment)
    {
        $this->submitCv = $submitCv;
        $this->assessment = $assessment;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $submitCv = $this->submitCv;
        $assessment = $this->assessment;
        return (new MailMessage)
            ->view('email.CvRejectedNotification', compact('notifiable', 'submitCv', 'assessment'))
            ->subject('[<PERSON><PERSON>land] [ CV "' . $submitCv->warehouseCv->candidate_job_title . '" của "' . $submitCv->warehouseCv->candidate_name . '" đã bị từ chối ]');

        // return (new MailMessage)
        //             ->subject('CV của bạn đã bị từ chối')
        //             ->greeting('Xin chào ' . $notifiable->name . ',')
        //             ->line('Chúng tôi rất tiếc phải thông báo rằng CV của bạn cho công việc "' . $this->submitCv->job->name . '" đã bị từ chối.')
        //             ->line('Lý do từ chối:')
        //             ->line($this->assessment)
        //             ->line('Cảm ơn bạn đã quan tâm đến công ty của chúng tôi. Chúng tôi khuyến khích bạn nộp đơn cho các vị trí phù hợp khác trong tương lai.')
        //             ->action('Xem các vị trí công việc khác', url('/job'));
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            //
        ];
    }
}
