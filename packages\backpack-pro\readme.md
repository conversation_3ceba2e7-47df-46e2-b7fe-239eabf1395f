# Pro

Depending on how many projects you'll be using this on, please purchase:
- [Backpack PRO for unlimited projects](https://backpackforlaravel.com/products/pro-for-unlimited-projects) - 399 EUR
- [Backpack PRO for one project](https://backpackforlaravel.com/products/pro-for-one-project) - 69 EUR

> If you've purchased a Backpack v4 license after Feb 9th 2021, you get free access to Backpack v5 PRO. [See details here](https://backpackforlaravel.com/docs/5.x/release-notes#new-pricing).

## Features

### Many _many_ more fields

PRO adds 28 more fields, including: [`address_algolia`](https://backpackforlaravel.com/docs/5.x/crud-fields#address_algolia-pro), [`address_google`](https://backpackforlaravel.com/docs/5.x/crud-fields#address_google-pro), [`base64_image`](https://backpackforlaravel.com/docs/5.x/crud-fields#base64_image-pro), [`browse_multiple`](https://backpackforlaravel.com/docs/5.x/crud-fields#browse_multiple-pro), [`browse`](https://backpackforlaravel.com/docs/5.x/crud-fields#browse-pro), [`ckeditor`](https://backpackforlaravel.com/docs/5.x/crud-fields#ckeditor-pro), [`color_picker`](https://backpackforlaravel.com/docs/5.x/crud-fields#color_picker-pro), [`date_picker`](https://backpackforlaravel.com/docs/5.x/crud-fields#date_picker-pro), [`date_range`](https://backpackforlaravel.com/docs/5.x/crud-fields#date_range-pro), [`datetime_picker`](https://backpackforlaravel.com/docs/5.x/crud-fields#datetime_picker-pro), [`easymde`](https://backpackforlaravel.com/docs/5.x/crud-fields#easymde-pro), [`icon_picker`](https://backpackforlaravel.com/docs/5.x/crud-fields#icon_picker-pro), [`image`](https://backpackforlaravel.com/docs/5.x/crud-fields#image-pro), [`relationship`](https://backpackforlaravel.com/docs/5.x/crud-fields#relationship-pro), [`repeatable`](https://backpackforlaravel.com/docs/5.x/crud-fields#repeatable-pro), [`select_and_order`](https://backpackforlaravel.com/docs/5.x/crud-fields#select_and_order-pro), [`select2_from_ajax_multiple`](https://backpackforlaravel.com/docs/5.x/crud-fields#select2_from_ajax_multiple-pro), [`select2_from_ajax`](https://backpackforlaravel.com/docs/5.x/crud-fields#select2_from_ajax-pro), [`select2_from_array`](https://backpackforlaravel.com/docs/5.x/crud-fields#select2_from_array-pro), [`select2_grouped`](https://backpackforlaravel.com/docs/5.x/crud-fields#select2_grouped-pro), [`select2_multiple`](https://backpackforlaravel.com/docs/5.x/crud-fields#select2_multiple-n-n-relationship-pro), [`select2_nested`](https://backpackforlaravel.com/docs/5.x/crud-fields#select2_nested-pro), [`select2`](https://backpackforlaravel.com/docs/5.x/crud-fields#select2-1-n-relationship-pro), [`table`](https://backpackforlaravel.com/docs/5.x/crud-fields#table-pro), [`tinymce`](https://backpackforlaravel.com/docs/5.x/crud-fields#tinymce-pro), [`video`](https://backpackforlaravel.com/docs/5.x/crud-fields#video-pro), [`wysiwyg`](https://backpackforlaravel.com/docs/5.x/crud-fields#wysiwyg-pro). There are so many of them... that we can't even brag about all of them here.

So we're just going to highlight 3 PRO fields, _each one_ worth 2x-10x the cost of Backpack PRO: 
- [`relationship`](https://backpackforlaravel.com/docs/5.x/crud-fields#relationship-pro) - not only will it create an interface for any Eloquent relationship; but it can do much _much_ more - it can even help you [create entries on-the-fly in a modal](https://backpackforlaravel.com/docs/5.x/crud-fields#create-related-entries-in-a-modal-using-the-inlinecreate-operati), [save additional info on the pivot table](https://backpackforlaravel.com/docs/5.x/crud-fields#save-additional-data-to-pivot-table) or even [create sub-items in a subform, using `subfields`](https://backpackforlaravel.com/docs/5.x/crud-fields#manage-related-entries-in-the-same-form-create-update-delete); this alone can save you dozens of hours!
- [`repeatable`](https://backpackforlaravel.com/docs/5.x/crud-fields#repeatable-pro) - empower your admins to add complex data;
- [`table`](https://backpackforlaravel.com/docs/5.x/crud-fields#table-pro) - a dead-simple way for your admins to add tabular data;

Go ahead and [**see all fields in action**, in our demo, within the Monsters CRUD](https://demo.backpackforlaravel.com/admin/monster/create). They all have clear labels, so you'll know which ones are FREE which ones are PRO.

<a href="https://demo.backpackforlaravel.com/admin/monster/create" class="btn btn-sm btn-outline-info shadow"> Preview </a> 
<a href="https://backpackforlaravel.com/docs/5.x/crud-fields#pro-field-types" class="btn btn-sm btn-info shadow"> Docs </a> 

### More columns

PRO adds 6 more column types to your toolbelt, to be using in your List or Show operations - [`video`](https://backpackforlaravel.com/docs/5.x/crud-columns#video-pro), [`array`](https://backpackforlaravel.com/docs/5.x/crud-columns#array-pro), [`array_count`](https://backpackforlaravel.com/docs/5.x/crud-columns#array_count-pro), [`markdown`](https://backpackforlaravel.com/docs/5.x/crud-columns#markdown-pro), [`relationship`](https://backpackforlaravel.com/docs/5.x/crud-columns#relationship-pro), [`table`](https://backpackforlaravel.com/docs/5.x/crud-columns#table-pro), with more to be added in 2022.

<a href="https://demo.backpackforlaravel.com/admin/monster/1/show" class="btn btn-sm btn-outline-info shadow"> Preview </a> 
<a href="https://backpackforlaravel.com/docs/5.x/crud-columns#pro-column-types" class="btn btn-sm btn-info shadow"> Docs </a> 

### More operations

Currently PRO adds 5 more operations to your toolbelt:
- 3 regular operations: [`Clone`](https://backpackforlaravel.com/docs/5.x/crud-operation-clone), [`Fetch`](https://backpackforlaravel.com/docs/5.x/crud-operation-fetch), [`InlineCreate`](https://backpackforlaravel.com/docs/5.x/crud-operation-inline-create);
- 2 bulk operations: [`BulkClone`](https://backpackforlaravel.com/docs/5.x/crud-operation-clone#clone-multiple-items-bulk-clone), [`BulkDelete`](https://backpackforlaravel.com/docs/5.x/crud-operation-delete#delete-multiple-items-bulk-delete-pro);

### Filters inside your List operation

Easily provide a way for your admin to narrow down the results in the table view, using the 9 available [filters](https://backpackforlaravel.com/docs/5.x/crud-filters). 

<a href="https://demo.backpackforlaravel.com/admin/monster" class="btn btn-sm btn-outline-info shadow"> Preview </a> 
<a href="https://backpackforlaravel.com/docs/5.x/crud-filters" class="btn btn-sm btn-info shadow"> Docs </a> 

### Chart widget

Quickly add bar charts, line charts and pie charts to your dashboards - using [the `chart` widget](https://backpackforlaravel.com/docs/5.x/base-widgets#chart-pro).

<a href="https://demo.backpackforlaravel.com/admin/dashboard" class="btn btn-sm btn-outline-info shadow"> Preview </a> 
<a href="https://backpackforlaravel.com/docs/5.x/base-widgets#chart-pro" class="btn btn-sm btn-info shadow"> Docs </a> 

## Requirements

This package requires Backpack v5, which requires Laravel 8 or Laravel 9.

## Installation

### Quick Installion

In your Laravel + Backpack project, run:

```bash
php artisan backpack:require:pro
```

It will ask you for your token & password - which you get after you purchase this package. You can later [see your token and password in your Backpack account](https://backpackforlaravel.com/user/tokens).

### Manual Installation

If the quick installation above doesn't work, here are the steps you can follow:

**Step 1.** [Buy access to this package](https://backpackforlaravel.com/cart/add-unique-product/10) and you'll get an [access token](https://backpackforlaravel.com/user/tokens). Then:
- add [your token](https://backpackforlaravel.com/user/tokens) to your project's `auth.json` file by running `composer config http-basic.backpackforlaravel.com [your-token-username] [your-token-password]`
- intruct your `composer.json` to look for packages in our private repo too:

```json
    "repositories": [
        {
            "type": "composer",
            "url": "https://repo.backpackforlaravel.com/"
        }
    ],
```

**Step 2.** Install the package using Composer:

``` bash
composer require backpack/pro
```

## Usage

You can use the "PRO" features as instructed in the [Backpack documentation](https://backpackforlaravel.com/docs).

## Overriding

The package provides:
- some extra views for `fields`, `filters`, `columns`, `buttons`, `widgets`; the directories here are checked automatically by Backpack, in this order: your `resources/views/vendor/backpack/crud/fields`, then this package; if you want to override a file provided by this package, just create one with the same name in your application, in the directory above (the exact same way you did in Backpack v4);
- some extra operations; the controller traits are loaded automatically by the files in Backpack/CRUD; if you want to override an operation this package provides, create a new `trait` in your app that uses the trait this package provides; then use _your trait_ wherever you want;

## Contributing

Unfortunately, since this package is closed-source, we don't have a good way to collaborate on it. If you'd like to do so, please let us know in [this thread](https://github.com/Laravel-Backpack/ideas/issues/125). We might find a temporary solution for you, until we find a permanent solution for everybody. 

## Support

To submit issues, bugs and feature requests, please use our [laravel-backpack/crud](https://github.com/laravel-backpack/crud) repo on Github.

## Security

If you discover any security related issues, <NAME_EMAIL> instead of using the issue tracker.

## License

This software is proprietary & closed-source, released under the [End-User License Agreement (EULA) for Private Backpack Addons](https://backpackforlaravel.com/eula). A copy of that license is also provided inside the source code - you can read that file by using the tabs at the beginning of this page. This is the only add-on we sell that is priced differently depending on how many projects you will use it on. So depending on how many Laravel+Backpack+PRO projects you will be developing, please purchase:
- [Backpack PRO for unlimited projects](https://backpackforlaravel.com/products/pro-for-unlimited-projects) - 399 EUR
- [Backpack PRO for one project](https://backpackforlaravel.com/products/pro-for-one-project) - 69 EUR


[ico-version]: https://img.shields.io/packagist/v/backpack/pro.svg?style=flat-square
[ico-downloads]: https://img.shields.io/packagist/dt/backpack/pro.svg?style=flat-square
[link-packagist]: https://packagist.org/packages/backpack/pro
[link-downloads]: https://packagist.org/packages/backpack/pro
[link-author]: https://github.com/backpack
[link-contributors]: ../../contributors
