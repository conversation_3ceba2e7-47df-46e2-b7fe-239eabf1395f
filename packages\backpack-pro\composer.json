{"name": "backpack/pro", "description": "Operations, Fields, Columns, Filters and Widgets for not-so-simple admin panels.", "license": "Proprietary", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://backpackforlaravel.com"}], "homepage": "https://github.com/digitallyhappy/backpack-pro", "keywords": ["<PERSON><PERSON>", "Backpack", "Backpack for <PERSON><PERSON>", "Backpack Addon", "Pro"], "require": {"backpack/crud": "^5.4.9"}, "require-dev": {"phpunit/phpunit": "~9.0", "orchestra/testbench": "~5|~6"}, "autoload": {"psr-4": {"Backpack\\Pro\\": "src/"}}, "autoload-dev": {"psr-4": {"Backpack\\Pro\\Tests\\": "tests"}}, "scripts": {"test": "vendor/bin/phpunit --testdox"}, "extra": {"laravel": {"providers": ["Backpack\\Pro\\AddonServiceProvider"]}}}